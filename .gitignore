# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# Output
.output
.vercel
.netlify
.wrangler
.svelte-kit
build

# OS
.DS_Store
Thumbs.db

# testing
coverage

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Env
.env
.env.*
!.env.example
!.env.test

# turbo
.turbo

# Vite
vite.config.js.timestamp-*
vite.config.ts.timestamp-*
.idea
