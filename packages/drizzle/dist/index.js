import { timestamp, pgTable, boolean, uuid, date, foreignKey, text, unique, numeric, integer, jsonb, varchar } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';

const Roles = {
    TeamAdmin: 'team-admin'};
const timestamps = {
    updatedAt: timestamp('updated_at').defaultNow(),
    createdAt: timestamp('created_at').defaultNow(),
    deletedAt: timestamp('deleted_at')
};
const calendarBlock = pgTable('calendar_block', {
    id: uuid().defaultRandom().primaryKey().notNull(),
    startDate: date('start_date').notNull(),
    endDate: date('end_date').notNull(),
    startTime: timestamp('start_time', {
        mode: 'string'
    }).notNull(),
    endTime: timestamp('end_time', {
        mode: 'string'
    }).notNull(),
    teamId: uuid('team_id').notNull(),
    teamLocationId: uuid('team_location_id').notNull(),
    createdBy: uuid('created_by').notNull(),
    isActive: boolean('is_active').default(true),
    ...timestamps
}, (table)=>[
        foreignKey({
            columns: [
                table.createdBy
            ],
            foreignColumns: [
                user.id
            ],
            name: 'calendar_blocks_created_by_fkey'
        }).onUpdate('cascade').onDelete('restrict'),
        foreignKey({
            columns: [
                table.teamId
            ],
            foreignColumns: [
                team.id
            ],
            name: 'calendar_blocks_team_id_fkey'
        }).onUpdate('cascade').onDelete('restrict'),
        foreignKey({
            columns: [
                table.teamLocationId
            ],
            foreignColumns: [
                teamLocation.id
            ],
            name: 'calendar_blocks_team_location_id_fkey'
        }).onUpdate('cascade').onDelete('restrict')
    ]);
const member = pgTable('member', {
    id: uuid().defaultRandom().primaryKey().notNull(),
    teamId: uuid('team_id').notNull(),
    userId: uuid('user_id').notNull(),
    teamLocationId: uuid('team_location_id').notNull(),
    memberStatus: text('member_status').notNull(),
    ...timestamps
}, (table)=>[
        foreignKey({
            columns: [
                table.id
            ],
            foreignColumns: [
                user.id
            ],
            name: 'member_id_fkey'
        }).onUpdate('cascade').onDelete('cascade'),
        foreignKey({
            columns: [
                table.id
            ],
            foreignColumns: [
                team.id
            ],
            name: 'member_team_id_fkey'
        }).onUpdate('cascade').onDelete('restrict'),
        foreignKey({
            columns: [
                table.teamLocationId
            ],
            foreignColumns: [
                teamLocation.id
            ],
            name: 'member_team_location_id_fkey'
        }).onUpdate('cascade').onDelete('restrict'),
        unique('users_id_key').on(table.userId)
    ]);
const gender = pgTable('gender', {
    name: text().primaryKey().notNull()
});
const appointmentType = pgTable('appointment_type', {
    id: uuid().defaultRandom().primaryKey().notNull(),
    appointmentDetail: text('appointment_detail').notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    requiresUpfrontPayment: boolean('requires_upfront_payment').default(false).notNull(),
    isRepeatable: boolean('is_repeatable').default(false).notNull(),
    durationInMinutes: integer('duration_in_minutes').default(30).notNull(),
    upfrontPaymentAmount: numeric('upfront_payment_amount', {
        precision: 10,
        scale: 2
    }).notNull().default('0'),
    repeatType: text('repeat_type'),
    teamId: uuid('team_id').notNull(),
    createdBy: uuid('created_by').notNull(),
    timezoneId: uuid('timezone_id').notNull(),
    ...timestamps
}, (table)=>[
        foreignKey({
            columns: [
                table.teamId
            ],
            foreignColumns: [
                team.id
            ],
            name: 'appointment_type_team_id_fkey'
        }).onUpdate('cascade').onDelete('restrict'),
        foreignKey({
            columns: [
                table.timezoneId
            ],
            foreignColumns: [
                timeZone.id
            ],
            name: 'appointment_type_timezone_id_fkey'
        }).onUpdate('cascade').onDelete('restrict'),
        foreignKey({
            columns: [
                table.createdBy
            ],
            foreignColumns: [
                user.id
            ],
            name: 'appointment_type_created_by_fkey'
        }).onUpdate('cascade').onDelete('restrict')
    ]);
const storageBucket = pgTable('storage_bucket', {
    id: uuid().defaultRandom().primaryKey().notNull(),
    downloadExpiration: integer('download_expiration').default(30).notNull(),
    minUploadFileSize: integer('min_upload_file_size').default(1).notNull(),
    maxUploadFileSize: integer('max_upload_file_size').default(50000000).notNull(),
    cacheControl: text('cache_control').default('max-age=3600'),
    presignedUrlsEnabled: boolean('presigned_urls_enabled').default(true).notNull(),
    ...timestamps
});
const storageFile = pgTable('storage_file', {
    id: uuid().defaultRandom().primaryKey().notNull(),
    bucketId: uuid('bucket_id').notNull(),
    name: text(),
    size: integer(),
    mimeType: text('mime_type'),
    etag: text(),
    isUploaded: boolean('is_uploaded').default(false),
    uploadedByUserId: uuid('uploaded_by_user_id'),
    metadata: jsonb(),
    ...timestamps
}, (table)=>[
        foreignKey({
            columns: [
                table.bucketId
            ],
            foreignColumns: [
                storageBucket.id
            ],
            name: 'fk_bucket'
        }).onUpdate('cascade').onDelete('cascade')
    ]);
const userRole = pgTable('user_role', {
    id: uuid().defaultRandom().primaryKey().notNull(),
    userId: uuid('user_id').notNull(),
    role: text().notNull(),
    ...timestamps
}, (table)=>[
        foreignKey({
            columns: [
                table.userId
            ],
            foreignColumns: [
                user.id
            ],
            name: 'fk_user'
        }).onUpdate('cascade').onDelete('cascade'),
        foreignKey({
            columns: [
                table.role
            ],
            foreignColumns: [
                role.role
            ],
            name: 'fk_role'
        }).onUpdate('cascade').onDelete('restrict'),
        unique('user_role_user_id_role_key').on(table.userId, table.role)
    ]);
const role = pgTable('role', {
    role: text().primaryKey().notNull()
});
const user = pgTable('user', {
    id: uuid().defaultRandom().primaryKey().notNull(),
    authKey: text('auth_key').notNull(),
    lastSeen: timestamp('last_seen', {
        withTimezone: true,
        mode: 'string'
    }),
    disabled: boolean().default(false).notNull(),
    givenName: text('given_name').notNull(),
    familyName: text('family_name').notNull(),
    displayName: text('display_name').notNull(),
    avatarUrl: text('avatar_url').default('').notNull(),
    locale: varchar({
        length: 2
    }).notNull(),
    idDocumentNumber: text('id_document_number'),
    email: text('email'),
    phoneNumber: text('phone_number'),
    emailVerified: boolean('email_verified').default(false).notNull(),
    phoneNumberVerified: boolean('phone_number_verified').default(false).notNull(),
    defaultRole: text('default_role').default(Roles.TeamAdmin).notNull(),
    gender: text().default('Male'),
    roles: text('roles').default('[Roles.TeamAdmin]'),
    profilePhotoUrl: text('profile_photo_url'),
    ...timestamps
}, (table)=>[
        foreignKey({
            columns: [
                table.defaultRole
            ],
            foreignColumns: [
                role.role
            ],
            name: 'fk_default_role'
        }).onUpdate('cascade').onDelete('restrict'),
        foreignKey({
            columns: [
                table.gender
            ],
            foreignColumns: [
                gender.name
            ],
            name: 'fk_gender'
        }).onUpdate('cascade').onDelete('restrict'),
        unique('user_email_key').on(table.email),
        unique('user_phone_number_key').on(table.phoneNumber)
    ]);
const teamPaymentProvider = pgTable('team_payment_provider', {
    id: uuid().defaultRandom().primaryKey().notNull(),
    providerName: text('provider_name').notNull(),
    providerApiKey: text('provider_api_key').notNull(),
    providerApiSecret: text('provider_api_secret').notNull(),
    teamId: uuid('team_id').notNull(),
    createdBy: uuid('created_by').notNull(),
    lastUpdatedBy: uuid('last_updated_by'),
    providerWebhookUrl: text('provider_webhook_url'),
    providerClientId: text('provider_client_id'),
    providerApiUrl: text('provider_api_url'),
    isActive: boolean('is_active').default(true),
    ...timestamps
}, (table)=>[
        foreignKey({
            columns: [
                table.teamId
            ],
            foreignColumns: [
                team.id
            ],
            name: 'team_payment_provider_team_id_fkey'
        }).onUpdate('cascade').onDelete('restrict'),
        unique('team_payment_provider_team_id_key').on(table.teamId)
    ]);
const team = pgTable('team', {
    id: uuid().defaultRandom().primaryKey().notNull(),
    teamName: text('team_name').notNull(),
    currentPlan: text('current_plan').notNull(),
    businessEmail: text('business_email').notNull(),
    phoneNumber: text('phone_number'),
    businessRegNo: text('business_reg_no'),
    registrationDate: date('registration_date'),
    isActive: boolean('is_active').default(true).notNull(),
    createdAt: timestamp('created_at', {
        mode: 'string'
    }).defaultNow(),
    updatedAt: timestamp('updated_at', {
        mode: 'string'
    }).defaultNow()
});
const timeZone = pgTable('time_zone', {
    id: uuid().defaultRandom().primaryKey().notNull(),
    timeZoneName: text('time_zone_name').notNull()
});
const workdaySetting = pgTable('workday_setting', {
    id: uuid().defaultRandom().primaryKey().notNull(),
    weekday: text('weekday').notNull(),
    startTime: numeric('start_time').notNull(),
    endTime: numeric('end_time').notNull(),
    isDayActive: boolean('is_day_active').notNull(),
    createdAt: timestamp('created_at', {
        mode: 'string'
    }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', {
        mode: 'string'
    }).defaultNow().notNull(),
    teamId: uuid('team_id').notNull(),
    timezoneId: uuid('timezone_id').notNull(),
    lastUpdatedBy: uuid('last_updated_by')
}, (table)=>[
        foreignKey({
            columns: [
                table.teamId
            ],
            foreignColumns: [
                team.id
            ],
            name: 'workday_setting_team_id_fkey'
        }).onUpdate('cascade').onDelete('restrict'),
        unique('workday_setting_id_key').on(table.id),
        unique('workday_setting_team_id_key').on(table.teamId)
    ]);
const teamLocation = pgTable('team_location', {
    id: uuid().defaultRandom().primaryKey().notNull(),
    isDefault: boolean('is_default').default(false),
    locationName: text('location_name').notNull(),
    locationAddress: text('location_address'),
    locationEmail: text('location_email'),
    locationPhoneNumber: text('location_phone_number'),
    isActive: boolean('is_active').default(true),
    teamId: uuid('team_id').notNull(),
    teamLocationAdmin: uuid('team_location_admin').notNull(),
    createdBy: uuid('created_by').notNull(),
    ...timestamps
}, (table)=>[
        foreignKey({
            columns: [
                table.teamLocationAdmin
            ],
            foreignColumns: [
                user.id
            ],
            name: 'team_location_team_location_admin_fkey'
        }).onUpdate('cascade').onDelete('restrict'),
        foreignKey({
            columns: [
                table.teamId
            ],
            foreignColumns: [
                team.id
            ],
            name: 'team_location_team_id_fkey'
        }).onUpdate('cascade').onDelete('restrict'),
        foreignKey({
            columns: [
                table.createdBy
            ],
            foreignColumns: [
                user.id
            ],
            name: 'team_location_created_by_fkey'
        }).onUpdate('cascade').onDelete('restrict')
    ]);
const appointment = pgTable('appointment', {
    id: uuid().defaultRandom().primaryKey().notNull(),
    customerName: text('customer_name').notNull(),
    customerEmail: text('customer_email'),
    customerPhoneNumber: text('customer_phone_number'),
    appointmentDate: date('appointment_date').notNull(),
    startTime: timestamp('start_time', {
        mode: 'string'
    }).notNull(),
    endTime: timestamp('end_time', {
        mode: 'string'
    }).notNull(),
    depositRequired: boolean('deposit_required').default(false),
    depositPaid: boolean('deposit_paid').default(false),
    appointmentStatus: text('appointment_status').notNull(),
    teamId: uuid('team_id').notNull(),
    teamLocationId: uuid('team_location_id').notNull(),
    appointmentTypeId: uuid('appointment_type_id').notNull(),
    updatedBy: uuid('updated_by'),
    attendedBy: uuid('attended_by'),
    appointmentNotes: text('appointment_notes'),
    ...timestamps
}, (table)=>[
        foreignKey({
            columns: [
                table.teamId
            ],
            foreignColumns: [
                team.id
            ],
            name: 'appointment_team_id_fkey'
        }).onUpdate('cascade').onDelete('restrict'),
        foreignKey({
            columns: [
                table.appointmentTypeId
            ],
            foreignColumns: [
                appointmentType.id
            ],
            name: 'appointment_appointment_type_id_fkey'
        }).onUpdate('cascade').onDelete('restrict'),
        foreignKey({
            columns: [
                table.teamLocationId
            ],
            foreignColumns: [
                teamLocation.id
            ],
            name: 'appointment_team_location_id_fkey'
        }).onUpdate('cascade').onDelete('restrict'),
        unique('appointment_appointment_date_customer_name_customer_email_key').on(table.customerName, table.customerEmail, table.appointmentDate)
    ]);
const teamBankDetail = pgTable('team_bank_detail', {
    id: uuid().defaultRandom().primaryKey().notNull(),
    teamId: uuid('team_id').notNull(),
    bankName: text('bank_name').notNull(),
    accountNumber: text('account_number').notNull(),
    createdAt: timestamp('created_at', {
        mode: 'string'
    }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', {
        mode: 'string'
    }).defaultNow().notNull(),
    swiftCode: text('swift_code'),
    branchCode: text('branch_code'),
    accountName: text('account_name')
}, (table)=>[
        foreignKey({
            columns: [
                table.teamId
            ],
            foreignColumns: [
                team.id
            ],
            name: 'team_bank_detail_team_id_fkey'
        }).onUpdate('cascade').onDelete('restrict'),
        unique('team_bank_detail_team_id_key').on(table.teamId)
    ]);
const memberInvite = pgTable('member_invite', {
    id: uuid().defaultRandom().primaryKey().notNull(),
    invitedByEmail: text('invited_by_email').notNull(),
    invitedBy: uuid('invited_by').notNull(),
    inviteeEmail: text('invitee_email').notNull(),
    teamId: uuid('team_id').notNull(),
    teamLocationId: uuid('team_location_id').notNull(),
    isPending: boolean('is_pending').default(true),
    givenName: text('given_name').notNull(),
    familyName: text('family_name').notNull(),
    gender: text(),
    role: text().notNull()
}, (table)=>[
        foreignKey({
            columns: [
                table.teamLocationId
            ],
            foreignColumns: [
                teamLocation.id
            ],
            name: 'member_invite_team_location_id_fkey'
        }).onUpdate('cascade').onDelete('restrict'),
        foreignKey({
            columns: [
                table.invitedBy
            ],
            foreignColumns: [
                user.id
            ],
            name: 'member_invite_invited_by_fkey2'
        }).onUpdate('cascade').onDelete('restrict'),
        foreignKey({
            columns: [
                table.teamId
            ],
            foreignColumns: [
                team.id
            ],
            name: 'member_invite_team_id_fkey'
        }).onUpdate('cascade').onDelete('restrict'),
        foreignKey({
            columns: [
                table.gender
            ],
            foreignColumns: [
                gender.name
            ],
            name: 'member_invite_gender_fkey'
        }).onUpdate('cascade').onDelete('restrict'),
        foreignKey({
            columns: [
                table.role
            ],
            foreignColumns: [
                role.role
            ],
            name: 'member_invite_role_fkey'
        }).onUpdate('cascade').onDelete('restrict')
    ]);
//******************** Relations ***********************//
const calendarBlockRelations = relations(calendarBlock, ({ one })=>({
        user: one(user, {
            fields: [
                calendarBlock.createdBy
            ],
            references: [
                user.id
            ]
        }),
        team: one(team, {
            fields: [
                calendarBlock.teamId
            ],
            references: [
                team.id
            ]
        }),
        teamLocation: one(teamLocation, {
            fields: [
                calendarBlock.teamLocationId
            ],
            references: [
                teamLocation.id
            ]
        })
    }));
const userRelations = relations(user, ({ one, many })=>({
        calendarBlocks: many(calendarBlock),
        member: one(member),
        appointmentTypes: many(appointmentType),
        userRoles: many(userRole),
        role: one(role, {
            fields: [
                user.defaultRole
            ],
            references: [
                role.role
            ]
        }),
        teamLocation_admin: many(teamLocation, {
            relationName: 'teamLocation_admin_user_id'
        }),
        teamLocation_createdBy: many(teamLocation, {
            relationName: 'teamLocation_createdBy_user_id'
        }),
        memberInvites: many(memberInvite),
        attendedBy: one(appointment, {
            fields: [
                user.id
            ],
            references: [
                appointment.attendedBy
            ]
        }),
        updatedBy: one(appointment, {
            fields: [
                user.id
            ],
            references: [
                appointment.updatedBy
            ]
        })
    }));
const teamRelations = relations(team, ({ many, one })=>({
        calendarBlocks: many(calendarBlock),
        members: many(member),
        appointmentTypes: many(appointmentType),
        teamPaymentProvider: one(teamPaymentProvider),
        workdaySetting: one(workdaySetting),
        teamLocations: many(teamLocation),
        appointments: many(appointment),
        teamBankDetail: one(teamBankDetail),
        memberInvites: many(memberInvite)
    }));
const teamLocationRelations = relations(teamLocation, ({ one, many })=>({
        calendarBlocks: many(calendarBlock),
        members: many(member),
        teamLocationAdmin: one(user, {
            fields: [
                teamLocation.teamLocationAdmin
            ],
            references: [
                user.id
            ],
            relationName: 'teamLocation_admin_user_id'
        }),
        team: one(team, {
            fields: [
                teamLocation.teamId
            ],
            references: [
                team.id
            ]
        }),
        user_createdBy: one(user, {
            fields: [
                teamLocation.createdBy
            ],
            references: [
                user.id
            ],
            relationName: 'teamLocation_createdBy_user_id'
        }),
        appointments: many(appointment),
        memberInvites: many(memberInvite)
    }));
const memberRelations = relations(member, ({ one })=>({
        user: one(user, {
            fields: [
                member.userId
            ],
            references: [
                user.id
            ]
        }),
        team: one(team, {
            fields: [
                member.teamId
            ],
            references: [
                team.id
            ]
        }),
        teamLocation: one(teamLocation, {
            fields: [
                member.teamLocationId
            ],
            references: [
                teamLocation.id
            ]
        })
    }));
const genderRelations = relations(gender, ({ many })=>({
        users: many(user),
        memberInvites: many(memberInvite)
    }));
const appointmentTypesRelations = relations(appointmentType, ({ one, many })=>({
        team: one(team, {
            fields: [
                appointmentType.teamId
            ],
            references: [
                team.id
            ]
        }),
        timeZone: one(timeZone, {
            fields: [
                appointmentType.timezoneId
            ],
            references: [
                timeZone.id
            ]
        }),
        user: one(user, {
            fields: [
                appointmentType.createdBy
            ],
            references: [
                user.id
            ]
        }),
        appointments: many(appointment)
    }));
const timeZonesRelations = relations(timeZone, ({ many })=>({
        appointmentTypes: many(appointmentType)
    }));
const storageFileRelations = relations(storageFile, ({ one })=>({
        storageBucket: one(storageBucket, {
            fields: [
                storageFile.bucketId
            ],
            references: [
                storageBucket.id
            ]
        })
    }));
const storageBucketRelations = relations(storageBucket, ({ many })=>({
        storageFiles: many(storageFile)
    }));
const userRoleRelations = relations(userRole, ({ one })=>({
        user: one(user, {
            fields: [
                userRole.userId
            ],
            references: [
                user.id
            ]
        }),
        role: one(role, {
            fields: [
                userRole.role
            ],
            references: [
                role.role
            ]
        })
    }));
const roleRelations = relations(role, ({ many })=>({
        userRoles: many(userRole),
        users: many(user),
        memberInvites: many(memberInvite)
    }));
const teamPaymentProviderRelations = relations(teamPaymentProvider, ({ one })=>({
        team: one(team, {
            fields: [
                teamPaymentProvider.teamId
            ],
            references: [
                team.id
            ]
        })
    }));
const workdaySettingRelations = relations(workdaySetting, ({ one })=>({
        team: one(team, {
            fields: [
                workdaySetting.teamId
            ],
            references: [
                team.id
            ]
        })
    }));
const appointmentRelations = relations(appointment, ({ one })=>({
        team: one(team, {
            fields: [
                appointment.teamId
            ],
            references: [
                team.id
            ]
        }),
        appointmentType: one(appointmentType, {
            fields: [
                appointment.appointmentTypeId
            ],
            references: [
                appointmentType.id
            ]
        }),
        teamLocation: one(teamLocation, {
            fields: [
                appointment.teamLocationId
            ],
            references: [
                teamLocation.id
            ]
        }),
        user: one(user, {
            fields: [
                appointment.updatedBy
            ],
            references: [
                user.id
            ]
        }),
        attendedBy: one(user, {
            fields: [
                appointment.attendedBy
            ],
            references: [
                user.id
            ]
        })
    }));
const teamBankDetailRelations = relations(teamBankDetail, ({ one })=>({
        team: one(team, {
            fields: [
                teamBankDetail.teamId
            ],
            references: [
                team.id
            ]
        })
    }));
const memberInviteRelations = relations(memberInvite, ({ one })=>({
        teamLocation: one(teamLocation, {
            fields: [
                memberInvite.teamLocationId
            ],
            references: [
                teamLocation.id
            ]
        }),
        user: one(user, {
            fields: [
                memberInvite.invitedBy
            ],
            references: [
                user.id
            ]
        }),
        team: one(team, {
            fields: [
                memberInvite.teamId
            ],
            references: [
                team.id
            ]
        }),
        gender: one(gender, {
            fields: [
                memberInvite.gender
            ],
            references: [
                gender.name
            ]
        }),
        role: one(role, {
            fields: [
                memberInvite.role
            ],
            references: [
                role.role
            ]
        })
    }));

export { appointment, appointmentRelations, appointmentType, appointmentTypesRelations, calendarBlock, calendarBlockRelations, gender, genderRelations, member, memberInvite, memberInviteRelations, memberRelations, role, roleRelations, storageBucket, storageBucketRelations, storageFile, storageFileRelations, team, teamBankDetail, teamBankDetailRelations, teamLocation, teamLocationRelations, teamPaymentProvider, teamPaymentProviderRelations, teamRelations, timeZone, timeZonesRelations, user, userRelations, userRole, userRoleRelations, workdaySetting, workdaySettingRelations };
