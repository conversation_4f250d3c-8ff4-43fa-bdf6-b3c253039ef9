CREATE TABLE "appointment" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"customer_name" text NOT NULL,
	"customer_email" text,
	"customer_phone_number" text,
	"appointment_date" date NOT NULL,
	"start_time" timestamp NOT NULL,
	"end_time" timestamp NOT NULL,
	"deposit_required" boolean DEFAULT false,
	"deposit_paid" boolean DEFAULT false,
	"appointment_status" text NOT NULL,
	"team_id" uuid NOT NULL,
	"team_location_id" uuid NOT NULL,
	"appointment_type_id" uuid NOT NULL,
	"updated_by" uuid,
	"attended_by" uuid,
	"appointment_notes" text,
	"updated_at" timestamp DEFAULT now(),
	"created_at" timestamp DEFAULT now(),
	"deleted_at" timestamp,
	CONSTRAINT "appointment_appointment_date_customer_name_customer_email_key" UNIQUE("customer_name","customer_email","appointment_date")
);
--> statement-breakpoint
CREATE TABLE "appointment_type" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"appointment_detail" text NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"requires_upfront_payment" boolean DEFAULT false NOT NULL,
	"is_repeatable" boolean DEFAULT false NOT NULL,
	"duration_in_minutes" integer DEFAULT 30 NOT NULL,
	"upfront_payment_amount" numeric(10, 2) DEFAULT '0' NOT NULL,
	"repeat_type" text,
	"team_id" uuid NOT NULL,
	"created_by" uuid NOT NULL,
	"timezone_id" uuid NOT NULL,
	"updated_at" timestamp DEFAULT now(),
	"created_at" timestamp DEFAULT now(),
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "calendar_block" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"start_date" date NOT NULL,
	"end_date" date NOT NULL,
	"start_time" timestamp NOT NULL,
	"end_time" timestamp NOT NULL,
	"team_id" uuid NOT NULL,
	"team_location_id" uuid NOT NULL,
	"created_by" uuid NOT NULL,
	"is_active" boolean DEFAULT true,
	"updated_at" timestamp DEFAULT now(),
	"created_at" timestamp DEFAULT now(),
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "gender" (
	"name" text PRIMARY KEY NOT NULL
);
--> statement-breakpoint
CREATE TABLE "member" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"team_id" uuid NOT NULL,
	"user_id" uuid NOT NULL,
	"team_location_id" uuid NOT NULL,
	"member_status" text NOT NULL,
	"updated_at" timestamp DEFAULT now(),
	"created_at" timestamp DEFAULT now(),
	"deleted_at" timestamp,
	CONSTRAINT "users_id_key" UNIQUE("user_id")
);
--> statement-breakpoint
CREATE TABLE "member_invite" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"invited_by_email" text NOT NULL,
	"invited_by" uuid NOT NULL,
	"invitee_email" text NOT NULL,
	"team_id" uuid NOT NULL,
	"team_location_id" uuid NOT NULL,
	"is_pending" boolean DEFAULT true,
	"given_name" text NOT NULL,
	"family_name" text NOT NULL,
	"gender" text,
	"role" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "role" (
	"role" text PRIMARY KEY NOT NULL
);
--> statement-breakpoint
CREATE TABLE "storage_bucket" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"download_expiration" integer DEFAULT 30 NOT NULL,
	"min_upload_file_size" integer DEFAULT 1 NOT NULL,
	"max_upload_file_size" integer DEFAULT 50000000 NOT NULL,
	"cache_control" text DEFAULT 'max-age=3600',
	"presigned_urls_enabled" boolean DEFAULT true NOT NULL,
	"updated_at" timestamp DEFAULT now(),
	"created_at" timestamp DEFAULT now(),
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "storage_file" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"bucket_id" uuid NOT NULL,
	"name" text,
	"size" integer,
	"mime_type" text,
	"etag" text,
	"is_uploaded" boolean DEFAULT false,
	"uploaded_by_user_id" uuid,
	"metadata" jsonb,
	"updated_at" timestamp DEFAULT now(),
	"created_at" timestamp DEFAULT now(),
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "team" (
	"teamId" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"team_name" text NOT NULL,
	"current_plan" text NOT NULL,
	"business_email" text NOT NULL,
	"phone_number" text,
	"business_reg_no" text,
	"registration_date" date,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "team_bank_detail" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"team_id" uuid NOT NULL,
	"bank_name" text NOT NULL,
	"account_number" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"swift_code" text,
	"branch_code" text,
	"account_name" text,
	CONSTRAINT "team_bank_detail_team_id_key" UNIQUE("team_id")
);
--> statement-breakpoint
CREATE TABLE "team_location" (
	"teamLocationId" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"is_default" boolean DEFAULT false,
	"location_name" text NOT NULL,
	"location_address" text,
	"location_email" text,
	"location_phone_number" text,
	"is_active" boolean DEFAULT true,
	"team_id" uuid NOT NULL,
	"team_location_admin" uuid NOT NULL,
	"created_by" uuid NOT NULL,
	"updated_at" timestamp DEFAULT now(),
	"created_at" timestamp DEFAULT now(),
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "team_payment_provider" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"provider_name" text NOT NULL,
	"provider_api_key" text NOT NULL,
	"provider_api_secret" text NOT NULL,
	"team_id" uuid NOT NULL,
	"created_by" uuid NOT NULL,
	"last_updated_by" uuid,
	"provider_webhook_url" text,
	"provider_client_id" text,
	"provider_api_url" text,
	"is_active" boolean DEFAULT true,
	"updated_at" timestamp DEFAULT now(),
	"created_at" timestamp DEFAULT now(),
	"deleted_at" timestamp,
	CONSTRAINT "team_payment_provider_team_id_key" UNIQUE("team_id")
);
--> statement-breakpoint
CREATE TABLE "time_zone" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"time_zone_name" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"auth_key" text NOT NULL,
	"last_seen" timestamp with time zone,
	"disabled" boolean DEFAULT false NOT NULL,
	"given_name" text NOT NULL,
	"family_name" text NOT NULL,
	"display_name" text NOT NULL,
	"avatar_url" text DEFAULT '' NOT NULL,
	"locale" varchar(2) NOT NULL,
	"id_document_number" text,
	"email" text,
	"phone_number" text,
	"email_verified" boolean DEFAULT false NOT NULL,
	"phone_number_verified" boolean DEFAULT false NOT NULL,
	"default_role" text DEFAULT 'team-admin' NOT NULL,
	"gender" text DEFAULT 'Male',
	"roles" text DEFAULT '[Roles.TeamAdmin]',
	"profile_photo_url" text,
	"updated_at" timestamp DEFAULT now(),
	"created_at" timestamp DEFAULT now(),
	"deleted_at" timestamp,
	CONSTRAINT "user_email_key" UNIQUE("email"),
	CONSTRAINT "user_phone_number_key" UNIQUE("phone_number")
);
--> statement-breakpoint
CREATE TABLE "user_role" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"role" text NOT NULL,
	"updated_at" timestamp DEFAULT now(),
	"created_at" timestamp DEFAULT now(),
	"deleted_at" timestamp,
	CONSTRAINT "user_role_user_id_role_key" UNIQUE("user_id","role")
);
--> statement-breakpoint
CREATE TABLE "workday_setting" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"weekday" text NOT NULL,
	"start_time" numeric NOT NULL,
	"end_time" numeric NOT NULL,
	"is_day_active" boolean NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"team_id" uuid NOT NULL,
	"timezone_id" uuid NOT NULL,
	"last_updated_by" uuid,
	CONSTRAINT "workday_setting_id_key" UNIQUE("id"),
	CONSTRAINT "workday_setting_team_id_key" UNIQUE("team_id")
);
--> statement-breakpoint
ALTER TABLE "appointment" ADD CONSTRAINT "appointment_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."team"("teamId") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "appointment" ADD CONSTRAINT "appointment_appointment_type_id_fkey" FOREIGN KEY ("appointment_type_id") REFERENCES "public"."appointment_type"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "appointment" ADD CONSTRAINT "appointment_team_location_id_fkey" FOREIGN KEY ("team_location_id") REFERENCES "public"."team_location"("teamLocationId") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "appointment_type" ADD CONSTRAINT "appointment_type_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."team"("teamId") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "appointment_type" ADD CONSTRAINT "appointment_type_timezone_id_fkey" FOREIGN KEY ("timezone_id") REFERENCES "public"."time_zone"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "appointment_type" ADD CONSTRAINT "appointment_type_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "calendar_block" ADD CONSTRAINT "calendar_blocks_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "calendar_block" ADD CONSTRAINT "calendar_blocks_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."team"("teamId") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "calendar_block" ADD CONSTRAINT "calendar_blocks_team_location_id_fkey" FOREIGN KEY ("team_location_id") REFERENCES "public"."team_location"("teamLocationId") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "member" ADD CONSTRAINT "member_id_fkey" FOREIGN KEY ("id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "member" ADD CONSTRAINT "member_team_id_fkey" FOREIGN KEY ("id") REFERENCES "public"."team"("teamId") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "member" ADD CONSTRAINT "member_team_location_id_fkey" FOREIGN KEY ("team_location_id") REFERENCES "public"."team_location"("teamLocationId") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "member_invite" ADD CONSTRAINT "member_invite_team_location_id_fkey" FOREIGN KEY ("team_location_id") REFERENCES "public"."team_location"("teamLocationId") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "member_invite" ADD CONSTRAINT "member_invite_invited_by_fkey2" FOREIGN KEY ("invited_by") REFERENCES "public"."user"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "member_invite" ADD CONSTRAINT "member_invite_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."team"("teamId") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "member_invite" ADD CONSTRAINT "member_invite_gender_fkey" FOREIGN KEY ("gender") REFERENCES "public"."gender"("name") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "member_invite" ADD CONSTRAINT "member_invite_role_fkey" FOREIGN KEY ("role") REFERENCES "public"."role"("role") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "storage_file" ADD CONSTRAINT "fk_bucket" FOREIGN KEY ("bucket_id") REFERENCES "public"."storage_bucket"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "team_bank_detail" ADD CONSTRAINT "team_bank_detail_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."team"("teamId") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "team_location" ADD CONSTRAINT "team_location_team_location_admin_fkey" FOREIGN KEY ("team_location_admin") REFERENCES "public"."user"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "team_location" ADD CONSTRAINT "team_location_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."team"("teamId") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "team_location" ADD CONSTRAINT "team_location_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "team_payment_provider" ADD CONSTRAINT "team_payment_provider_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."team"("teamId") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "user" ADD CONSTRAINT "fk_default_role" FOREIGN KEY ("default_role") REFERENCES "public"."role"("role") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "user" ADD CONSTRAINT "fk_gender" FOREIGN KEY ("gender") REFERENCES "public"."gender"("name") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "user_role" ADD CONSTRAINT "fk_user" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "user_role" ADD CONSTRAINT "fk_role" FOREIGN KEY ("role") REFERENCES "public"."role"("role") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "workday_setting" ADD CONSTRAINT "workday_setting_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."team"("teamId") ON DELETE restrict ON UPDATE cascade;