{"id": "179cb65e-aca8-4de2-89c6-0276ac905281", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.appointment": {"name": "appointment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "customer_name": {"name": "customer_name", "type": "text", "primaryKey": false, "notNull": true}, "customer_email": {"name": "customer_email", "type": "text", "primaryKey": false, "notNull": false}, "customer_phone_number": {"name": "customer_phone_number", "type": "text", "primaryKey": false, "notNull": false}, "appointment_date": {"name": "appointment_date", "type": "date", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "timestamp", "primaryKey": false, "notNull": true}, "deposit_required": {"name": "deposit_required", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "deposit_paid": {"name": "deposit_paid", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "appointment_status": {"name": "appointment_status", "type": "text", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "team_location_id": {"name": "team_location_id", "type": "uuid", "primaryKey": false, "notNull": true}, "appointment_type_id": {"name": "appointment_type_id", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": false}, "attended_by": {"name": "attended_by", "type": "uuid", "primaryKey": false, "notNull": false}, "appointment_notes": {"name": "appointment_notes", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"appointment_team_id_fkey": {"name": "appointment_team_id_fkey", "tableFrom": "appointment", "tableTo": "team", "columnsFrom": ["team_id"], "columnsTo": ["teamId"], "onDelete": "restrict", "onUpdate": "cascade"}, "appointment_appointment_type_id_fkey": {"name": "appointment_appointment_type_id_fkey", "tableFrom": "appointment", "tableTo": "appointment_type", "columnsFrom": ["appointment_type_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "appointment_team_location_id_fkey": {"name": "appointment_team_location_id_fkey", "tableFrom": "appointment", "tableTo": "team_location", "columnsFrom": ["team_location_id"], "columnsTo": ["teamLocationId"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"appointment_appointment_date_customer_name_customer_email_key": {"name": "appointment_appointment_date_customer_name_customer_email_key", "nullsNotDistinct": false, "columns": ["customer_name", "customer_email", "appointment_date"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.appointment_type": {"name": "appointment_type", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "appointment_detail": {"name": "appointment_detail", "type": "text", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "requires_upfront_payment": {"name": "requires_upfront_payment", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_repeatable": {"name": "is_repeatable", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "duration_in_minutes": {"name": "duration_in_minutes", "type": "integer", "primaryKey": false, "notNull": true, "default": 30}, "upfront_payment_amount": {"name": "upfront_payment_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0'"}, "repeat_type": {"name": "repeat_type", "type": "text", "primaryKey": false, "notNull": false}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "timezone_id": {"name": "timezone_id", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"appointment_type_team_id_fkey": {"name": "appointment_type_team_id_fkey", "tableFrom": "appointment_type", "tableTo": "team", "columnsFrom": ["team_id"], "columnsTo": ["teamId"], "onDelete": "restrict", "onUpdate": "cascade"}, "appointment_type_timezone_id_fkey": {"name": "appointment_type_timezone_id_fkey", "tableFrom": "appointment_type", "tableTo": "time_zone", "columnsFrom": ["timezone_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "appointment_type_created_by_fkey": {"name": "appointment_type_created_by_fkey", "tableFrom": "appointment_type", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.calendar_block": {"name": "calendar_block", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "timestamp", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "team_location_id": {"name": "team_location_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"calendar_blocks_created_by_fkey": {"name": "calendar_blocks_created_by_fkey", "tableFrom": "calendar_block", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "calendar_blocks_team_id_fkey": {"name": "calendar_blocks_team_id_fkey", "tableFrom": "calendar_block", "tableTo": "team", "columnsFrom": ["team_id"], "columnsTo": ["teamId"], "onDelete": "restrict", "onUpdate": "cascade"}, "calendar_blocks_team_location_id_fkey": {"name": "calendar_blocks_team_location_id_fkey", "tableFrom": "calendar_block", "tableTo": "team_location", "columnsFrom": ["team_location_id"], "columnsTo": ["teamLocationId"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.gender": {"name": "gender", "schema": "", "columns": {"name": {"name": "name", "type": "text", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.member": {"name": "member", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "team_location_id": {"name": "team_location_id", "type": "uuid", "primaryKey": false, "notNull": true}, "member_status": {"name": "member_status", "type": "text", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"member_id_fkey": {"name": "member_id_fkey", "tableFrom": "member", "tableTo": "user", "columnsFrom": ["id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "member_team_id_fkey": {"name": "member_team_id_fkey", "tableFrom": "member", "tableTo": "team", "columnsFrom": ["id"], "columnsTo": ["teamId"], "onDelete": "restrict", "onUpdate": "cascade"}, "member_team_location_id_fkey": {"name": "member_team_location_id_fkey", "tableFrom": "member", "tableTo": "team_location", "columnsFrom": ["team_location_id"], "columnsTo": ["teamLocationId"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_id_key": {"name": "users_id_key", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.member_invite": {"name": "member_invite", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "invited_by_email": {"name": "invited_by_email", "type": "text", "primaryKey": false, "notNull": true}, "invited_by": {"name": "invited_by", "type": "uuid", "primaryKey": false, "notNull": true}, "invitee_email": {"name": "invitee_email", "type": "text", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "team_location_id": {"name": "team_location_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_pending": {"name": "is_pending", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "given_name": {"name": "given_name", "type": "text", "primaryKey": false, "notNull": true}, "family_name": {"name": "family_name", "type": "text", "primaryKey": false, "notNull": true}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"member_invite_team_location_id_fkey": {"name": "member_invite_team_location_id_fkey", "tableFrom": "member_invite", "tableTo": "team_location", "columnsFrom": ["team_location_id"], "columnsTo": ["teamLocationId"], "onDelete": "restrict", "onUpdate": "cascade"}, "member_invite_invited_by_fkey2": {"name": "member_invite_invited_by_fkey2", "tableFrom": "member_invite", "tableTo": "user", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "member_invite_team_id_fkey": {"name": "member_invite_team_id_fkey", "tableFrom": "member_invite", "tableTo": "team", "columnsFrom": ["team_id"], "columnsTo": ["teamId"], "onDelete": "restrict", "onUpdate": "cascade"}, "member_invite_gender_fkey": {"name": "member_invite_gender_fkey", "tableFrom": "member_invite", "tableTo": "gender", "columnsFrom": ["gender"], "columnsTo": ["name"], "onDelete": "restrict", "onUpdate": "cascade"}, "member_invite_role_fkey": {"name": "member_invite_role_fkey", "tableFrom": "member_invite", "tableTo": "role", "columnsFrom": ["role"], "columnsTo": ["role"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.role": {"name": "role", "schema": "", "columns": {"role": {"name": "role", "type": "text", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.storage_bucket": {"name": "storage_bucket", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "download_expiration": {"name": "download_expiration", "type": "integer", "primaryKey": false, "notNull": true, "default": 30}, "min_upload_file_size": {"name": "min_upload_file_size", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "max_upload_file_size": {"name": "max_upload_file_size", "type": "integer", "primaryKey": false, "notNull": true, "default": 50000000}, "cache_control": {"name": "cache_control", "type": "text", "primaryKey": false, "notNull": false, "default": "'max-age=3600'"}, "presigned_urls_enabled": {"name": "presigned_urls_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.storage_file": {"name": "storage_file", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "bucket_id": {"name": "bucket_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "size": {"name": "size", "type": "integer", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": false}, "etag": {"name": "etag", "type": "text", "primaryKey": false, "notNull": false}, "is_uploaded": {"name": "is_uploaded", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "uploaded_by_user_id": {"name": "uploaded_by_user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"fk_bucket": {"name": "fk_bucket", "tableFrom": "storage_file", "tableTo": "storage_bucket", "columnsFrom": ["bucket_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.team": {"name": "team", "schema": "", "columns": {"teamId": {"name": "teamId", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "team_name": {"name": "team_name", "type": "text", "primaryKey": false, "notNull": true}, "current_plan": {"name": "current_plan", "type": "text", "primaryKey": false, "notNull": true}, "business_email": {"name": "business_email", "type": "text", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "text", "primaryKey": false, "notNull": false}, "business_reg_no": {"name": "business_reg_no", "type": "text", "primaryKey": false, "notNull": false}, "registration_date": {"name": "registration_date", "type": "date", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.team_bank_detail": {"name": "team_bank_detail", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "bank_name": {"name": "bank_name", "type": "text", "primaryKey": false, "notNull": true}, "account_number": {"name": "account_number", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "swift_code": {"name": "swift_code", "type": "text", "primaryKey": false, "notNull": false}, "branch_code": {"name": "branch_code", "type": "text", "primaryKey": false, "notNull": false}, "account_name": {"name": "account_name", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"team_bank_detail_team_id_fkey": {"name": "team_bank_detail_team_id_fkey", "tableFrom": "team_bank_detail", "tableTo": "team", "columnsFrom": ["team_id"], "columnsTo": ["teamId"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"team_bank_detail_team_id_key": {"name": "team_bank_detail_team_id_key", "nullsNotDistinct": false, "columns": ["team_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.team_location": {"name": "team_location", "schema": "", "columns": {"teamLocationId": {"name": "teamLocationId", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "location_name": {"name": "location_name", "type": "text", "primaryKey": false, "notNull": true}, "location_address": {"name": "location_address", "type": "text", "primaryKey": false, "notNull": false}, "location_email": {"name": "location_email", "type": "text", "primaryKey": false, "notNull": false}, "location_phone_number": {"name": "location_phone_number", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "team_location_admin": {"name": "team_location_admin", "type": "uuid", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"team_location_team_location_admin_fkey": {"name": "team_location_team_location_admin_fkey", "tableFrom": "team_location", "tableTo": "user", "columnsFrom": ["team_location_admin"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "team_location_team_id_fkey": {"name": "team_location_team_id_fkey", "tableFrom": "team_location", "tableTo": "team", "columnsFrom": ["team_id"], "columnsTo": ["teamId"], "onDelete": "restrict", "onUpdate": "cascade"}, "team_location_created_by_fkey": {"name": "team_location_created_by_fkey", "tableFrom": "team_location", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.team_payment_provider": {"name": "team_payment_provider", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "provider_name": {"name": "provider_name", "type": "text", "primaryKey": false, "notNull": true}, "provider_api_key": {"name": "provider_api_key", "type": "text", "primaryKey": false, "notNull": true}, "provider_api_secret": {"name": "provider_api_secret", "type": "text", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "last_updated_by": {"name": "last_updated_by", "type": "uuid", "primaryKey": false, "notNull": false}, "provider_webhook_url": {"name": "provider_webhook_url", "type": "text", "primaryKey": false, "notNull": false}, "provider_client_id": {"name": "provider_client_id", "type": "text", "primaryKey": false, "notNull": false}, "provider_api_url": {"name": "provider_api_url", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"team_payment_provider_team_id_fkey": {"name": "team_payment_provider_team_id_fkey", "tableFrom": "team_payment_provider", "tableTo": "team", "columnsFrom": ["team_id"], "columnsTo": ["teamId"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"team_payment_provider_team_id_key": {"name": "team_payment_provider_team_id_key", "nullsNotDistinct": false, "columns": ["team_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.time_zone": {"name": "time_zone", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "time_zone_name": {"name": "time_zone_name", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "auth_key": {"name": "auth_key", "type": "text", "primaryKey": false, "notNull": true}, "last_seen": {"name": "last_seen", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "given_name": {"name": "given_name", "type": "text", "primaryKey": false, "notNull": true}, "family_name": {"name": "family_name", "type": "text", "primaryKey": false, "notNull": true}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": true}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}, "locale": {"name": "locale", "type": "<PERSON><PERSON><PERSON>(2)", "primaryKey": false, "notNull": true}, "id_document_number": {"name": "id_document_number", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "phone_number": {"name": "phone_number", "type": "text", "primaryKey": false, "notNull": false}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "phone_number_verified": {"name": "phone_number_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "default_role": {"name": "default_role", "type": "text", "primaryKey": false, "notNull": true, "default": "'team-admin'"}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false, "default": "'Male'"}, "roles": {"name": "roles", "type": "text", "primaryKey": false, "notNull": false, "default": "'[Roles.TeamAdmin]'"}, "profile_photo_url": {"name": "profile_photo_url", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"fk_default_role": {"name": "fk_default_role", "tableFrom": "user", "tableTo": "role", "columnsFrom": ["default_role"], "columnsTo": ["role"], "onDelete": "restrict", "onUpdate": "cascade"}, "fk_gender": {"name": "fk_gender", "tableFrom": "user", "tableTo": "gender", "columnsFrom": ["gender"], "columnsTo": ["name"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_key": {"name": "user_email_key", "nullsNotDistinct": false, "columns": ["email"]}, "user_phone_number_key": {"name": "user_phone_number_key", "nullsNotDistinct": false, "columns": ["phone_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_role": {"name": "user_role", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"fk_user": {"name": "fk_user", "tableFrom": "user_role", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "fk_role": {"name": "fk_role", "tableFrom": "user_role", "tableTo": "role", "columnsFrom": ["role"], "columnsTo": ["role"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_role_user_id_role_key": {"name": "user_role_user_id_role_key", "nullsNotDistinct": false, "columns": ["user_id", "role"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workday_setting": {"name": "workday_setting", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "weekday": {"name": "weekday", "type": "text", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "numeric", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "numeric", "primaryKey": false, "notNull": true}, "is_day_active": {"name": "is_day_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "timezone_id": {"name": "timezone_id", "type": "uuid", "primaryKey": false, "notNull": true}, "last_updated_by": {"name": "last_updated_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"workday_setting_team_id_fkey": {"name": "workday_setting_team_id_fkey", "tableFrom": "workday_setting", "tableTo": "team", "columnsFrom": ["team_id"], "columnsTo": ["teamId"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"workday_setting_id_key": {"name": "workday_setting_id_key", "nullsNotDistinct": false, "columns": ["id"]}, "workday_setting_team_id_key": {"name": "workday_setting_team_id_key", "nullsNotDistinct": false, "columns": ["team_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}