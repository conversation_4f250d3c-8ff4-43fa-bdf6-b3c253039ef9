ALTER TABLE "team" RENAME COLUMN "teamId" TO "id";--> statement-breakpoint
ALTER TABLE "team_location" RENAME COLUMN "teamLocationId" TO "id";--> statement-breakpoint
ALTER TABLE "appointment" DROP CONSTRAINT "appointment_team_id_fkey";
--> statement-breakpoint
ALTER TABLE "appointment" DROP CONSTRAINT "appointment_team_location_id_fkey";
--> statement-breakpoint
ALTER TABLE "appointment_type" DROP CONSTRAINT "appointment_type_team_id_fkey";
--> statement-breakpoint
ALTER TABLE "calendar_block" DROP CONSTRAINT "calendar_blocks_team_id_fkey";
--> statement-breakpoint
ALTER TABLE "calendar_block" DROP CONSTRAINT "calendar_blocks_team_location_id_fkey";
--> statement-breakpoint
ALTER TABLE "member" DROP CONSTRAINT "member_team_id_fkey";
--> statement-breakpoint
ALTER TABLE "member" DROP CONSTRAINT "member_team_location_id_fkey";
--> statement-breakpoint
ALTER TABLE "member_invite" DROP CONSTRAINT "member_invite_team_location_id_fkey";
--> statement-breakpoint
ALTER TABLE "member_invite" DROP CONSTRAINT "member_invite_team_id_fkey";
--> statement-breakpoint
ALTER TABLE "team_bank_detail" DROP CONSTRAINT "team_bank_detail_team_id_fkey";
--> statement-breakpoint
ALTER TABLE "team_location" DROP CONSTRAINT "team_location_team_id_fkey";
--> statement-breakpoint
ALTER TABLE "team_payment_provider" DROP CONSTRAINT "team_payment_provider_team_id_fkey";
--> statement-breakpoint
ALTER TABLE "workday_setting" DROP CONSTRAINT "workday_setting_team_id_fkey";
--> statement-breakpoint
ALTER TABLE "appointment" ADD CONSTRAINT "appointment_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."team"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "appointment" ADD CONSTRAINT "appointment_team_location_id_fkey" FOREIGN KEY ("team_location_id") REFERENCES "public"."team_location"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "appointment_type" ADD CONSTRAINT "appointment_type_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."team"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "calendar_block" ADD CONSTRAINT "calendar_blocks_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."team"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "calendar_block" ADD CONSTRAINT "calendar_blocks_team_location_id_fkey" FOREIGN KEY ("team_location_id") REFERENCES "public"."team_location"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "member" ADD CONSTRAINT "member_team_id_fkey" FOREIGN KEY ("id") REFERENCES "public"."team"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "member" ADD CONSTRAINT "member_team_location_id_fkey" FOREIGN KEY ("team_location_id") REFERENCES "public"."team_location"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "member_invite" ADD CONSTRAINT "member_invite_team_location_id_fkey" FOREIGN KEY ("team_location_id") REFERENCES "public"."team_location"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "member_invite" ADD CONSTRAINT "member_invite_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."team"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "team_bank_detail" ADD CONSTRAINT "team_bank_detail_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."team"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "team_location" ADD CONSTRAINT "team_location_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."team"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "team_payment_provider" ADD CONSTRAINT "team_payment_provider_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."team"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "workday_setting" ADD CONSTRAINT "workday_setting_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."team"("id") ON DELETE restrict ON UPDATE cascade;