{"name": "@repo/drizzle", "version": "1.0.0", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/es/index.js", "types": "./dist/es/index.d.ts", "scripts": {"build": "bunchee", "dev": "bunchee --watch", "db:studio": "drizzle-kit studio", "db:change": "dotenv -e .env  -- drizzle-kit generate --config drizzle.config.ts; drizzle-kit migrate --config drizzle.config.ts;", "db:stop": "docker compose down", "db:start": "docker compose up"}, "dependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "bunchee": "^6.5.2", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.44.1", "eslint": "^9.28.0"}, "devDependencies": {"@types/node": "^22.15.29", "dotenv-cli": "^8.0.0"}}