import { getHours, getYear } from 'date-fns'
import { sha256 } from '@oslojs/crypto/sha2'
import { Decimal } from 'decimal.js'
import SuperJSON from 'superjson'
import type { IAuthAppData } from '../types'

export const timeGreeting = () => {
	let time: string = 'morning'
	const today = new Date()
	const currentHour = getHours(today)
	if (currentHour >= 12 && currentHour < 18) {
		time = 'afternoon'
	} else if (currentHour >= 18) {
		time = 'evening'
	}
	return `Good ${time}`
}

export const getRandomColorName = (): string => {
	const colors: string[] = [
		'bg-red-900',
		'bg-orange-900',
		'bg-yellow-900',
		'bg-green-900',
		'bg-indigo-900',
		'bg-violet-900',
		'bg-pink-900',
		'bg-brown-900',
		'bg-gray-900',
		'bg-black-900',
		'bg-cyan-900',
		'bg-teal-900',
		'bg-lime-900',
		'bg-amber-900',
		'bg-purple-900'
	]

	const randomIndex: number = Math.floor(Math.random() * colors.length)
	return colors[randomIndex]
}
export const getCopyRight = () => {
	const today: Date = new Date()
	return `© ${getYear(today)} SpenDeed.AI. All rights reserved.`
}
export const getInitials = (firstName: string, surname: string) => {
	return `${firstName?.substring(0, 1)?.toUpperCase()} ${surname?.substring(0, 1)?.toUpperCase()}`
}
export const getStringInitials = (fullString: string) => {
	return fullString
		? fullString
			.split(' ')
			.map((name) => name.charAt(0).toUpperCase())
			.join('')
		: 'NA'
}
export const capitalizeFirstLetter = (input: string): string => {
	if (input.length === 0) {
		return input
	}
	return input.charAt(0).toUpperCase() + input.slice(1).toLowerCase()
}
export const getDomain = (email: string) => {
	const atIndex: number = email.indexOf('@')
	if (atIndex === -1) {
		throw new Error('Invalid email address')
	}
	return email.slice(atIndex + 1)
}
export const greetings = (displayName: string) => {
	return `${timeGreeting()}  ${displayName ? displayName : 'Anonymous'}!`
}
//1 per module
export const displayCurrency = (_entity: any, value: number | undefined): string => {
	if (value === undefined) return '- €'
	return new Intl.NumberFormat('fr', { currency: 'EUR', style: 'currency' }).format(value ?? 0)
}

export const displayCurrencyK = (_entity: any, value: number | undefined): string => {
	if (value === undefined) return '- €'

	if (Math.abs(value) < 1000) {
		return displayCurrency(_entity, value)
	}
	return new Intl.NumberFormat('fr', {
		currency: 'EUR',
		style: 'currency',
		maximumFractionDigits: 0
	})
		.format(value / 1000)
		.replace('€', 'k€')
}

export const stripUrl = (url: string) => {
	let result = url
	// Keep replacing until no more parentheses are found
	while (result.includes('(')) {
		result = result.replace(/\([^()]*\)/g, '')
	}
	// Remove any duplicate slashes that might have been created
	result = result.replace(/\/+/g, '/')
	// Remove trailing slash if it wasn't in the original URL
	if (!url.endsWith('/') && result.endsWith('/')) {
		result = result.slice(0, -1)
	}

	return result
}
export const buildGravatarUrl = (email: string) => {
	const content = new TextEncoder().encode(email.trim().toLowerCase())
	const hashedEmail = sha256(content)
	return `https://gravatar.com/avatar/${hashedEmail}?d=wavatar`
}

export const getKeyByValue = (obj: object, value: any): string | undefined => {
	return Object.keys(obj).find((key) => obj[key as keyof typeof obj] === value)
}

export const saveEmailLocally = (email: string) => localStorage.setItem('email', email)

export const getLocalEmail = () => localStorage.getItem('email')

export const formatTimeWithAmPm = (timeStr: string): string => {
	const [hours, minutes] = timeStr.split(':').map(Number)
	const date = new Date()
	date.setHours(hours, minutes)
	return date
		.toLocaleString('en-US', {
			hour: 'numeric',
			minute: '2-digit',
			hour12: true
		})
		.toLowerCase()
}

export const initSuperJson = () => {
	SuperJSON.registerCustom<Decimal, string>(
		{
			isApplicable: (v): v is Decimal => Decimal.isDecimal(v),
			serialize: (v) => v.toJSON(),
			deserialize: (v) => new Decimal(v)
		},
		'decimal.js'
	)
}

export const objectToText = (obj: any): string => {
	return Object.entries(obj)
		.map(([key, value]) => `${key}: ${value}`)
		.join(' | ')
}

export const classToPlain = <T extends object>(instance: T): Partial<T> => {
	const result: Partial<T> = {}
	for (const key of Object.keys(instance)) {
		result[key as keyof T] = instance[key as keyof T]
	}
	return result
}

export const hasPermission = (authData: IAuthAppData, { userId, teamId, teamLocationId }: { userId: string; teamId: string; teamLocationId: string }) => {
	return {
		isTeamAdmin: isTeamAdmin(authData, teamId),
		isTeamMember: isTeamMember(authData, teamId),
		isOwner: isOwner(authData, userId),
		isLocationAdmin: isTeamLocationAdmin(authData, teamLocationId)
	}
}

export const isTeamAdmin = (authData, teamId) => {
	return !!authData?.isTeamAdmin && teamId === authData?.teamId
}

export const isTeamMember = (authData, teamId) => {
	return !!authData?.isTeamMember && teamId === authData?.teamId
}
export const isOwner = (authData, userId: string) => {
	return authData.isTeamMember && authData?.userID === userId
}

export const isTeamLocationAdmin = (authData, teamLocationId) => {
	return !!authData?.isTeamAdmin && teamLocationId === authData?.teamLocationId
}
