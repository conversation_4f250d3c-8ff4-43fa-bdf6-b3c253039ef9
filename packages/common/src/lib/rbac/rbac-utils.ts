export function any(promises: Promise<boolean>[]): Promise<boolean> {
  return Promise.all(promises.map(p => p.catch(e => e)))
    .then(results => {
      const validResults = results.filter(result => !(result instanceof Error));
      if (validResults.some(result => result === true)) {
        return true;
      }
      return false;
    });
}

export function isGlob(str: string): boolean {
  return str.includes('*');
}

export function globToRegex(str: string): RegExp {
  const escaped = str.replace(/[-\/\\^$+?.()|[\]{}]/g, '\\$&');
  const globbed = escaped.replace(/\*/g, '.*');
  return new RegExp(`^${globbed}$`);
}