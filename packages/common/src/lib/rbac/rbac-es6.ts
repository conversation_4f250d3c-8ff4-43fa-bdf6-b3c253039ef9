import debugModule from 'debug'
import { any, globToRegex, isGlob } from './rbac-utils'


const debug = new debugModule('rbac')

interface Operation {
  name: string
  when?: (params: any) => Promise<boolean> | boolean
}

interface GlobOperation {
  name: RegExp
  original: string
  when?: (params: any) => Promise<boolean> | boolean
}

interface RoleObject {
  can: Record<string, 1 | ((params: any) => Promise<boolean> | boolean)>
  canGlob: GlobOperation[]
  inherits?: string[]
}

interface RoleDefinition {
  can: (string | Operation)[]
  inherits?: string[]
}

type RoleMap = Record<string, RoleDefinition>

class RBAC {
  private _inited: boolean
  private _init?: Promise<void>
  private roles!: Map<string, RoleObject>

  constructor(roles: RoleMap | (() => Promise<RoleMap>) | Promise<RoleMap>) {
    this._inited = false
    if (typeof roles !== 'function' && typeof roles.then !== 'function') {
      debug('sync init')
      // Add roles to class and mark as inited
      this.roles = this._parseRoleMap(roles as RoleMap)
      this._inited = true
    } else {
      debug('async init')
      this._init = this.asyncInit(roles as (() => Promise<RoleMap>) | Promise<RoleMap>)
    }
  }

  private _parseRoleMap(roles: RoleMap): Map<string, RoleObject> {
    debug('parsing rolemap')
    // If not a function then should be object
    if (typeof roles !== 'object') {
      throw new TypeError('Expected input to be object')
    }

    const map = new Map<string, RoleObject>()

    // Standardize roles
    Object.keys(roles).forEach(role => {
      const roleObj: RoleObject = {
        can: {},
        canGlob: []
      }
      // Check can definition
      if (!Array.isArray(roles[role].can)) {
        throw new TypeError('Expected roles[' + role + '].can to be an array')
      }
      if (roles[role].inherits) {
        if (!Array.isArray(roles[role].inherits)) {
          throw new TypeError('Expected roles[' + role + '].inherits to be an array')
        }
        roleObj.inherits = []
        roles[role].inherits!.forEach(child => {
          if (typeof child !== 'string') {
            throw new TypeError('Expected roles[' + role + '].inherits element')
          }
          if (!roles[child]) {
            throw new TypeError('Undefined inheritance role: ' + child)
          }
          roleObj.inherits!.push(child)
        })
      }
      // Iterate allowed operations
      roles[role].can.forEach(operation => {
        // If operation is string
        if (typeof operation === 'string') {
          // Add as an operation
          if (!isGlob(operation)) {
            roleObj.can[operation] = 1
          } else {
            roleObj.canGlob.push({ name: globToRegex(operation), original: operation })
          }
          return
        }
        // Check if operation has a .when function
        if (typeof (operation as Operation).when === 'function' && typeof (operation as Operation).name === 'string') {
          const op = operation as Operation
          if (!isGlob(op.name)) {
            roleObj.can[op.name] = op.when!
          } else {
            roleObj.canGlob.push({ name: globToRegex(op.name), original: op.name, when: op.when })
          }
          return
        }
        throw new TypeError('Unexpected operation type')
      })

      map.set(role, roleObj)
    })

    return map
  }

  private async asyncInit(roles: (() => Promise<RoleMap>) | Promise<RoleMap>): Promise<void> {
    // If opts is a function execute for async loading
    let resolvedRoles: RoleMap

    if (typeof roles === 'function') {
      resolvedRoles = await (roles as () => Promise<RoleMap>)()
    } else {
      resolvedRoles = await roles
    }

    // Add roles to class and mark as inited
    this.roles = this._parseRoleMap(resolvedRoles)
    this._inited = true
  }

  async can(role: string | string[], operation: string, params?: any): Promise<boolean> {
    // If not inited then wait until init finishes
    if (!this._inited) {
      debug('Not inited, wait')
      await this._init
      debug('Init complete, continue')
    }

    if (Array.isArray(role)) {
      debug('array of roles, try all')
      return any(role.map(r => this.can(r, operation, params)))
    }

    if (typeof role !== 'string') {
      debug('Expected first parameter to be string : role')
      return false
    }

    if (typeof operation !== 'string') {
      debug('Expected second parameter to be string : operation')
      return false
    }

    const $role = this.roles.get(role)

    if (!$role) {
      debug('Undefined role')
      return false
    }

    // IF this operation is not defined at current level try higher
    if (!$role.can[operation] && !$role.canGlob.find(glob => glob.name.test(operation))) {
      debug('Not allowed at this level, try higher')
      // If no parents reject
      if (!$role.inherits || $role.inherits.length < 1) {
        debug('No inherit, reject false')
        return false
      }
      // Return if any parent resolves true or all reject
      return any($role.inherits.map(parent => {
        debug('Try from ' + parent)
        return this.can(parent, operation, params)
      }))
    }

    // We have the operation resolve
    if ($role.can[operation] === 1) {
      debug('We have a match, resolve')
      return true
    }

    // Operation is conditional, run async function
    if (typeof $role.can[operation] === 'function') {
      debug('Operation is conditional, run fn')
      try {
        return $role.can[operation](params)
      } catch (e) {
        debug('conditional function threw', e)
        return false
      }
    }

    // Try globs
    const globMatch = $role.canGlob.find(glob => glob.name.test(operation))
    if (globMatch && !globMatch.when) {
      debug(`We have a globmatch (${globMatch.original}), resolve`)
      return true
    }

    if (globMatch && globMatch.when) {
      debug(`We have a conditional globmatch (${globMatch.original}), run fn`)
      try {
        return globMatch.when(params)
      } catch (e) {
        debug('conditional function threw', e)
        return false
      }
    }

    // No operation reject as false
    debug('Shouldnt have reached here, something wrong, reject')
    throw new Error('something went wrong')
  }

  // Static factory method
  static create(opts: RoleMap | (() => Promise<RoleMap>) | Promise<RoleMap>): RBAC {
    return new RBAC(opts)
  }
}

export default RBAC