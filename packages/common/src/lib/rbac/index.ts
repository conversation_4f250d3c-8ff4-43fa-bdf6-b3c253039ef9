import type { IAuthAppData } from '../../types'
import RBAC from './rbac-es6'

export interface paramsType {
      payload: any
      authData: IAuthAppData
}
export const ActionTypes = {
      readTeam: 'read:team',
      manageTeam: 'manage:team',
      readmembers: 'read:members',
      manageMembers: 'manage:members',
      readAppointments: 'read:appointments',
      manageAppointments: 'manage:appointments',
      readAppointmentTypes: 'read:appointmentTypes',
      manageAppointmentTypes: 'manage:appointmentTypes',
      readTeamLocations: 'read:teamLocations',
      manageTeamLocations: 'manage:teamLocations',
      readTeamBankDetails: 'read:teamBankDetails',
      manageTeamBankDetails: 'manage:teamBankDetails',
      readTeamPaymentProviders: 'read:teamPaymentProviders',
      manageTeamPaymentProviders: 'manage:teamPaymentProviders',
      readTeamWorkdaySettings: 'read:teamWorkdaySettings',
      manageTeamWorkdaySettings: 'manage:teamWorkdaySettings',
      readTeamMembers: 'read:teamMembers',
      manageTeamMembers: 'manage:teamMembers',
      readTeamMemberInvites: 'read:teamMemberInvites',
      manageTeamMemberInvites: 'manage:teamMemberInvites',
      readCalendarBlocks: 'read:calendarBlocks',
      manageCalendarBlocks: 'manage:calendarBlocks',
      readStorageBuckets: 'read:storageBuckets',
      manageStorageBuckets: 'manage:storageBuckets',
      readStorageFiles: 'read:storageFiles',
      manageStorageFiles: 'manage:storageFiles',
      readUserRoles: 'read:userRoles',
      manageUserRoles: 'manage:userRoles',
      readRoles: 'read:roles',
      manageRoled: 'manage:roles',
      readTimeZones: 'read:timeZones',
      manageTimeZones: 'manage:timeZones',
      all: '*',
      allTeams: 'team:*',
      allTeamLocations: 'team:location:*',
      allMembers: 'team:member:*',
      allBankDetails: 'team:bankDetail:*',
      allAppointments: 'team:appointment:*',
      allAppointmentTypes: 'team:appointmentType:*',
      allPaymentProviders: 'team:paymentProvider:*',
      allWorkdaySettings: 'team:workdaySetting:*',
      allMemberInvites: 'team:memberInvite:*',
      allCalendarBlocks: 'team:calendarBlock:*',
      allStorageBuckets: 'team:storageBucket:*',
      allStorageFiles: 'team:storageFile:*',
      allUserRoles: 'team:userRole:*',
      allRoles: 'team:role:*',
      allTimeZones: 'team:timeZone:*'
} as const

export type ActionType = (typeof ActionTypes)[keyof typeof ActionTypes]

export interface RoleDefinition {
      can: (string | {
            name: string
            when?: (params: any) => Promise<boolean> | boolean
      })[]
      inherits?: string[]
}

export type RoleMap = Record<string, RoleDefinition>


const opts: RoleMap = {
      Admin: {
            can: [ActionTypes.all]
      },
      TeamMember: {
            can: [
                  {
                        name: ActionTypes.readTeam, when: (params: any) => {
                              return params.payload.teamId === params.authData.teamId
                        }
                  },
                  {
                        name: ActionTypes.readmembers,
                        when: (params: any) => {
                              return params.payload.userId === params.authData.userId
                        }
                  },
                  {
                        name: ActionTypes.readAppointments, when: (params: any) => {
                              return params.payload.teamId === params.authData.teamId
                        }
                  }, {
                        name: ActionTypes.manageAppointments, when: (params: any) => {
                              return params.payload.locationId === params.authData.teamLocationId
                        }
                  },
                  {
                        name: ActionTypes.readAppointmentTypes, when: (params: any) => {
                              return params.payload.teamId === params.authData.teamId
                        }
                  },
                  {
                        name: ActionTypes.readTeamLocations, when: (params: any) => {
                              return params.payload.teamId === params.authData.teamId
                        }
                  },
                  {
                        name: ActionTypes.manageAppointmentTypes, when: (params: any) => {
                              return params.payload.teamId === params.authData.teamId
                        }
                  },
                  {
                        name: ActionTypes.readTeamBankDetails, when: (params: any) => {
                              return params.payload.teamId === params.authData.teamId
                        }
                  },
                  {
                        name: ActionTypes.readTeamPaymentProviders, when: (params: any) => {
                              return params.payload.teamId === params.authData.teamId
                        }
                  },
                  {
                        name: ActionTypes.readTeamWorkdaySettings, when: (params: any) => {
                              return params.payload.teamId === params.authData.teamId
                        }
                  },
                  {
                        name: ActionTypes.readTeamMembers, when: (params: any) => {
                              return params.payload.teamId === params.authData.teamId
                        }
                  },
                  {
                        name: ActionTypes.readCalendarBlocks, when: (params: any) => {
                              return params.payload.teamId === params.authData.teamId
                        }
                  },
                  {
                        name: ActionTypes.readStorageBuckets, when: (params: any) => {
                              return params.payload.teamId === params.authData.teamId
                        }
                  },
                  {
                        name: ActionTypes.readStorageFiles, when: (params: any) => {
                              return params.payload.teamId === params.authData.teamId
                        }
                  },
                  {
                        name: ActionTypes.readUserRoles, when: (params: any) => {
                              return params.payload.userId === params.authData.userId
                        }
                  },
                  ActionTypes.readRoles,
                  ActionTypes.readTimeZones
            ]
      },
      TeamAdmin: {
            can: [{
                  name: ActionTypes.manageTeam, when: (params: any) => {
                        return params.payload.teamId === params.authData.teamId
                  }
            },
            {
                  name: ActionTypes.readmembers, when: (params: any) => {
                        return params.payload.teamId === params.authData.teamId
                  }
            }, {
                  name: ActionTypes.manageMembers, when: (params: any) => {
                        return params.payload.teamId === params.authData.teamId
                  }
            },
            {
                  name: ActionTypes.manageAppointments, when: (params: any) => {
                        return params.payload.teamId === params.authData.teamId
                  }
            },
            {
                  name: ActionTypes.manageAppointmentTypes, when: (params: any) => {
                        return params.payload.teamId === params.authData.teamId
                  }
            },
            {
                  name: ActionTypes.allTeamLocations, when: (params: any) => {
                        return params.payload.teamId === params.authData.teamId
                  }
            },
            {
                  name: ActionTypes.allBankDetails, when: (params: any) => {
                        return params.payload.teamId === params.authData.teamId
                  }
            },
            {
                  name: ActionTypes.allPaymentProviders, when: (params: any) => {
                        return params.payload.teamId === params.authData.teamId
                  }
            },
            {
                  name: ActionTypes.allWorkdaySettings, when: (params: any) => {
                        return params.payload.teamId === params.authData.teamId
                  }
            },

            {
                  name: ActionTypes.allMemberInvites, when: (params: any) => {
                        return params.payload.teamId === params.authData.teamId
                  }
            },
            {
                  name: ActionTypes.allCalendarBlocks, when: (params: any) => {
                        return params.payload.teamId === params.authData.teamId
                  }
            },
            {
                  name: ActionTypes.allStorageBuckets, when: (params: any) => {
                        return params.payload.teamId === params.authData.teamId
                  }
            },
            {
                  name: ActionTypes.allStorageFiles, when: (params: any) => {
                        return params.payload.teamId === params.authData.teamId
                  }
            },
            {
                  name: ActionTypes.allUserRoles, when: (params: any) => {
                        return params.payload.teamId === params.authData.teamId
                  }
            },
            ActionTypes.readRoles,
            ActionTypes.readTimeZones
            ],
            inherits: ['TeamMember']
      },

      TeamLocationAdmin: {
            can: [{
                  name: 'manage:appointments', when: (params: any) => {
                        return params.payload.teamLocationId === params.authData.teamLocationId
                  }
            }, {
                  name: 'manage:teamWorkdaySettings', when: (params: any) => {
                        return params.payload.teamLocationId === params.authData.teamLocationId
                  }
            },
            {
                  name: 'read:teamMembers', when: (params: any) => {
                        return params.payload.teamLocationId === params.authData.teamLocationId
                  }
            }, {
                  name: 'manage:calendarBlocks', when: (params: any) => {
                        return params.payload.teamLocationId === params.authData.teamLocationId
                  }
            }
            ],
            inherits: ['TeamMember']
      }
}

const rbac = new RBAC(opts)

// Use the RBAC instance
export const checkPermission = async (
      role: string | string[],
      operation: string,
      params?: any
): Promise<boolean> => {
      return await rbac.can(role, operation, params)
}

export { rbac }