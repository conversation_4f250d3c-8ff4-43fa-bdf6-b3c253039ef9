import {
	maxLength,
	minLength,
	object,
	optional,
	pipe,
	boolean,
	string,
	type InferInput
} from 'valibot'
import stringVal from './stringVal'

export const bankAccountFormSchema = object({
	accountName: pipe(
		string(stringVal('Account Name')),
		minLength(2, 'Account name must be at least 2 characters long'),
		maxLength(100, 'Account name must be less than 100 characters')
	),
	accountNumber: pipe(
		string(stringVal('Account Number')),
		minLength(6, 'Account number must be at least 6 characters long'),
		maxLength(20, 'Account number must be less than 20 characters')
	),
	bankName: pipe(
		string(stringVal('Bank Name')),
		minLength(2, 'Bank name must be at least 2 characters long'),
		maxLength(100, 'Bank name must be less than 100 characters')
	),
	branchCode: pipe(
		string(stringVal('Branch Code')),
		minLength(4, 'Branch code must be at least 4 characters long'),
		maxLength(10, 'Branch code must be less than 10 characters')
	),
	swiftCode: pipe(
		string(stringVal('Swift Code')),
		minLength(8, 'SWIFT code must be at least 8 characters long'),
		maxLength(11, 'SWIFT code must be less than 11 characters')
	)
})

export type BankAccountFormSchemaType = InferInput<typeof bankAccountFormSchema>

export const paymentProviderFormSchema = object({
	id: optional(string(stringVal('ID'))),
	providerName: pipe(
		string(stringVal('Provider Name')),
		minLength(2, 'Provider name must be at least 2 characters long'),
		maxLength(100, 'Provider name must be less than 100 characters')
	),
	providerApiUrl: pipe(
		string(stringVal('API URL')),
		minLength(5, 'API URL must be at least 5 characters long'),
		maxLength(255, 'API URL must be less than 255 characters')
	),
	providerApiKey: optional(
		pipe(
			string(stringVal('Client ID')),
			minLength(8, 'Client ID must be at least 8 characters long'),
			maxLength(255, 'Client ID must be less than 255 characters')
		)
	),
	providerApiSecret: optional(
		pipe(
			string(stringVal('Client Secret')),
			minLength(16, 'Client secret must be at least 16 characters long'),
			maxLength(255, 'Client secret must be less than 255 characters')
		)
	),
	providerClientId: optional(
		pipe(
			string(stringVal('Client Secret')),
			minLength(16, 'Client secret must be at least 16 characters long'),
			maxLength(255, 'Client secret must be less than 255 characters')
		)
	),
	isActive: optional(boolean(), false),
	providerWebhookUrl: optional(
		pipe(
			string(stringVal('Webhook URL')),
			minLength(5, 'Webhook URL must be at least 5 characters long'),
			maxLength(255, 'Webhook URL must be less than 255 characters')
		)
	)
})

export type PaymentProviderFormSchemaType = InferInput<typeof paymentProviderFormSchema>
