import stringVal from './stringVal'
import { object, string, optional, any, type InferInput, pipe, regex } from 'valibot'

export const addTeamMemberFormSchema = object({
	id: optional(string()),
	givenName: string(),
	familyName: string(),
	gender: any(),
	email: string(),
	role: string(),
	location: string(),
	idDocumentNumber: string(),
	phoneNumber: pipe(
		string(stringVal('Phone Number')),
		regex(/^[0-9]{10}$/, 'Phone number must be 10 digits')
	)
})
export type AddTeamMemberFormSchemaType = InferInput<typeof addTeamMemberFormSchema>
