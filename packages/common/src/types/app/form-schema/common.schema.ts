import type { FormOptions } from 'sveltekit-superforms'

export type FormMessage = {
	status: 'error' | 'success' | 'warning'
	text: string
	data?: any
}

export const formOptions: FormOptions<any, any> = {
	validators: undefined,
	resetForm: false,
	errorSelector: '[aria-invalid="true"],[data-invalid]',
	scrollToError: 'smooth',
	autoFocusOnError: 'detect',
	taintedMessage: 'Do you want to leave this page? Changes you made may not be saved.',
	validationMethod: 'onblur',
	multipleSubmits: 'prevent'
}

export const lettersOnly = /^[A-Za-z]+$/
export const companyEmailOnly =
	/^[a-zA-Z0-9._%+-]+@(?!gmail.com)(?!yahoo.com)(?!hotmail.com)(?!yahoo.co.in)(?!aol.com)(?!live.com)(?!outlook.com)[a-zA-Z0-9_-]+.[a-zA-Z0-9-.]{2,61}$/
export const passwordConstraint = '^(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{6,})'

// export const ActionTypes = {
// 	UpdateMemberStatus: 'updateMemberStatus',
// 	UpdateLocation: 'updateLocation',
// 	DisableLocation: 'DisableLocation'
// } as const

// export type ActionType = (typeof ActionTypes)[keyof typeof ActionTypes]

// export type UpdateMemberStatusParamsType = {
// 	userId: string
// 	teamId: string
// 	status: string
// }
// export type UpdateLocationParamsType = {
// 	isActive: boolean
// 	teamId: string
// 	locationId: string
// }
// export type ServerPostType = {
// 	params: UpdateMemberStatusParamsType | Record<string, any> | UpdateLocationParamsType
// 	actionType: (typeof ActionTypes)[keyof typeof ActionTypes]
// }

