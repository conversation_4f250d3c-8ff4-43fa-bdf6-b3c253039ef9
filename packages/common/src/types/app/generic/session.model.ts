import type { AppUserInfo } from './auth'

export type ISessionData = {
	jwtToken: string | undefined
	user: AppUserInfo | undefined
	isAdmin: boolean
	isTeamAdmin: boolean
	teamConfigIsSetup: boolean
	expires: Date | undefined
}

export type JWTClaims = {
	loginMethod: 'email' | 'apple' | 'google' | 'passkey'
	admin: boolean
	iat: number
	email: string
	scopes: string[]
	userId: string
	defaultRole: string
	allowedRoles: string[]
	teamId: string
	teamLocationId: string
}
