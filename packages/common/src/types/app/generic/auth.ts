
import stringVal from '../stringVal'
import { boolean, object, optional, string, type InferInput, any, pipe, regex } from 'valibot'
import { TeamMemberStatuses } from './app.types'

export const Roles = {
	User: 'user',
	Admin: 'Admin',
	TeamAdmin: 'team-admin',
	TeamMember: 'team-member',
	TeamLocationAdmin: 'team-location-admin'
} as const

export type Role = (typeof Roles)[keyof typeof Roles]

export const Genders = {
	Male: 'Male',
	Female: 'Female',
	Other: 'Other'
} as const
export type GenderType = (typeof Genders)[keyof typeof Genders]

export const Permissions = {
	ManageTeam: 'Manage Team',
	ReadTeam: 'Read Team',
	ReadMembers: 'Read Members',
	ManageMembers: 'Manage Members',
	Contribute: 'Contribute',
	ReadAppointments: 'Read Appointments',
	ManageAppointments: 'Manage Appointments',
	ManageAppointmentTypes: 'Manage Appointment Types',
	ReadAppointmentTypes: 'Read Appointment Types'
} as const

export type Permission = (typeof Permissions)[keyof typeof Permissions]

export const AuthAppDataSchema = object({
	teamId: optional(string()),
	defaultRole: optional(string(), Roles.TeamMember),
	teamLocationId: optional(string()),
	allowedRoles: optional(any(), [Roles.TeamMember])
})

export type IAuthAppData = InferInput<typeof AuthAppDataSchema>

export const AppUserMetadataSchema = object({
	givenName: string(),
	familyName: string(),
	email: string(),
	teamId: string(),
	teamLocationId: string(),
	businessName: optional(string()),
	phoneNumber: string(),
	userStatus: optional(string(), TeamMemberStatuses.Active),
	idDocumentNumber: optional(string()),
	gender: optional(string(), Genders.Male),
	dateOfBirth: optional(string()),
	avatarUrl: string()
})

export type IAppUserMetadata = InferInput<typeof AppUserMetadataSchema>

export const AppUserSchema = object({
	id: string(),
	email: string(),
	givenName: string(),
	familyName: string(),
	initials: optional(string(), ''),
	gender: optional(any()),
	isUserEmailVerified: optional(boolean(), false),
	phoneNumber: optional(
		pipe(
			string(stringVal('Phone Number')),
			regex(/^[0-9]{10}$/, 'Phone number must be 10 digits')
		)
	),
	isPhoneNumberVerified: optional(boolean(), false),
	idDocumentNumber: optional(string()),
	isEnabled: boolean(),
	profilePhotoUrl: string(),
	status: optional(string(), TeamMemberStatuses.Active),
	teamId: string(),
	defaultRole: optional(string(), Roles.TeamMember),
	teamLocationId: string(),
	allowedRoles: optional(any(), [Roles.TeamMember])
})

export type AppUserInfo = InferInput<typeof AppUserSchema>

export const TeamPlans = {
	Free: 'Free',
	Pro: 'Pro',
	Enterprise: 'Enterprise'
} as const
export type TeamPlan = (typeof TeamPlans)[keyof typeof TeamPlans]

export const TeamLocationStatuses = {
	Active: 'Active',
	InActive: 'InActive'
} as const
export type TeamLocationStatus = (typeof TeamLocationStatuses)[keyof typeof TeamLocationStatuses]



export const TeamRoles = {
	TeamAdmin: 'team-admin',
	TeamLocationAdmin: 'team-location-admin',
	TeamMember: 'team-member'
} as const



export const Gender = {
	Male: 'Male',
	Female: 'Female',
	Other: 'Other'
} as const

export const WorkDays = {
	Sunday: 'Sunday',
	Monday: 'Monday',
	Tuesday: 'Tuesday',
	Wednesday: 'Wednesday',
	Thursday: 'Thursday',
	Friday: 'Friday',
	Saturday: 'Saturday'
} as const

export const UserAccountTypes = {
	Staff: 'Staff',
	Customer: 'Customer'
} as const


export interface Updateable<T> {
	update(T: Partial<T>): any
	delete(): void
}

