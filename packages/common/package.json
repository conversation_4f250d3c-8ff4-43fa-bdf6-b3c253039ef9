{"name": "@repo/common", "version": "1.0.0", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/es/index.js", "types": "./dist/es/index.d.ts", "scripts": {"build": "bunchee", "dev": "bunchee --watch"}, "dependencies": {"@oslojs/crypto": "^1.0.1", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "date-fns": "^4.1.0", "debug": "^4.4.1", "decimal.js": "^10.5.0", "eslint": "^9.28.0", "linq-extensions": "^1.0.4", "superjson": "^2.2.2", "valibot": "^1.1.0"}, "peerDependencies": {"svelte": "^5.23.1"}, "devDependencies": {"bunchee": "^6.5.2", "easy-rbac": "^3.2.0"}}