import * as valibot from 'valibot';
import { InferInput } from 'valibot';
import { FormOptions } from 'sveltekit-superforms';

declare const dynamicModalSchema: valibot.ObjectSchema<{
    readonly title: valibot.StringSchema<"Required">;
    readonly description: valibot.StringSchema<"Required">;
    readonly componentName: valibot.StringSchema<"Required">;
    readonly componentProps: valibot.OptionalSchema<valibot.UnknownSchema, undefined>;
    readonly canClose: valibot.OptionalSchema<valibot.BooleanSchema<"Required">, true>;
    readonly color: valibot.PicklistSchema<["primary", "gray", "red", "yellow", "green", "indigo", "purple", "pink", "blue", "light", "dark", "default", "dropdown", "navbar", "navbarUl", "form", "none"], undefined>;
    readonly size: valibot.PicklistSchema<["xl", "lg", "md"], undefined>;
}, undefined>;
type IDynamicModal = InferInput<typeof dynamicModalSchema>;
declare const ToastTypeSchema: valibot.PicklistSchema<["success", "error", "info", "warning"], undefined>;
type ToastType = InferInput<typeof ToastTypeSchema>;
declare const dynamicSheetSchema: valibot.ObjectSchema<{
    readonly position: valibot.PicklistSchema<["left", "right", "top", "bottom"], undefined>;
    readonly side: valibot.PicklistSchema<["right", "top", "bottom", "left"], undefined>;
    readonly title: valibot.StringSchema<"Required">;
    readonly description: valibot.StringSchema<"Required">;
    readonly componentName: valibot.StringSchema<"Required">;
    readonly componentProps: valibot.OptionalSchema<valibot.UnknownSchema, undefined>;
    readonly canClose: valibot.OptionalSchema<valibot.BooleanSchema<"Required">, true>;
    readonly color: valibot.PicklistSchema<["primary", "gray", "red", "yellow", "green", "indigo", "purple", "pink", "blue", "light", "dark", "default", "dropdown", "navbar", "navbarUl", "form", "none"], undefined>;
    readonly size: valibot.PicklistSchema<["xl", "lg", "md"], undefined>;
}, undefined>;
type IDynamicSheet = InferInput<typeof dynamicSheetSchema>;
declare const SheetTypes: {
    readonly Marketing: "Marketing";
    readonly SignUpSuccess: "SignUpSuccess";
    readonly Login: "Login";
    readonly SignUp: "SignUp";
    readonly SignUpCode: "SignUpCode";
    readonly ForgotPass: "ForgotPass";
    readonly ResetPass: "ResetPass";
    readonly ResetPassCode: "ResetPassCode";
    readonly LoginCode: "LoginCode";
};
type SheetType = (typeof SheetTypes)[keyof typeof SheetTypes];
type IComponentCache = {
    componentName: string;
    component: any;
    dialogClass: string;
};
declare const CalendarViewTypes: {
    readonly Day: "Day";
    readonly Week: "Week";
    readonly Month: "Month";
};
type CalendarViewType = (typeof CalendarViewTypes)[keyof typeof CalendarViewTypes];
declare const InboxMessageTypes: {
    VerifyEmailDomain: string;
};
type InboxMessageType = (typeof InboxMessageTypes)[keyof typeof InboxMessageTypes];
declare const TeamMemberStatuses: {
    readonly Active: "Active";
    readonly InActive: "Inactive";
    readonly OnLeave: "On Leave";
    readonly Resigned: "Resigned";
};
type TeamMemberStatus = (typeof TeamMemberStatuses)[keyof typeof TeamMemberStatuses];
declare const ActorEvents: {
    readonly ItemCreated: "item.created";
    readonly ItemUpdated: "item.updated";
    readonly ItemDeleted: "item.deleted";
};
type ActorEvent = (typeof ActorEvents)[keyof typeof ActorEvents];

declare const appointmentRepeatTypes: {
    readonly Daily: "Daily";
    readonly Weekly: "Weekly";
    readonly EveryTwoWeeks: "EveryTwoWeeks";
    readonly EveryFourWeeks: "EveryFourWeeks";
    readonly Monthly: "Monthly";
};
type AppointmentRepeatType = (typeof appointmentRepeatTypes)[keyof typeof appointmentRepeatTypes];
declare const appointmentStatuses: {
    readonly Scheduled: "Scheduled";
    readonly Confirmed: "Confirmed";
    readonly Cancelled: "Cancelled";
    readonly Done: "Done";
    readonly Missed: "Missed";
};
type AppointmentStatus = (typeof appointmentStatuses)[keyof typeof appointmentStatuses];

declare const Roles: {
    readonly User: "user";
    readonly Admin: "Admin";
    readonly TeamAdmin: "team-admin";
    readonly TeamMember: "team-member";
    readonly TeamLocationAdmin: "team-location-admin";
};
type Role = (typeof Roles)[keyof typeof Roles];
declare const Genders: {
    readonly Male: "Male";
    readonly Female: "Female";
    readonly Other: "Other";
};
type GenderType = (typeof Genders)[keyof typeof Genders];
declare const Permissions: {
    readonly ManageTeam: "Manage Team";
    readonly ReadTeam: "Read Team";
    readonly ReadMembers: "Read Members";
    readonly ManageMembers: "Manage Members";
    readonly Contribute: "Contribute";
    readonly ReadAppointments: "Read Appointments";
    readonly ManageAppointments: "Manage Appointments";
    readonly ManageAppointmentTypes: "Manage Appointment Types";
    readonly ReadAppointmentTypes: "Read Appointment Types";
};
type Permission = (typeof Permissions)[keyof typeof Permissions];
declare const AuthAppDataSchema: valibot.ObjectSchema<{
    readonly teamId: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly defaultRole: valibot.OptionalSchema<valibot.StringSchema<undefined>, "team-member">;
    readonly teamLocationId: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly allowedRoles: valibot.OptionalSchema<valibot.AnySchema, readonly ["team-member"]>;
}, undefined>;
type IAuthAppData = InferInput<typeof AuthAppDataSchema>;
declare const AppUserMetadataSchema: valibot.ObjectSchema<{
    readonly givenName: valibot.StringSchema<undefined>;
    readonly familyName: valibot.StringSchema<undefined>;
    readonly email: valibot.StringSchema<undefined>;
    readonly teamId: valibot.StringSchema<undefined>;
    readonly teamLocationId: valibot.StringSchema<undefined>;
    readonly businessName: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly phoneNumber: valibot.StringSchema<undefined>;
    readonly userStatus: valibot.OptionalSchema<valibot.StringSchema<undefined>, "Active">;
    readonly idDocumentNumber: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly gender: valibot.OptionalSchema<valibot.StringSchema<undefined>, "Male">;
    readonly dateOfBirth: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly avatarUrl: valibot.StringSchema<undefined>;
}, undefined>;
type IAppUserMetadata = InferInput<typeof AppUserMetadataSchema>;
declare const AppUserSchema: valibot.ObjectSchema<{
    readonly id: valibot.StringSchema<undefined>;
    readonly email: valibot.StringSchema<undefined>;
    readonly givenName: valibot.StringSchema<undefined>;
    readonly familyName: valibot.StringSchema<undefined>;
    readonly initials: valibot.OptionalSchema<valibot.StringSchema<undefined>, "">;
    readonly gender: valibot.OptionalSchema<valibot.AnySchema, undefined>;
    readonly isUserEmailVerified: valibot.OptionalSchema<valibot.BooleanSchema<undefined>, false>;
    readonly phoneNumber: valibot.OptionalSchema<valibot.SchemaWithPipe<readonly [valibot.StringSchema<any>, valibot.RegexAction<string, "Phone number must be 10 digits">]>, undefined>;
    readonly isPhoneNumberVerified: valibot.OptionalSchema<valibot.BooleanSchema<undefined>, false>;
    readonly idDocumentNumber: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly isEnabled: valibot.BooleanSchema<undefined>;
    readonly profilePhotoUrl: valibot.StringSchema<undefined>;
    readonly status: valibot.OptionalSchema<valibot.StringSchema<undefined>, "Active">;
    readonly teamId: valibot.StringSchema<undefined>;
    readonly defaultRole: valibot.OptionalSchema<valibot.StringSchema<undefined>, "team-member">;
    readonly teamLocationId: valibot.StringSchema<undefined>;
    readonly allowedRoles: valibot.OptionalSchema<valibot.AnySchema, readonly ["team-member"]>;
}, undefined>;
type AppUserInfo = InferInput<typeof AppUserSchema>;
declare const TeamPlans: {
    readonly Free: "Free";
    readonly Pro: "Pro";
    readonly Enterprise: "Enterprise";
};
type TeamPlan = (typeof TeamPlans)[keyof typeof TeamPlans];
declare const TeamLocationStatuses: {
    readonly Active: "Active";
    readonly InActive: "InActive";
};
type TeamLocationStatus = (typeof TeamLocationStatuses)[keyof typeof TeamLocationStatuses];
declare const TeamRoles: {
    readonly TeamAdmin: "team-admin";
    readonly TeamLocationAdmin: "team-location-admin";
    readonly TeamMember: "team-member";
};
declare const Gender: {
    readonly Male: "Male";
    readonly Female: "Female";
    readonly Other: "Other";
};
declare const WorkDays: {
    readonly Sunday: "Sunday";
    readonly Monday: "Monday";
    readonly Tuesday: "Tuesday";
    readonly Wednesday: "Wednesday";
    readonly Thursday: "Thursday";
    readonly Friday: "Friday";
    readonly Saturday: "Saturday";
};
declare const UserAccountTypes: {
    readonly Staff: "Staff";
    readonly Customer: "Customer";
};
interface Updateable<T> {
    update(T: Partial<T>): any;
    delete(): void;
}

type Result$1<T> = {
    data: T;
    error: undefined;
    isOk: true;
} | {
    data: null;
    error: string;
    isOk: false;
};
declare const Result$1: {
    success: <T>(data: T) => Result$1<T>;
    failure: <T>(error: Error) => Result$1<T>;
};
type Prettify<T> = {
    [K in keyof T]: T[K];
} & {};

type ISessionData = {
    jwtToken: string | undefined;
    user: AppUserInfo | undefined;
    isAdmin: boolean;
    isTeamAdmin: boolean;
    teamConfigIsSetup: boolean;
    expires: Date | undefined;
};
type JWTClaims = {
    loginMethod: 'email' | 'apple' | 'google' | 'passkey';
    admin: boolean;
    iat: number;
    email: string;
    scopes: string[];
    userId: string;
    defaultRole: string;
    allowedRoles: string[];
    teamId: string;
    teamLocationId: string;
};

declare const genderListData: {
    id: string;
    name: string;
}[];

declare const TIMEZONES: readonly [{
    readonly id: "UTC";
    readonly name: "UTC";
}, {
    readonly id: "America/New_York";
    readonly name: "America/New_York";
}, {
    readonly id: "America/Chicago";
    readonly name: "America/Chicago";
}, {
    readonly id: "America/Denver";
    readonly name: "America/Denver";
}, {
    readonly id: "America/Los_Angeles";
    readonly name: "America/Los_Angeles";
}, {
    readonly id: "Europe/London";
    readonly name: "Europe/London";
}, {
    readonly id: "Europe/Paris";
    readonly name: "Europe/Paris";
}, {
    readonly id: "Europe/Berlin";
    readonly name: "Europe/Berlin";
}, {
    readonly id: "Europe/Moscow";
    readonly name: "Europe/Moscow";
}, {
    readonly id: "Asia/Dubai";
    readonly name: "Asia/Dubai";
}, {
    readonly id: "Asia/Singapore";
    readonly name: "Asia/Singapore";
}, {
    readonly id: "Asia/Tokyo";
    readonly name: "Asia/Tokyo";
}, {
    readonly id: "Australia/Sydney";
    readonly name: "Australia/Sydney";
}, {
    readonly id: "Pacific/Auckland";
    readonly name: "Pacific/Auckland";
}, {
    readonly id: "Africa/Lagos";
    readonly name: "Africa/Lagos (Abuja)";
}, {
    readonly id: "Africa/Johannesburg";
    readonly name: "Africa/Johannesburg (Pretoria)";
}, {
    readonly id: "Africa/Nairobi";
    readonly name: "Africa/Nairobi";
}];
type TimeZone = (typeof TIMEZONES)[number];

declare const userRolesData: {
    id: string;
    name: string;
}[];

declare const newAppointmentFormSchema: valibot.ObjectSchema<{
    readonly appointmentTypeId: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 1, "Title is required">]>;
    readonly appointmentNotes: valibot.OptionalSchema<valibot.StringSchema<string>, undefined>;
    readonly startTime: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.RegexAction<string, "Invalid date format">]>;
    readonly endTime: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.RegexAction<string, "Invalid date format">]>;
    readonly customerEmail: valibot.SchemaWithPipe<readonly [valibot.StringSchema<undefined>, valibot.EmailAction<string, "Invalid email address">]>;
    readonly clientId: valibot.SchemaWithPipe<readonly [valibot.StringSchema<undefined>, valibot.MinLengthAction<string, 1, "Client name is required">]>;
    readonly serviceId: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly customerName: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 1, "Client name is required">]>;
    readonly customerPhoneNumber: valibot.OptionalSchema<valibot.StringSchema<string>, undefined>;
    readonly locationId: valibot.StringSchema<string>;
    readonly appointmentDate: valibot.DateSchema<undefined>;
}, undefined>;
type NewAppointmentFormSchemaType = InferInput<typeof newAppointmentFormSchema>;
declare const confirmAppointmentFormSchema: valibot.ObjectSchema<{
    readonly id: valibot.StringSchema<undefined>;
    readonly status: valibot.PicklistSchema<["pending", "confirmed", "cancelled"], undefined>;
    readonly title: valibot.SchemaWithPipe<readonly [valibot.StringSchema<undefined>, valibot.MinLengthAction<string, 1, undefined>]>;
    readonly description: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly startTime: valibot.DateSchema<undefined>;
    readonly endTime: valibot.DateSchema<undefined>;
}, undefined>;
type ConfirmAppointmentFormSchemaType = InferInput<typeof confirmAppointmentFormSchema>;
declare const rescheduleAppointmentFormSchema: valibot.ObjectSchema<{
    readonly appointmentId: valibot.StringSchema<undefined>;
    readonly appointmentDate: valibot.AnySchema;
    readonly appointmentStartTime: valibot.AnySchema;
    readonly appointmentStatus: valibot.PicklistSchema<["Scheduled", "Cancelled", "Confirmed", "Done", "Missed"], undefined>;
}, undefined>;
type RescheduleAppointmentFormType = InferInput<typeof rescheduleAppointmentFormSchema>;
type DayOfWeek = 'Sunday' | 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday';
declare const createAppointmentTypeFormSchema: valibot.ObjectSchema<{
    readonly id: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly name: valibot.SchemaWithPipe<readonly [valibot.StringSchema<undefined>, valibot.MinLengthAction<string, 1, "Name is required">]>;
    readonly durationInMinutes: valibot.SchemaWithPipe<readonly [valibot.NumberSchema<undefined>, valibot.MinValueAction<number, 15, "Duration must be at least 1 minute">]>;
    readonly isActive: valibot.BooleanSchema<undefined>;
    readonly isRepeatEnabled: valibot.BooleanSchema<undefined>;
    readonly requiresUpfrontPayment: valibot.BooleanSchema<undefined>;
    readonly timeZoneId: valibot.StringSchema<undefined>;
    readonly upfrontPaymentAmount: valibot.OptionalSchema<valibot.NumberSchema<undefined>, 0>;
    readonly repeatInterval: valibot.PicklistSchema<["Daily", "Weekly", "EveryTwoWeeks", "EveryFourWeeks", "Monthly"], undefined>;
}, undefined>;
type CreateAppointmentTypeFormSchemaType = InferInput<typeof createAppointmentTypeFormSchema>;
declare const deleteAppointmentTypeFormSchema: valibot.ObjectSchema<{
    readonly id: valibot.StringSchema<undefined>;
}, undefined>;
type DeleteAppointmentTypeFormSchemaType = InferInput<typeof deleteAppointmentTypeFormSchema>;

declare const signUpCodeFormSchema: valibot.ObjectSchema<{
    readonly code: valibot.StringSchema<string>;
    readonly token: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly principal: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly email: valibot.OptionalSchema<valibot.StringSchema<string>, undefined>;
    readonly returnUrl: valibot.OptionalSchema<valibot.StringSchema<string>, "/">;
}, undefined>;
type SignUpCodeFormType = InferInput<typeof signUpCodeFormSchema>;
declare const signUpFormSchema: valibot.ObjectSchema<{
    readonly acceptTerms: valibot.BooleanSchema<undefined>;
    readonly token: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly authType: valibot.PicklistSchema<["passkey", "email", "apple", "google"], undefined>;
    readonly verificationMethod: valibot.PicklistSchema<["link", "code"], undefined>;
    readonly givenName: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 2, "First Name must have a length of at least 2 characters">, valibot.RegexAction<string, "Only alphabet characters allowed">]>;
    readonly familyName: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 2, "Last Name must have a length of at least 2 characters">, valibot.RegexAction<string, "Only alphabet characters allowed">]>;
    readonly phoneNumber: valibot.OptionalSchema<valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.RegexAction<string, "Phone number must be 10 digits">]>, undefined>;
    readonly email: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.EmailAction<string, "Entry is not a valid email">]>;
    readonly accountType: valibot.OptionalSchema<valibot.StringSchema<undefined>, "Staff">;
    readonly businessName: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 2, "Business Name must have a length of at least 2 characters">]>;
    readonly businessAddress: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 2, "Business Address must have a length of at least 2 characters">]>;
}, undefined>;
type SignUpFormType = InferInput<typeof signUpFormSchema>;
/** reset Password schema*/
declare const resetPasswordFormSchema: valibot.SchemaWithPipe<readonly [valibot.ObjectSchema<{
    readonly password: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 6, "Password must be at least 6 characters long">, valibot.RegexAction<string, "Password must contain at least one number, one uppercase letter, and one symbol">]>;
    readonly confirmPassword: valibot.StringSchema<string>;
    readonly userId: valibot.StringSchema<string>;
}, undefined>, valibot.BaseValidation<{
    password: string;
    confirmPassword: string;
    userId: string;
}, {
    password: string;
    confirmPassword: string;
    userId: string;
}, valibot.CheckIssue<{
    password: string;
    confirmPassword: string;
    userId: string;
}>>]>;
type ResetPasswordFormType = InferInput<typeof resetPasswordFormSchema>;
declare const requestPasswordResetFormSchema: valibot.ObjectSchema<{
    readonly email: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.EmailAction<string, "Entry is not a valid email">]>;
}, undefined>;
type RequestPasswordResetFormType = InferInput<typeof requestPasswordResetFormSchema>;
declare const loginFormSchema: valibot.ObjectSchema<{
    readonly email: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.EmailAction<string, "Entry is not a valid email">]>;
    readonly token: valibot.OptionalSchema<valibot.AnySchema, undefined>;
    readonly authType: valibot.PicklistSchema<["passkey", "email", "apple", "google"], undefined>;
    readonly returnUrl: valibot.OptionalSchema<valibot.StringSchema<string>, "/">;
    readonly isEmailVerified: valibot.OptionalSchema<valibot.BooleanSchema<undefined>, false>;
}, undefined>;
type LoginFormType = InferInput<typeof loginFormSchema>;
declare const logOutFormSchema: valibot.ObjectSchema<{
    readonly userId: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
}, undefined>;
type LogOutFormSchemaType = InferInput<typeof logOutFormSchema>;
declare const setPasswordFormSchema: valibot.SchemaWithPipe<readonly [valibot.ObjectSchema<{
    readonly password: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 6, "Password must be at least 6 characters long">, valibot.RegexAction<string, "Password must contain at least one number, one uppercase letter, and one symbol">]>;
    readonly confirmPassword: valibot.StringSchema<string>;
}, undefined>, valibot.BaseValidation<{
    password: string;
    confirmPassword: string;
}, {
    password: string;
    confirmPassword: string;
}, valibot.CheckIssue<{
    password: string;
    confirmPassword: string;
}>>]>;
type SetPasswordFormType = InferInput<typeof setPasswordFormSchema>;

type FormMessage = {
    status: 'error' | 'success' | 'warning';
    text: string;
    data?: any;
};
declare const formOptions: FormOptions<any, any>;
declare const lettersOnly: RegExp;
declare const companyEmailOnly: RegExp;
declare const passwordConstraint = "^(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{6,})";

declare const settingsFormSchema: valibot.ObjectSchema<{
    readonly id: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly teamName: valibot.StringSchema<undefined>;
    readonly businessEmail: valibot.StringSchema<undefined>;
    readonly phoneNumber: valibot.OptionalSchema<valibot.SchemaWithPipe<readonly [valibot.StringSchema<any>, valibot.RegexAction<string, "Phone number must be 10 digits">]>, undefined>;
    readonly businessRegNo: valibot.StringSchema<undefined>;
    readonly registrationDate: valibot.DateSchema<undefined>;
}, undefined>;
type SettingsFormSchemaType = InferInput<typeof settingsFormSchema>;
declare const staffInviteFormSchema: valibot.ObjectSchema<{
    readonly staffName: valibot.StringSchema<undefined>;
    readonly staffEmail: valibot.StringSchema<undefined>;
    readonly assignedLocation: valibot.StringSchema<undefined>;
    readonly assignedRole: valibot.StringSchema<undefined>;
    readonly gender: valibot.StringSchema<undefined>;
    readonly phoneNumber: valibot.OptionalSchema<valibot.SchemaWithPipe<readonly [valibot.StringSchema<any>, valibot.RegexAction<string, "Phone number must be 10 digits">]>, undefined>;
    readonly idDocumentNumber: valibot.StringSchema<undefined>;
    readonly dateOfBirth: valibot.StringSchema<undefined>;
    readonly address: valibot.StringSchema<undefined>;
    readonly city: valibot.StringSchema<undefined>;
    readonly postalCode: valibot.StringSchema<undefined>;
    readonly country: valibot.StringSchema<undefined>;
}, undefined>;
type StaffInviteFormSchemaType = InferInput<typeof staffInviteFormSchema>;
declare const locationFormSchema: valibot.ObjectSchema<{
    readonly id: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly locationAddress: valibot.StringSchema<undefined>;
    readonly locationEmail: valibot.SchemaWithPipe<readonly [valibot.StringSchema<any>, valibot.EmailAction<string, "Entry is not a valid email">]>;
    readonly locationPhoneNumber: valibot.OptionalSchema<valibot.SchemaWithPipe<readonly [valibot.StringSchema<any>, valibot.RegexAction<string, "Phone number must be 10 digits">]>, undefined>;
    readonly locationName: valibot.StringSchema<undefined>;
    readonly isActive: valibot.BooleanSchema<undefined>;
    readonly isDefault: valibot.OptionalSchema<valibot.BooleanSchema<undefined>, false>;
    readonly locationAdmin: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
}, undefined>;
type LocationFormSchemaType = InferInput<typeof locationFormSchema>;

declare const addTeamMemberFormSchema: valibot.ObjectSchema<{
    readonly id: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly givenName: valibot.StringSchema<undefined>;
    readonly familyName: valibot.StringSchema<undefined>;
    readonly gender: valibot.AnySchema;
    readonly email: valibot.StringSchema<undefined>;
    readonly role: valibot.StringSchema<undefined>;
    readonly location: valibot.StringSchema<undefined>;
    readonly idDocumentNumber: valibot.StringSchema<undefined>;
    readonly phoneNumber: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.RegexAction<string, "Phone number must be 10 digits">]>;
}, undefined>;
type AddTeamMemberFormSchemaType = InferInput<typeof addTeamMemberFormSchema>;

declare const bankAccountFormSchema: valibot.ObjectSchema<{
    readonly accountName: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 2, "Account name must be at least 2 characters long">, valibot.MaxLengthAction<string, 100, "Account name must be less than 100 characters">]>;
    readonly accountNumber: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 6, "Account number must be at least 6 characters long">, valibot.MaxLengthAction<string, 20, "Account number must be less than 20 characters">]>;
    readonly bankName: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 2, "Bank name must be at least 2 characters long">, valibot.MaxLengthAction<string, 100, "Bank name must be less than 100 characters">]>;
    readonly branchCode: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 4, "Branch code must be at least 4 characters long">, valibot.MaxLengthAction<string, 10, "Branch code must be less than 10 characters">]>;
    readonly swiftCode: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 8, "SWIFT code must be at least 8 characters long">, valibot.MaxLengthAction<string, 11, "SWIFT code must be less than 11 characters">]>;
}, undefined>;
type BankAccountFormSchemaType = InferInput<typeof bankAccountFormSchema>;
declare const paymentProviderFormSchema: valibot.ObjectSchema<{
    readonly id: valibot.OptionalSchema<valibot.StringSchema<string>, undefined>;
    readonly providerName: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 2, "Provider name must be at least 2 characters long">, valibot.MaxLengthAction<string, 100, "Provider name must be less than 100 characters">]>;
    readonly providerApiUrl: valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 5, "API URL must be at least 5 characters long">, valibot.MaxLengthAction<string, 255, "API URL must be less than 255 characters">]>;
    readonly providerApiKey: valibot.OptionalSchema<valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 8, "Client ID must be at least 8 characters long">, valibot.MaxLengthAction<string, 255, "Client ID must be less than 255 characters">]>, undefined>;
    readonly providerApiSecret: valibot.OptionalSchema<valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 16, "Client secret must be at least 16 characters long">, valibot.MaxLengthAction<string, 255, "Client secret must be less than 255 characters">]>, undefined>;
    readonly providerClientId: valibot.OptionalSchema<valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 16, "Client secret must be at least 16 characters long">, valibot.MaxLengthAction<string, 255, "Client secret must be less than 255 characters">]>, undefined>;
    readonly isActive: valibot.OptionalSchema<valibot.BooleanSchema<undefined>, false>;
    readonly providerWebhookUrl: valibot.OptionalSchema<valibot.SchemaWithPipe<readonly [valibot.StringSchema<string>, valibot.MinLengthAction<string, 5, "Webhook URL must be at least 5 characters long">, valibot.MaxLengthAction<string, 255, "Webhook URL must be less than 255 characters">]>, undefined>;
}, undefined>;
type PaymentProviderFormSchemaType = InferInput<typeof paymentProviderFormSchema>;

declare const userProfileSchema: valibot.ObjectSchema<{
    readonly givenName: valibot.StringSchema<"First name is required">;
    readonly familyName: valibot.StringSchema<"Last name is required">;
    readonly name: valibot.StringSchema<undefined>;
    readonly accountType: valibot.StringSchema<"Account type is required">;
    readonly profilePhotoUrl: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
}, undefined>;
declare const teamLocationSchema: valibot.ObjectSchema<{
    readonly id: valibot.StringSchema<undefined>;
    readonly locationName: valibot.StringSchema<"Location name is required">;
    readonly locationAddress: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly locationEmail: valibot.SchemaWithPipe<readonly [valibot.StringSchema<undefined>, valibot.EmailAction<string, "Invalid email format">]>;
    readonly locationPhoneNumber: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly isActive: valibot.BooleanSchema<undefined>;
    readonly isDefault: valibot.OptionalSchema<valibot.BooleanSchema<undefined>, undefined>;
    readonly teamLocationAdmin: valibot.AnySchema;
}, undefined>;
declare const memberInviteSchema: valibot.ObjectSchema<{
    readonly inviteeEmail: valibot.SchemaWithPipe<readonly [valibot.StringSchema<undefined>, valibot.EmailAction<string, "Invalid email format">]>;
    readonly role: valibot.PicklistSchema<("team-admin" | "team-member" | "team-location-admin")[], "Invalid role selection">;
    readonly teamLocation: valibot.ObjectSchema<{
        readonly id: valibot.StringSchema<undefined>;
        readonly locationName: valibot.StringSchema<"Location name is required">;
        readonly locationAddress: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
        readonly locationEmail: valibot.SchemaWithPipe<readonly [valibot.StringSchema<undefined>, valibot.EmailAction<string, "Invalid email format">]>;
        readonly locationPhoneNumber: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
        readonly isActive: valibot.BooleanSchema<undefined>;
        readonly isDefault: valibot.OptionalSchema<valibot.BooleanSchema<undefined>, undefined>;
        readonly teamLocationAdmin: valibot.AnySchema;
    }, undefined>;
    readonly invitedBy: valibot.AnySchema;
    readonly invitedAt: valibot.DateSchema<undefined>;
    readonly status: valibot.PicklistSchema<("Active" | "Inactive" | "On Leave" | "Resigned")[], "Invalid status">;
    readonly expiresAt: valibot.DateSchema<undefined>;
}, undefined>;
declare const teamSchema: valibot.ObjectSchema<{
    readonly teamName: valibot.StringSchema<"Team name is required">;
    readonly businessEmail: valibot.SchemaWithPipe<readonly [valibot.StringSchema<undefined>, valibot.EmailAction<string, "Invalid email format">]>;
    readonly businessRegNo: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly phoneNumber: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly createdAt: valibot.DateSchema<undefined>;
}, undefined>;
declare const userRootSchema: valibot.ObjectSchema<{
    readonly email: valibot.SchemaWithPipe<readonly [valibot.StringSchema<undefined>, valibot.EmailAction<string, "Invalid email format">]>;
    readonly phoneNumber: valibot.StringSchema<"Phone number is required">;
    readonly nationalId: valibot.StringSchema<"National ID is required">;
    readonly dateOfBirth: valibot.DateSchema<"Date of birth is required">;
    readonly gender: valibot.PicklistSchema<("Male" | "Female" | "Other")[], "Invalid gender selection">;
    readonly address: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly city: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly country: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    readonly team: valibot.ObjectSchema<{
        readonly teamName: valibot.StringSchema<"Team name is required">;
        readonly businessEmail: valibot.SchemaWithPipe<readonly [valibot.StringSchema<undefined>, valibot.EmailAction<string, "Invalid email format">]>;
        readonly businessRegNo: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
        readonly phoneNumber: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
        readonly createdAt: valibot.DateSchema<undefined>;
    }, undefined>;
    readonly givenName: valibot.StringSchema<"First name is required">;
    readonly familyName: valibot.StringSchema<"Last name is required">;
    readonly name: valibot.StringSchema<undefined>;
    readonly accountType: valibot.StringSchema<"Account type is required">;
    readonly profilePhotoUrl: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
}, undefined>;
declare const teamMemberSchema: valibot.ObjectSchema<{
    readonly userAccount: valibot.ObjectSchema<{
        readonly email: valibot.SchemaWithPipe<readonly [valibot.StringSchema<undefined>, valibot.EmailAction<string, "Invalid email format">]>;
        readonly phoneNumber: valibot.StringSchema<"Phone number is required">;
        readonly nationalId: valibot.StringSchema<"National ID is required">;
        readonly dateOfBirth: valibot.DateSchema<"Date of birth is required">;
        readonly gender: valibot.PicklistSchema<("Male" | "Female" | "Other")[], "Invalid gender selection">;
        readonly address: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
        readonly city: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
        readonly country: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
        readonly team: valibot.ObjectSchema<{
            readonly teamName: valibot.StringSchema<"Team name is required">;
            readonly businessEmail: valibot.SchemaWithPipe<readonly [valibot.StringSchema<undefined>, valibot.EmailAction<string, "Invalid email format">]>;
            readonly businessRegNo: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
            readonly phoneNumber: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
            readonly createdAt: valibot.DateSchema<undefined>;
        }, undefined>;
        readonly givenName: valibot.StringSchema<"First name is required">;
        readonly familyName: valibot.StringSchema<"Last name is required">;
        readonly name: valibot.StringSchema<undefined>;
        readonly accountType: valibot.StringSchema<"Account type is required">;
        readonly profilePhotoUrl: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
    }, undefined>;
    readonly role: valibot.PicklistSchema<("team-admin" | "team-member" | "team-location-admin")[], "Invalid role selection">;
    readonly location: valibot.ObjectSchema<{
        readonly id: valibot.StringSchema<undefined>;
        readonly locationName: valibot.StringSchema<"Location name is required">;
        readonly locationAddress: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
        readonly locationEmail: valibot.SchemaWithPipe<readonly [valibot.StringSchema<undefined>, valibot.EmailAction<string, "Invalid email format">]>;
        readonly locationPhoneNumber: valibot.OptionalSchema<valibot.StringSchema<undefined>, undefined>;
        readonly isActive: valibot.BooleanSchema<undefined>;
        readonly isDefault: valibot.OptionalSchema<valibot.BooleanSchema<undefined>, undefined>;
        readonly teamLocationAdmin: valibot.AnySchema;
    }, undefined>;
    readonly joinedAt: valibot.DateSchema<undefined>;
    readonly lastActive: valibot.OptionalSchema<valibot.DateSchema<undefined>, undefined>;
    readonly memberStatus: valibot.PicklistSchema<("Active" | "Inactive" | "On Leave" | "Resigned")[], "Invalid status">;
}, undefined>;
type UserRootType = InferInput<typeof userRootSchema>;
type UserProfileType = InferInput<typeof userProfileSchema>;
type TeamLocationType = InferInput<typeof teamLocationSchema>;
type TeamMemberType = InferInput<typeof teamMemberSchema>;
type MemberInviteType = InferInput<typeof memberInviteSchema>;
type TeamType = InferInput<typeof teamSchema>;

declare const timeGreeting: () => string;
declare const getRandomColorName: () => string;
declare const getCopyRight: () => string;
declare const getInitials: (firstName: string, surname: string) => string;
declare const getStringInitials: (fullString: string) => string;
declare const capitalizeFirstLetter: (input: string) => string;
declare const getDomain: (email: string) => string;
declare const greetings: (displayName: string) => string;
declare const displayCurrency: (_entity: any, value: number | undefined) => string;
declare const displayCurrencyK: (_entity: any, value: number | undefined) => string;
declare const stripUrl: (url: string) => string;
declare const buildGravatarUrl: (email: string) => string;
declare const getKeyByValue: (obj: object, value: any) => string | undefined;
declare const saveEmailLocally: (email: string) => void;
declare const getLocalEmail: () => string | null;
declare const formatTimeWithAmPm: (timeStr: string) => string;
declare const initSuperJson: () => void;
declare const objectToText: (obj: any) => string;
declare const classToPlain: <T extends object>(instance: T) => Partial<T>;
declare const hasPermission: (authData: IAuthAppData, { userId, teamId, teamLocationId }: {
    userId: string;
    teamId: string;
    teamLocationId: string;
}) => {
    isTeamAdmin: boolean;
    isTeamMember: boolean;
    isOwner: any;
    isLocationAdmin: boolean;
};
declare const isTeamAdmin: (authData: any, teamId: any) => boolean;
declare const isTeamMember: (authData: any, teamId: any) => boolean;
declare const isOwner: (authData: any, userId: string) => any;
declare const isTeamLocationAdmin: (authData: any, teamLocationId: any) => boolean;

interface Operation {
    name: string;
    when?: (params: any) => Promise<boolean> | boolean;
}
interface RoleDefinition$1 {
    can: (string | Operation)[];
    inherits?: string[];
}
type RoleMap$1 = Record<string, RoleDefinition$1>;
declare class RBAC {
    private _inited;
    private _init?;
    private roles;
    constructor(roles: RoleMap$1 | (() => Promise<RoleMap$1>) | Promise<RoleMap$1>);
    private _parseRoleMap;
    private asyncInit;
    can(role: string | string[], operation: string, params?: any): Promise<boolean>;
    static create(opts: RoleMap$1 | (() => Promise<RoleMap$1>) | Promise<RoleMap$1>): RBAC;
}

interface paramsType {
    payload: any;
    authData: IAuthAppData;
}
declare const ActionTypes: {
    readonly readTeam: "read:team";
    readonly manageTeam: "manage:team";
    readonly readmembers: "read:members";
    readonly manageMembers: "manage:members";
    readonly readAppointments: "read:appointments";
    readonly manageAppointments: "manage:appointments";
    readonly readAppointmentTypes: "read:appointmentTypes";
    readonly manageAppointmentTypes: "manage:appointmentTypes";
    readonly readTeamLocations: "read:teamLocations";
    readonly manageTeamLocations: "manage:teamLocations";
    readonly readTeamBankDetails: "read:teamBankDetails";
    readonly manageTeamBankDetails: "manage:teamBankDetails";
    readonly readTeamPaymentProviders: "read:teamPaymentProviders";
    readonly manageTeamPaymentProviders: "manage:teamPaymentProviders";
    readonly readTeamWorkdaySettings: "read:teamWorkdaySettings";
    readonly manageTeamWorkdaySettings: "manage:teamWorkdaySettings";
    readonly readTeamMembers: "read:teamMembers";
    readonly manageTeamMembers: "manage:teamMembers";
    readonly readTeamMemberInvites: "read:teamMemberInvites";
    readonly manageTeamMemberInvites: "manage:teamMemberInvites";
    readonly readCalendarBlocks: "read:calendarBlocks";
    readonly manageCalendarBlocks: "manage:calendarBlocks";
    readonly readStorageBuckets: "read:storageBuckets";
    readonly manageStorageBuckets: "manage:storageBuckets";
    readonly readStorageFiles: "read:storageFiles";
    readonly manageStorageFiles: "manage:storageFiles";
    readonly readUserRoles: "read:userRoles";
    readonly manageUserRoles: "manage:userRoles";
    readonly readRoles: "read:roles";
    readonly manageRoled: "manage:roles";
    readonly readTimeZones: "read:timeZones";
    readonly manageTimeZones: "manage:timeZones";
    readonly all: "*";
    readonly allTeams: "team:*";
    readonly allTeamLocations: "team:location:*";
    readonly allMembers: "team:member:*";
    readonly allBankDetails: "team:bankDetail:*";
    readonly allAppointments: "team:appointment:*";
    readonly allAppointmentTypes: "team:appointmentType:*";
    readonly allPaymentProviders: "team:paymentProvider:*";
    readonly allWorkdaySettings: "team:workdaySetting:*";
    readonly allMemberInvites: "team:memberInvite:*";
    readonly allCalendarBlocks: "team:calendarBlock:*";
    readonly allStorageBuckets: "team:storageBucket:*";
    readonly allStorageFiles: "team:storageFile:*";
    readonly allUserRoles: "team:userRole:*";
    readonly allRoles: "team:role:*";
    readonly allTimeZones: "team:timeZone:*";
};
type ActionType = (typeof ActionTypes)[keyof typeof ActionTypes];
interface RoleDefinition {
    can: (string | {
        name: string;
        when?: (params: any) => Promise<boolean> | boolean;
    })[];
    inherits?: string[];
}
type RoleMap = Record<string, RoleDefinition>;
declare const rbac: RBAC;
declare const checkPermission: (role: string | string[], operation: string, params?: any) => Promise<boolean>;

type TimeSpanUnit = 'ms' | 's' | 'm' | 'h' | 'd' | 'w';
declare class TimeSpan {
    constructor(value: number, unit: TimeSpanUnit);
    value: number;
    unit: TimeSpanUnit;
    milliseconds(): number;
    seconds(): number;
    transform(x: number): TimeSpan;
}
declare function isWithinExpirationDate(date: Date): boolean;
declare function createDate(timeSpan: TimeSpan): Date;
type TypedArray = Uint8Array | Int8Array | Uint16Array | Int16Array | Uint32Array | Int32Array | Float32Array | Float64Array | BigInt64Array | BigUint64Array;

type Success<T> = {
    data: T;
    error: null;
};
type Failure<E> = {
    data: null;
    error: E;
};
type Result<T, E = Error> = Success<T> | Failure<E>;
declare function tryCatch<T, E = Error>(promise: Promise<T>): Promise<Result<T, E>>;

export { ActionTypes, ActorEvents, AppUserMetadataSchema, AppUserSchema, AuthAppDataSchema, CalendarViewTypes, Gender, Genders, InboxMessageTypes, Permissions, Result$1 as Result, Roles, SheetTypes, TIMEZONES, TeamLocationStatuses, TeamMemberStatuses, TeamPlans, TeamRoles, TimeSpan, ToastTypeSchema, UserAccountTypes, WorkDays, addTeamMemberFormSchema, appointmentRepeatTypes, appointmentStatuses, bankAccountFormSchema, buildGravatarUrl, capitalizeFirstLetter, checkPermission, classToPlain, companyEmailOnly, confirmAppointmentFormSchema, createAppointmentTypeFormSchema, createDate, deleteAppointmentTypeFormSchema, displayCurrency, displayCurrencyK, dynamicModalSchema, dynamicSheetSchema, formOptions, formatTimeWithAmPm, genderListData, getCopyRight, getDomain, getInitials, getKeyByValue, getLocalEmail, getRandomColorName, getStringInitials, greetings, hasPermission, initSuperJson, isOwner, isTeamAdmin, isTeamLocationAdmin, isTeamMember, isWithinExpirationDate, lettersOnly, locationFormSchema, logOutFormSchema, loginFormSchema, memberInviteSchema, newAppointmentFormSchema, objectToText, passwordConstraint, paymentProviderFormSchema, rbac, requestPasswordResetFormSchema, rescheduleAppointmentFormSchema, resetPasswordFormSchema, saveEmailLocally, setPasswordFormSchema, settingsFormSchema, signUpCodeFormSchema, signUpFormSchema, staffInviteFormSchema, stripUrl, teamLocationSchema, teamMemberSchema, teamSchema, timeGreeting, tryCatch, userProfileSchema, userRolesData, userRootSchema };
export type { ActionType, ActorEvent, AddTeamMemberFormSchemaType, AppUserInfo, AppointmentRepeatType, AppointmentStatus, BankAccountFormSchemaType, CalendarViewType, ConfirmAppointmentFormSchemaType, CreateAppointmentTypeFormSchemaType, DayOfWeek, DeleteAppointmentTypeFormSchemaType, FormMessage, GenderType, IAppUserMetadata, IAuthAppData, IComponentCache, IDynamicModal, IDynamicSheet, ISessionData, InboxMessageType, JWTClaims, LocationFormSchemaType, LogOutFormSchemaType, LoginFormType, MemberInviteType, NewAppointmentFormSchemaType, PaymentProviderFormSchemaType, Permission, Prettify, RequestPasswordResetFormType, RescheduleAppointmentFormType, ResetPasswordFormType, Role, RoleDefinition, RoleMap, SetPasswordFormType, SettingsFormSchemaType, SheetType, SignUpCodeFormType, SignUpFormType, StaffInviteFormSchemaType, TeamLocationStatus, TeamLocationType, TeamMemberStatus, TeamMemberType, TeamPlan, TeamType, TimeSpanUnit, TimeZone, ToastType, TypedArray, Updateable, UserProfileType, UserRootType, paramsType };
