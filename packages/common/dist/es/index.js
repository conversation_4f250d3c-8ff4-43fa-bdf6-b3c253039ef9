import { getHours, getYear } from 'date-fns';
import { sha256 } from '@oslojs/crypto/sha2';
import { Decimal } from 'decimal.js';
import SuperJSON, { stringify } from 'superjson';
import debugModule from 'debug';
import { object, picklist, optional, string, boolean, unknown, any as any$1, pipe, regex, date, minLength, email, number, minValue, forward, check, maxLength } from 'valibot';

const timeGreeting = ()=>{
    let time = 'morning';
    const today = new Date();
    const currentHour = getHours(today);
    if (currentHour >= 12 && currentHour < 18) {
        time = 'afternoon';
    } else if (currentHour >= 18) {
        time = 'evening';
    }
    return `Good ${time}`;
};
const getRandomColorName = ()=>{
    const colors = [
        'bg-red-900',
        'bg-orange-900',
        'bg-yellow-900',
        'bg-green-900',
        'bg-indigo-900',
        'bg-violet-900',
        'bg-pink-900',
        'bg-brown-900',
        'bg-gray-900',
        'bg-black-900',
        'bg-cyan-900',
        'bg-teal-900',
        'bg-lime-900',
        'bg-amber-900',
        'bg-purple-900'
    ];
    const randomIndex = Math.floor(Math.random() * colors.length);
    return colors[randomIndex];
};
const getCopyRight = ()=>{
    const today = new Date();
    return `© ${getYear(today)} SpenDeed.AI. All rights reserved.`;
};
const getInitials = (firstName, surname)=>{
    return `${firstName?.substring(0, 1)?.toUpperCase()} ${surname?.substring(0, 1)?.toUpperCase()}`;
};
const getStringInitials = (fullString)=>{
    return fullString ? fullString.split(' ').map((name)=>name.charAt(0).toUpperCase()).join('') : 'NA';
};
const capitalizeFirstLetter = (input)=>{
    if (input.length === 0) {
        return input;
    }
    return input.charAt(0).toUpperCase() + input.slice(1).toLowerCase();
};
const getDomain = (email)=>{
    const atIndex = email.indexOf('@');
    if (atIndex === -1) {
        throw new Error('Invalid email address');
    }
    return email.slice(atIndex + 1);
};
const greetings = (displayName)=>{
    return `${timeGreeting()}  ${displayName ? displayName : 'Anonymous'}!`;
};
//1 per module
const displayCurrency = (_entity, value)=>{
    if (value === undefined) return '- €';
    return new Intl.NumberFormat('fr', {
        currency: 'EUR',
        style: 'currency'
    }).format(value ?? 0);
};
const displayCurrencyK = (_entity, value)=>{
    if (value === undefined) return '- €';
    if (Math.abs(value) < 1000) {
        return displayCurrency(_entity, value);
    }
    return new Intl.NumberFormat('fr', {
        currency: 'EUR',
        style: 'currency',
        maximumFractionDigits: 0
    }).format(value / 1000).replace('€', 'k€');
};
const stripUrl = (url)=>{
    let result = url;
    // Keep replacing until no more parentheses are found
    while(result.includes('(')){
        result = result.replace(/\([^()]*\)/g, '');
    }
    // Remove any duplicate slashes that might have been created
    result = result.replace(/\/+/g, '/');
    // Remove trailing slash if it wasn't in the original URL
    if (!url.endsWith('/') && result.endsWith('/')) {
        result = result.slice(0, -1);
    }
    return result;
};
const buildGravatarUrl = (email)=>{
    const content = new TextEncoder().encode(email.trim().toLowerCase());
    const hashedEmail = sha256(content);
    return `https://gravatar.com/avatar/${hashedEmail}?d=wavatar`;
};
const getKeyByValue = (obj, value)=>{
    return Object.keys(obj).find((key)=>obj[key] === value);
};
const saveEmailLocally = (email)=>localStorage.setItem('email', email);
const getLocalEmail = ()=>localStorage.getItem('email');
const formatTimeWithAmPm = (timeStr)=>{
    const [hours, minutes] = timeStr.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes);
    return date.toLocaleString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    }).toLowerCase();
};
const initSuperJson = ()=>{
    SuperJSON.registerCustom({
        isApplicable: (v)=>Decimal.isDecimal(v),
        serialize: (v)=>v.toJSON(),
        deserialize: (v)=>new Decimal(v)
    }, 'decimal.js');
};
const objectToText = (obj)=>{
    return Object.entries(obj).map(([key, value])=>`${key}: ${value}`).join(' | ');
};
const classToPlain = (instance)=>{
    const result = {};
    for (const key of Object.keys(instance)){
        result[key] = instance[key];
    }
    return result;
};
const hasPermission = (authData, { userId, teamId, teamLocationId })=>{
    return {
        isTeamAdmin: isTeamAdmin(authData, teamId),
        isTeamMember: isTeamMember(authData, teamId),
        isOwner: isOwner(authData, userId),
        isLocationAdmin: isTeamLocationAdmin(authData, teamLocationId)
    };
};
const isTeamAdmin = (authData, teamId)=>{
    return !!authData?.isTeamAdmin && teamId === authData?.teamId;
};
const isTeamMember = (authData, teamId)=>{
    return !!authData?.isTeamMember && teamId === authData?.teamId;
};
const isOwner = (authData, userId)=>{
    return authData.isTeamMember && authData?.userID === userId;
};
const isTeamLocationAdmin = (authData, teamLocationId)=>{
    return !!authData?.isTeamAdmin && teamLocationId === authData?.teamLocationId;
};

function any(promises) {
    return Promise.all(promises.map((p)=>p.catch((e)=>e))).then((results)=>{
        const validResults = results.filter((result)=>!(result instanceof Error));
        if (validResults.some((result)=>result === true)) {
            return true;
        }
        return false;
    });
}
function isGlob(str) {
    return str.includes('*');
}
function globToRegex(str) {
    const escaped = str.replace(/[-\/\\^$+?.()|[\]{}]/g, '\\$&');
    const globbed = escaped.replace(/\*/g, '.*');
    return new RegExp(`^${globbed}$`);
}

const debug = new debugModule('rbac');
class RBAC {
    constructor(roles){
        this._inited = false;
        if (typeof roles !== 'function' && typeof roles.then !== 'function') {
            debug('sync init');
            // Add roles to class and mark as inited
            this.roles = this._parseRoleMap(roles);
            this._inited = true;
        } else {
            debug('async init');
            this._init = this.asyncInit(roles);
        }
    }
    _parseRoleMap(roles) {
        debug('parsing rolemap');
        // If not a function then should be object
        if (typeof roles !== 'object') {
            throw new TypeError('Expected input to be object');
        }
        const map = new Map();
        // Standardize roles
        Object.keys(roles).forEach((role)=>{
            const roleObj = {
                can: {},
                canGlob: []
            };
            // Check can definition
            if (!Array.isArray(roles[role].can)) {
                throw new TypeError('Expected roles[' + role + '].can to be an array');
            }
            if (roles[role].inherits) {
                if (!Array.isArray(roles[role].inherits)) {
                    throw new TypeError('Expected roles[' + role + '].inherits to be an array');
                }
                roleObj.inherits = [];
                roles[role].inherits.forEach((child)=>{
                    if (typeof child !== 'string') {
                        throw new TypeError('Expected roles[' + role + '].inherits element');
                    }
                    if (!roles[child]) {
                        throw new TypeError('Undefined inheritance role: ' + child);
                    }
                    roleObj.inherits.push(child);
                });
            }
            // Iterate allowed operations
            roles[role].can.forEach((operation)=>{
                // If operation is string
                if (typeof operation === 'string') {
                    // Add as an operation
                    if (!isGlob(operation)) {
                        roleObj.can[operation] = 1;
                    } else {
                        roleObj.canGlob.push({
                            name: globToRegex(operation),
                            original: operation
                        });
                    }
                    return;
                }
                // Check if operation has a .when function
                if (typeof operation.when === 'function' && typeof operation.name === 'string') {
                    const op = operation;
                    if (!isGlob(op.name)) {
                        roleObj.can[op.name] = op.when;
                    } else {
                        roleObj.canGlob.push({
                            name: globToRegex(op.name),
                            original: op.name,
                            when: op.when
                        });
                    }
                    return;
                }
                throw new TypeError('Unexpected operation type');
            });
            map.set(role, roleObj);
        });
        return map;
    }
    async asyncInit(roles) {
        // If opts is a function execute for async loading
        let resolvedRoles;
        if (typeof roles === 'function') {
            resolvedRoles = await roles();
        } else {
            resolvedRoles = await roles;
        }
        // Add roles to class and mark as inited
        this.roles = this._parseRoleMap(resolvedRoles);
        this._inited = true;
    }
    async can(role, operation, params) {
        // If not inited then wait until init finishes
        if (!this._inited) {
            debug('Not inited, wait');
            await this._init;
            debug('Init complete, continue');
        }
        if (Array.isArray(role)) {
            debug('array of roles, try all');
            return any(role.map((r)=>this.can(r, operation, params)));
        }
        if (typeof role !== 'string') {
            debug('Expected first parameter to be string : role');
            return false;
        }
        if (typeof operation !== 'string') {
            debug('Expected second parameter to be string : operation');
            return false;
        }
        const $role = this.roles.get(role);
        if (!$role) {
            debug('Undefined role');
            return false;
        }
        // IF this operation is not defined at current level try higher
        if (!$role.can[operation] && !$role.canGlob.find((glob)=>glob.name.test(operation))) {
            debug('Not allowed at this level, try higher');
            // If no parents reject
            if (!$role.inherits || $role.inherits.length < 1) {
                debug('No inherit, reject false');
                return false;
            }
            // Return if any parent resolves true or all reject
            return any($role.inherits.map((parent)=>{
                debug('Try from ' + parent);
                return this.can(parent, operation, params);
            }));
        }
        // We have the operation resolve
        if ($role.can[operation] === 1) {
            debug('We have a match, resolve');
            return true;
        }
        // Operation is conditional, run async function
        if (typeof $role.can[operation] === 'function') {
            debug('Operation is conditional, run fn');
            try {
                return $role.can[operation](params);
            } catch (e) {
                debug('conditional function threw', e);
                return false;
            }
        }
        // Try globs
        const globMatch = $role.canGlob.find((glob)=>glob.name.test(operation));
        if (globMatch && !globMatch.when) {
            debug(`We have a globmatch (${globMatch.original}), resolve`);
            return true;
        }
        if (globMatch && globMatch.when) {
            debug(`We have a conditional globmatch (${globMatch.original}), run fn`);
            try {
                return globMatch.when(params);
            } catch (e) {
                debug('conditional function threw', e);
                return false;
            }
        }
        // No operation reject as false
        debug('Shouldnt have reached here, something wrong, reject');
        throw new Error('something went wrong');
    }
    // Static factory method
    static create(opts) {
        return new RBAC(opts);
    }
}

const ActionTypes = {
    readTeam: 'read:team',
    manageTeam: 'manage:team',
    readmembers: 'read:members',
    manageMembers: 'manage:members',
    readAppointments: 'read:appointments',
    manageAppointments: 'manage:appointments',
    readAppointmentTypes: 'read:appointmentTypes',
    manageAppointmentTypes: 'manage:appointmentTypes',
    readTeamLocations: 'read:teamLocations',
    manageTeamLocations: 'manage:teamLocations',
    readTeamBankDetails: 'read:teamBankDetails',
    manageTeamBankDetails: 'manage:teamBankDetails',
    readTeamPaymentProviders: 'read:teamPaymentProviders',
    manageTeamPaymentProviders: 'manage:teamPaymentProviders',
    readTeamWorkdaySettings: 'read:teamWorkdaySettings',
    manageTeamWorkdaySettings: 'manage:teamWorkdaySettings',
    readTeamMembers: 'read:teamMembers',
    manageTeamMembers: 'manage:teamMembers',
    readTeamMemberInvites: 'read:teamMemberInvites',
    manageTeamMemberInvites: 'manage:teamMemberInvites',
    readCalendarBlocks: 'read:calendarBlocks',
    manageCalendarBlocks: 'manage:calendarBlocks',
    readStorageBuckets: 'read:storageBuckets',
    manageStorageBuckets: 'manage:storageBuckets',
    readStorageFiles: 'read:storageFiles',
    manageStorageFiles: 'manage:storageFiles',
    readUserRoles: 'read:userRoles',
    manageUserRoles: 'manage:userRoles',
    readRoles: 'read:roles',
    manageRoled: 'manage:roles',
    readTimeZones: 'read:timeZones',
    manageTimeZones: 'manage:timeZones',
    all: '*',
    allTeams: 'team:*',
    allTeamLocations: 'team:location:*',
    allMembers: 'team:member:*',
    allBankDetails: 'team:bankDetail:*',
    allAppointments: 'team:appointment:*',
    allAppointmentTypes: 'team:appointmentType:*',
    allPaymentProviders: 'team:paymentProvider:*',
    allWorkdaySettings: 'team:workdaySetting:*',
    allMemberInvites: 'team:memberInvite:*',
    allCalendarBlocks: 'team:calendarBlock:*',
    allStorageBuckets: 'team:storageBucket:*',
    allStorageFiles: 'team:storageFile:*',
    allUserRoles: 'team:userRole:*',
    allRoles: 'team:role:*',
    allTimeZones: 'team:timeZone:*'
};
const opts = {
    Admin: {
        can: [
            ActionTypes.all
        ]
    },
    TeamMember: {
        can: [
            {
                name: ActionTypes.readTeam,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.readmembers,
                when: (params)=>{
                    return params.payload.userId === params.authData.userId;
                }
            },
            {
                name: ActionTypes.readAppointments,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.manageAppointments,
                when: (params)=>{
                    return params.payload.locationId === params.authData.teamLocationId;
                }
            },
            {
                name: ActionTypes.readAppointmentTypes,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.readTeamLocations,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.manageAppointmentTypes,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.readTeamBankDetails,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.readTeamPaymentProviders,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.readTeamWorkdaySettings,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.readTeamMembers,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.readCalendarBlocks,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.readStorageBuckets,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.readStorageFiles,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.readUserRoles,
                when: (params)=>{
                    return params.payload.userId === params.authData.userId;
                }
            },
            ActionTypes.readRoles,
            ActionTypes.readTimeZones
        ]
    },
    TeamAdmin: {
        can: [
            {
                name: ActionTypes.manageTeam,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.readmembers,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.manageMembers,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.manageAppointments,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.manageAppointmentTypes,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.allTeamLocations,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.allBankDetails,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.allPaymentProviders,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.allWorkdaySettings,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.allMemberInvites,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.allCalendarBlocks,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.allStorageBuckets,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.allStorageFiles,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            {
                name: ActionTypes.allUserRoles,
                when: (params)=>{
                    return params.payload.teamId === params.authData.teamId;
                }
            },
            ActionTypes.readRoles,
            ActionTypes.readTimeZones
        ],
        inherits: [
            'TeamMember'
        ]
    },
    TeamLocationAdmin: {
        can: [
            {
                name: 'manage:appointments',
                when: (params)=>{
                    return params.payload.teamLocationId === params.authData.teamLocationId;
                }
            },
            {
                name: 'manage:teamWorkdaySettings',
                when: (params)=>{
                    return params.payload.teamLocationId === params.authData.teamLocationId;
                }
            },
            {
                name: 'read:teamMembers',
                when: (params)=>{
                    return params.payload.teamLocationId === params.authData.teamLocationId;
                }
            },
            {
                name: 'manage:calendarBlocks',
                when: (params)=>{
                    return params.payload.teamLocationId === params.authData.teamLocationId;
                }
            }
        ],
        inherits: [
            'TeamMember'
        ]
    }
};
const rbac = new RBAC(opts);
// Use the RBAC instance
const checkPermission = async (role, operation, params)=>{
    return await rbac.can(role, operation, params);
};

class TimeSpan {
    constructor(value, unit){
        this.value = value;
        this.unit = unit;
    }
    milliseconds() {
        if (this.unit === 'ms') {
            return this.value;
        }
        if (this.unit === 's') {
            return this.value * 1000;
        }
        if (this.unit === 'm') {
            return this.value * 1000 * 60;
        }
        if (this.unit === 'h') {
            return this.value * 1000 * 60 * 60;
        }
        if (this.unit === 'd') {
            return this.value * 1000 * 60 * 60 * 24;
        }
        return this.value * 1000 * 60 * 60 * 24 * 7;
    }
    seconds() {
        return this.milliseconds() / 1000;
    }
    transform(x) {
        return new TimeSpan(Math.round(this.milliseconds() * x), 'ms');
    }
}
function isWithinExpirationDate(date) {
    return Date.now() < date.getTime();
}
function createDate(timeSpan) {
    return new Date(Date.now() + timeSpan.milliseconds());
}

// Main wrapper function
async function tryCatch(promise) {
    try {
        const data = await promise;
        return {
            data,
            error: null
        };
    } catch (error) {
        console.error(`try catch error: ${error?.message}`);
        console.error(`try catch error: ${error?.stack}`);
        return {
            data: null,
            error: error
        };
    }
}

const dynamicModalSchema = object({
    title: string('Required'),
    description: string('Required'),
    componentName: string('Required'),
    componentProps: optional(unknown()),
    canClose: optional(boolean('Required'), true),
    color: picklist([
        'primary',
        'gray',
        'red',
        'yellow',
        'green',
        'indigo',
        'purple',
        'pink',
        'blue',
        'light',
        'dark',
        'default',
        'dropdown',
        'navbar',
        'navbarUl',
        'form',
        'none'
    ]),
    size: picklist([
        'xl',
        'lg',
        'md'
    ])
});
const ToastTypeSchema = picklist([
    'success',
    'error',
    'info',
    'warning'
]);
const dynamicSheetSchema = object({
    ...dynamicModalSchema.entries,
    position: picklist([
        'left',
        'right',
        'top',
        'bottom'
    ]),
    side: picklist([
        'right',
        'top',
        'bottom',
        'left'
    ])
});
const SheetTypes = {
    Marketing: 'Marketing',
    SignUpSuccess: 'SignUpSuccess',
    Login: 'Login',
    SignUp: 'SignUp',
    SignUpCode: 'SignUpCode',
    ForgotPass: 'ForgotPass',
    ResetPass: 'ResetPass',
    ResetPassCode: 'ResetPassCode',
    LoginCode: 'LoginCode'
};
const CalendarViewTypes = {
    Day: 'Day',
    Week: 'Week',
    Month: 'Month'
};
const InboxMessageTypes = {
    VerifyEmailDomain: 'VerifyEmailDomain'
};
const TeamMemberStatuses = {
    Active: 'Active',
    InActive: 'Inactive',
    OnLeave: 'On Leave',
    Resigned: 'Resigned'
};
const ActorEvents = {
    ItemCreated: 'item.created',
    ItemUpdated: 'item.updated',
    ItemDeleted: 'item.deleted'
};

const appointmentRepeatTypes = {
    Daily: 'Daily',
    Weekly: 'Weekly',
    EveryTwoWeeks: 'EveryTwoWeeks',
    EveryFourWeeks: 'EveryFourWeeks',
    Monthly: 'Monthly'
};
const appointmentStatuses = {
    Scheduled: 'Scheduled',
    Confirmed: 'Confirmed',
    Cancelled: 'Cancelled',
    Done: 'Done',
    Missed: 'Missed'
};

const stringVal$1 = (name)=>{
    return {
        message: `${name} must be a string`
    };
};

const Roles = {
    User: 'user',
    Admin: 'Admin',
    TeamAdmin: 'team-admin',
    TeamMember: 'team-member',
    TeamLocationAdmin: 'team-location-admin'
};
const Genders = {
    Male: 'Male',
    Female: 'Female',
    Other: 'Other'
};
const Permissions = {
    ManageTeam: 'Manage Team',
    ReadTeam: 'Read Team',
    ReadMembers: 'Read Members',
    ManageMembers: 'Manage Members',
    Contribute: 'Contribute',
    ReadAppointments: 'Read Appointments',
    ManageAppointments: 'Manage Appointments',
    ManageAppointmentTypes: 'Manage Appointment Types',
    ReadAppointmentTypes: 'Read Appointment Types'
};
const AuthAppDataSchema = object({
    teamId: optional(string()),
    defaultRole: optional(string(), Roles.TeamMember),
    teamLocationId: optional(string()),
    allowedRoles: optional(any$1(), [
        Roles.TeamMember
    ])
});
const AppUserMetadataSchema = object({
    givenName: string(),
    familyName: string(),
    email: string(),
    teamId: string(),
    teamLocationId: string(),
    businessName: optional(string()),
    phoneNumber: string(),
    userStatus: optional(string(), TeamMemberStatuses.Active),
    idDocumentNumber: optional(string()),
    gender: optional(string(), Genders.Male),
    dateOfBirth: optional(string()),
    avatarUrl: string()
});
const AppUserSchema = object({
    id: string(),
    email: string(),
    givenName: string(),
    familyName: string(),
    initials: optional(string(), ''),
    gender: optional(any$1()),
    isUserEmailVerified: optional(boolean(), false),
    phoneNumber: optional(pipe(string(stringVal$1('Phone Number')), regex(/^[0-9]{10}$/, 'Phone number must be 10 digits'))),
    isPhoneNumberVerified: optional(boolean(), false),
    idDocumentNumber: optional(string()),
    isEnabled: boolean(),
    profilePhotoUrl: string(),
    status: optional(string(), TeamMemberStatuses.Active),
    teamId: string(),
    defaultRole: optional(string(), Roles.TeamMember),
    teamLocationId: string(),
    allowedRoles: optional(any$1(), [
        Roles.TeamMember
    ])
});
const TeamPlans = {
    Free: 'Free',
    Pro: 'Pro',
    Enterprise: 'Enterprise'
};
const TeamLocationStatuses = {
    Active: 'Active',
    InActive: 'InActive'
};
const TeamRoles = {
    TeamAdmin: 'team-admin',
    TeamLocationAdmin: 'team-location-admin',
    TeamMember: 'team-member'
};
const Gender = {
    Male: 'Male',
    Female: 'Female',
    Other: 'Other'
};
const WorkDays = {
    Sunday: 'Sunday',
    Monday: 'Monday',
    Tuesday: 'Tuesday',
    Wednesday: 'Wednesday',
    Thursday: 'Thursday',
    Friday: 'Friday',
    Saturday: 'Saturday'
};
const UserAccountTypes = {
    Staff: 'Staff',
    Customer: 'Customer'
};

const Result = {
    success: (data)=>{
        return {
            data,
            error: undefined,
            isOk: true
        };
    },
    failure: (error)=>{
        console.log('error log: ', stringify(error)) //to use with logging tools
        ;
        return {
            data: null,
            error: error?.message,
            isOk: false
        };
    }
};

const genderListData = [
    {
        id: 'male',
        name: 'Male'
    },
    {
        id: 'female',
        name: 'Female'
    },
    {
        id: 'other',
        name: 'Other'
    }
];

// This is a constant array of timezones that should be used throughout the application
// It is exported once and should be imported from this file only
const TIMEZONES = [
    {
        id: 'UTC',
        name: 'UTC'
    },
    {
        id: 'America/New_York',
        name: 'America/New_York'
    },
    {
        id: 'America/Chicago',
        name: 'America/Chicago'
    },
    {
        id: 'America/Denver',
        name: 'America/Denver'
    },
    {
        id: 'America/Los_Angeles',
        name: 'America/Los_Angeles'
    },
    {
        id: 'Europe/London',
        name: 'Europe/London'
    },
    {
        id: 'Europe/Paris',
        name: 'Europe/Paris'
    },
    {
        id: 'Europe/Berlin',
        name: 'Europe/Berlin'
    },
    {
        id: 'Europe/Moscow',
        name: 'Europe/Moscow'
    },
    {
        id: 'Asia/Dubai',
        name: 'Asia/Dubai'
    },
    {
        id: 'Asia/Singapore',
        name: 'Asia/Singapore'
    },
    {
        id: 'Asia/Tokyo',
        name: 'Asia/Tokyo'
    },
    {
        id: 'Australia/Sydney',
        name: 'Australia/Sydney'
    },
    {
        id: 'Pacific/Auckland',
        name: 'Pacific/Auckland'
    },
    {
        id: 'Africa/Lagos',
        name: 'Africa/Lagos (Abuja)'
    },
    {
        id: 'Africa/Johannesburg',
        name: 'Africa/Johannesburg (Pretoria)'
    },
    {
        id: 'Africa/Nairobi',
        name: 'Africa/Nairobi'
    }
];

const userRolesData = [
    {
        id: 'team-admin',
        name: 'Team Admin'
    },
    {
        id: 'team-location-admin',
        name: 'Team Location Admin'
    },
    {
        id: 'team-member',
        name: 'Team Member'
    },
    {
        id: 'customer',
        name: 'Customer'
    },
    {
        id: 'guest',
        name: 'Guest'
    }
];

const stringVal = (name)=>{
    return `${name} must be a string`;
};

const newAppointmentFormSchema = object({
    appointmentTypeId: pipe(string(stringVal('Title')), minLength(1, 'Title is required')),
    appointmentNotes: optional(string(stringVal('Description'))),
    startTime: pipe(string(stringVal('Start Time')), regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/, 'Invalid date format')),
    endTime: pipe(string(stringVal('End Time')), regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/, 'Invalid date format')),
    customerEmail: pipe(string(), email('Invalid email address')),
    clientId: pipe(string(), minLength(1, 'Client name is required')),
    serviceId: optional(string()),
    customerName: pipe(string(stringVal('Full Name')), minLength(1, 'Client name is required')),
    customerPhoneNumber: optional(string(stringVal('Phone Number'))),
    locationId: string(stringVal('Location')),
    appointmentDate: date()
});
const confirmAppointmentFormSchema = object({
    id: string(),
    status: picklist([
        'pending',
        'confirmed',
        'cancelled'
    ]),
    title: pipe(string(), minLength(1)),
    description: optional(string()),
    startTime: date(),
    endTime: date()
});
const rescheduleAppointmentFormSchema = object({
    appointmentId: string(),
    appointmentDate: any$1(),
    appointmentStartTime: any$1(),
    // appointmentStartTime: custom<DateValue>(
    // 	(input): input is DateValue => input instanceof Date,
    // 	'Invalid date format'
    // ),
    appointmentStatus: picklist([
        appointmentStatuses.Scheduled,
        appointmentStatuses.Cancelled,
        appointmentStatuses.Confirmed,
        appointmentStatuses.Done,
        appointmentStatuses.Missed
    ])
});
const createAppointmentTypeFormSchema = object({
    id: optional(string()),
    name: pipe(string(), minLength(1, 'Name is required')),
    durationInMinutes: pipe(number(), minValue(15, 'Duration must be at least 1 minute')),
    isActive: boolean(),
    isRepeatEnabled: boolean(),
    requiresUpfrontPayment: boolean(),
    timeZoneId: string(),
    upfrontPaymentAmount: optional(number(), 0),
    repeatInterval: picklist([
        appointmentRepeatTypes.Daily,
        appointmentRepeatTypes.Weekly,
        appointmentRepeatTypes.EveryTwoWeeks,
        appointmentRepeatTypes.EveryFourWeeks,
        appointmentRepeatTypes.Monthly
    ])
});
const deleteAppointmentTypeFormSchema = object({
    id: string()
});

const formOptions = {
    validators: undefined,
    resetForm: false,
    errorSelector: '[aria-invalid="true"],[data-invalid]',
    scrollToError: 'smooth',
    autoFocusOnError: 'detect',
    taintedMessage: 'Do you want to leave this page? Changes you made may not be saved.',
    validationMethod: 'onblur',
    multipleSubmits: 'prevent'
};
const lettersOnly = /^[A-Za-z]+$/;
const companyEmailOnly = /^[a-zA-Z0-9._%+-]+@(?!gmail.com)(?!yahoo.com)(?!hotmail.com)(?!yahoo.co.in)(?!aol.com)(?!live.com)(?!outlook.com)[a-zA-Z0-9_-]+.[a-zA-Z0-9-.]{2,61}$/;
const passwordConstraint = '^(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{6,})' // export const ActionTypes = {
 // 	UpdateMemberStatus: 'updateMemberStatus',
 // 	UpdateLocation: 'updateLocation',
 // 	DisableLocation: 'DisableLocation'
 // } as const
 // export type ActionType = (typeof ActionTypes)[keyof typeof ActionTypes]
 // export type UpdateMemberStatusParamsType = {
 // 	userId: string
 // 	teamId: string
 // 	status: string
 // }
 // export type UpdateLocationParamsType = {
 // 	isActive: boolean
 // 	teamId: string
 // 	locationId: string
 // }
 // export type ServerPostType = {
 // 	params: UpdateMemberStatusParamsType | Record<string, any> | UpdateLocationParamsType
 // 	actionType: (typeof ActionTypes)[keyof typeof ActionTypes]
 // }
;

const lettersOnlyRegex = new RegExp(lettersOnly);
const passwordRegEx = new RegExp(passwordConstraint);
const signUpCodeFormSchema = object({
    code: string(stringVal('Code')),
    token: optional(string()),
    principal: optional(string()),
    email: optional(string(stringVal('Email'))),
    returnUrl: optional(string(stringVal('Return Url')), '/')
});
const signUpFormSchema = object({
    acceptTerms: boolean(),
    token: optional(string()),
    authType: picklist([
        'passkey',
        'email',
        'apple',
        'google'
    ]),
    verificationMethod: picklist([
        'link',
        'code'
    ]),
    givenName: pipe(string(stringVal('First Name')), minLength(2, 'First Name must have a length of at least 2 characters'), regex(lettersOnlyRegex, 'Only alphabet characters allowed')),
    familyName: pipe(string(stringVal('Last Name')), minLength(2, 'Last Name must have a length of at least 2 characters'), regex(lettersOnlyRegex, 'Only alphabet characters allowed')),
    phoneNumber: optional(pipe(string(stringVal('Phone Number')), regex(/^[0-9]{10}$/, 'Phone number must be 10 digits'))),
    email: pipe(string(stringVal('Email')), email('Entry is not a valid email')),
    accountType: optional(string(), UserAccountTypes.Staff),
    businessName: pipe(string(stringVal('Business Name')), minLength(2, 'Business Name must have a length of at least 2 characters')),
    businessAddress: pipe(string(stringVal('Business Address')), minLength(2, 'Business Address must have a length of at least 2 characters'))
});
/** reset Password schema*/ const resetPasswordFormSchema = pipe(object({
    password: pipe(string(stringVal('Password')), minLength(6, 'Password must be at least 6 characters long'), regex(passwordRegEx, 'Password must contain at least one number, one uppercase letter, and one symbol')),
    confirmPassword: string(stringVal('Confirm Password')),
    userId: string(stringVal('AppUser Id'))
}), forward(check((data)=>data.password === data.confirmPassword, `Passwords don't match`), [
    'confirmPassword'
]));
const requestPasswordResetFormSchema = object({
    email: pipe(string(stringVal('Email')), email('Entry is not a valid email'))
});
const loginFormSchema = object({
    email: pipe(string(stringVal('Email')), email('Entry is not a valid email')),
    token: optional(any$1()),
    authType: picklist([
        'passkey',
        'email',
        'apple',
        'google'
    ]),
    returnUrl: optional(string(stringVal('Return Url')), '/'),
    isEmailVerified: optional(boolean(), false)
});
const logOutFormSchema = object({
    userId: optional(string())
});
const setPasswordFormSchema = pipe(object({
    password: pipe(string(stringVal('Password')), minLength(6, 'Password must be at least 6 characters long'), regex(passwordRegEx, 'Password must contain at least one number, one uppercase letter, and one symbol')),
    confirmPassword: string(stringVal('Confirm Password'))
}), forward(check((data)=>data.password === data.confirmPassword, `Passwords don't match`), [
    'confirmPassword'
]));

const settingsFormSchema = object({
    id: optional(string()),
    teamName: string(),
    businessEmail: string(),
    phoneNumber: optional(pipe(string(stringVal$1('Phone Number')), regex(/^[0-9]{10}$/, 'Phone number must be 10 digits'))),
    businessRegNo: string(),
    registrationDate: date()
});
const staffInviteFormSchema = object({
    staffName: string(),
    staffEmail: string(),
    assignedLocation: string(),
    assignedRole: string(),
    gender: string(),
    phoneNumber: optional(pipe(string(stringVal$1('Phone Number')), regex(/^[0-9]{10}$/, 'Phone number must be 10 digits'))),
    idDocumentNumber: string(),
    dateOfBirth: string(),
    address: string(),
    city: string(),
    postalCode: string(),
    country: string()
});
const locationFormSchema = object({
    id: optional(string()),
    locationAddress: string(),
    locationEmail: pipe(string(stringVal$1('Email')), email('Entry is not a valid email')),
    locationPhoneNumber: optional(pipe(string(stringVal$1('Phone Number')), regex(/^[0-9]{10}$/, 'Phone number must be 10 digits'))),
    locationName: string(),
    isActive: boolean(),
    isDefault: optional(boolean(), false),
    locationAdmin: optional(string())
});

const addTeamMemberFormSchema = object({
    id: optional(string()),
    givenName: string(),
    familyName: string(),
    gender: any$1(),
    email: string(),
    role: string(),
    location: string(),
    idDocumentNumber: string(),
    phoneNumber: pipe(string(stringVal('Phone Number')), regex(/^[0-9]{10}$/, 'Phone number must be 10 digits'))
});

const bankAccountFormSchema = object({
    accountName: pipe(string(stringVal('Account Name')), minLength(2, 'Account name must be at least 2 characters long'), maxLength(100, 'Account name must be less than 100 characters')),
    accountNumber: pipe(string(stringVal('Account Number')), minLength(6, 'Account number must be at least 6 characters long'), maxLength(20, 'Account number must be less than 20 characters')),
    bankName: pipe(string(stringVal('Bank Name')), minLength(2, 'Bank name must be at least 2 characters long'), maxLength(100, 'Bank name must be less than 100 characters')),
    branchCode: pipe(string(stringVal('Branch Code')), minLength(4, 'Branch code must be at least 4 characters long'), maxLength(10, 'Branch code must be less than 10 characters')),
    swiftCode: pipe(string(stringVal('Swift Code')), minLength(8, 'SWIFT code must be at least 8 characters long'), maxLength(11, 'SWIFT code must be less than 11 characters'))
});
const paymentProviderFormSchema = object({
    id: optional(string(stringVal('ID'))),
    providerName: pipe(string(stringVal('Provider Name')), minLength(2, 'Provider name must be at least 2 characters long'), maxLength(100, 'Provider name must be less than 100 characters')),
    providerApiUrl: pipe(string(stringVal('API URL')), minLength(5, 'API URL must be at least 5 characters long'), maxLength(255, 'API URL must be less than 255 characters')),
    providerApiKey: optional(pipe(string(stringVal('Client ID')), minLength(8, 'Client ID must be at least 8 characters long'), maxLength(255, 'Client ID must be less than 255 characters'))),
    providerApiSecret: optional(pipe(string(stringVal('Client Secret')), minLength(16, 'Client secret must be at least 16 characters long'), maxLength(255, 'Client secret must be less than 255 characters'))),
    providerClientId: optional(pipe(string(stringVal('Client Secret')), minLength(16, 'Client secret must be at least 16 characters long'), maxLength(255, 'Client secret must be less than 255 characters'))),
    isActive: optional(boolean(), false),
    providerWebhookUrl: optional(pipe(string(stringVal('Webhook URL')), minLength(5, 'Webhook URL must be at least 5 characters long'), maxLength(255, 'Webhook URL must be less than 255 characters')))
});

// Base User Profile Schema
const userProfileSchema = object({
    givenName: string('First name is required'),
    familyName: string('Last name is required'),
    name: string(),
    accountType: string('Account type is required'),
    profilePhotoUrl: optional(string())
});
// User Root Schema that extends userProfileSchema
// Team Location Schema
const teamLocationSchema = object({
    id: string(),
    locationName: string('Location name is required'),
    locationAddress: optional(string()),
    locationEmail: pipe(string(), email('Invalid email format')),
    locationPhoneNumber: optional(string()),
    isActive: boolean(),
    isDefault: optional(boolean()),
    teamLocationAdmin: any$1()
});
// Member Invite Schema
const memberInviteSchema = object({
    inviteeEmail: pipe(string(), email('Invalid email format')),
    role: picklist(Object.values(TeamRoles), 'Invalid role selection'),
    teamLocation: teamLocationSchema,
    invitedBy: any$1(),
    invitedAt: date(),
    status: picklist(Object.values(TeamMemberStatuses), 'Invalid status'),
    expiresAt: date()
});
// Team Schema
const teamSchema = object({
    teamName: string('Team name is required'),
    businessEmail: pipe(string(), email('Invalid email format')),
    businessRegNo: optional(string()),
    phoneNumber: optional(string()),
    createdAt: date()
});
const userRootSchema = object({
    ...userProfileSchema.entries,
    email: pipe(string(), email('Invalid email format')),
    phoneNumber: string('Phone number is required'),
    nationalId: string('National ID is required'),
    dateOfBirth: date('Date of birth is required'),
    gender: picklist(Object.values(Gender), 'Invalid gender selection'),
    address: optional(string()),
    city: optional(string()),
    country: optional(string()),
    team: object({
        ...teamSchema.entries
    })
});
// Team Member Schema
const teamMemberSchema = object({
    userAccount: userRootSchema,
    role: picklist(Object.values(TeamRoles), 'Invalid role selection'),
    location: teamLocationSchema,
    joinedAt: date(),
    lastActive: optional(date()),
    memberStatus: picklist(Object.values(TeamMemberStatuses), 'Invalid status')
});

export { ActionTypes, ActorEvents, AppUserMetadataSchema, AppUserSchema, AuthAppDataSchema, CalendarViewTypes, Gender, Genders, InboxMessageTypes, Permissions, Result, Roles, SheetTypes, TIMEZONES, TeamLocationStatuses, TeamMemberStatuses, TeamPlans, TeamRoles, TimeSpan, ToastTypeSchema, UserAccountTypes, WorkDays, addTeamMemberFormSchema, appointmentRepeatTypes, appointmentStatuses, bankAccountFormSchema, buildGravatarUrl, capitalizeFirstLetter, checkPermission, classToPlain, companyEmailOnly, confirmAppointmentFormSchema, createAppointmentTypeFormSchema, createDate, deleteAppointmentTypeFormSchema, displayCurrency, displayCurrencyK, dynamicModalSchema, dynamicSheetSchema, formOptions, formatTimeWithAmPm, genderListData, getCopyRight, getDomain, getInitials, getKeyByValue, getLocalEmail, getRandomColorName, getStringInitials, greetings, hasPermission, initSuperJson, isOwner, isTeamAdmin, isTeamLocationAdmin, isTeamMember, isWithinExpirationDate, lettersOnly, locationFormSchema, logOutFormSchema, loginFormSchema, memberInviteSchema, newAppointmentFormSchema, objectToText, passwordConstraint, paymentProviderFormSchema, rbac, requestPasswordResetFormSchema, rescheduleAppointmentFormSchema, resetPasswordFormSchema, saveEmailLocally, setPasswordFormSchema, settingsFormSchema, signUpCodeFormSchema, signUpFormSchema, staffInviteFormSchema, stripUrl, teamLocationSchema, teamMemberSchema, teamSchema, timeGreeting, tryCatch, userProfileSchema, userRolesData, userRootSchema };
