{"name": "monorepo", "description": "Turborepo sveltekit starter with shadcn pre-configured", "private": true, "scripts": {"web": "dotenv -e .env -- turbo dev:web", "worker": "dotenv -e .env -- turbo dev:worker", "db": "dotenv -e .env -- turbo dev:encore", "migrate": "turbo db:change", "database": "turbo db:start", "studio": "dotenv -e .env -- turbo db:studio", "build": "dotenv -e .env -- turbo build", "preview": "dotenv -e .env -- turbo preview", "check": "dotenv -e .env -- turbo check", "lint": "turbo lint", "lint:fix": "turbo lint:fix", "format": "prettier --write .", "format:check": "prettier --check .", "ui": "pnpm run --cwd packages/ui ui"}, "devDependencies": {"dotenv-cli": "^8.0.0", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.12", "turbo": "^2.5.4"}, "packageManager": "pnpm@10.11.0", "engines": {"node": ">=20"}}