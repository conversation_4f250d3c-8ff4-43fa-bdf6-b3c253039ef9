import prettier from 'eslint-config-prettier'
import js from '@eslint/js'
import svelte from 'eslint-plugin-svelte'
import globals from 'globals'
import ts from 'typescript-eslint'

export default ts.config(
	js.configs.recommended,
	...ts.configs.recommended,
	...svelte.configs['flat/recommended'],
	prettier,
	...svelte.configs['flat/prettier'],
	{
		languageOptions: {
			globals: {
				...globals.browser,
				...globals.node
			}
		}
	},
	{
		files: ['**/*.svelte'],

		languageOptions: {
			parserOptions: {
				parser: ts.parser
			}
		}
	},
	{
		ignores: ['build/', '.svelte-kit/', 'dist/']
	},
	{
		rules: {
			semi: 'off',
			'missing-declaration': 'off',
			'a11y-invalid-attribute': 'off',
			'svelte/a11y-invalid-attribute': 'off',
			'@typescript-eslint/semi': 'off',
			'@typescript-eslint/no-unused-expressions': [
				'error',
				{
					allowShortCircuit: false,
					allowTernary: false
				}
			],
			'@typescript-eslint/ban-ts-comment': 'off',
			// Allow explicit `any`s
			'@typescript-eslint/no-explicit-any': 'off',
			'@typescript-eslint/no-implicit-any': 'off',

			// START: Allow implicit `any`s
			'@typescript-eslint/no-unsafe-argument': 'off',
			'@typescript-eslint/no-unsafe-assignment': 'off',
			'@typescript-eslint/no-unsafe-call': 'off',
			'@typescript-eslint/no-unsafe-member-access': 'off',
			'@typescript-eslint/no-unsafe-return': 'off',
			'svelte/no-inspect': 'off',
			'@typescript-eslint/ no-non-null-asserted-optional-chain':'off',
			arraysInObjects: 'off',
			// Add this rule to disable implicit any warnings
			'@typescript-eslint/no-inferrable-types': 'off'
		}
	}
)
