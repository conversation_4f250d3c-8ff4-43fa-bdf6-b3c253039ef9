{"name": "spendeed.web", "version": "0.0.1", "private": true, "scripts": {"gen:client": "apigen-ts http://localhost:4111/openapi.json ./src/lib/mastra/client.ts", "dev": "run-s db:start db:change db:studio dev:web", "dev:web": "BROWSER='Firefox Developer Edition' vite dev", "build": "vite build", "preview": "vite preview", "test": "npm run test:integration && npm run test:unit", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check", "check:watch": "svelte-kit sync && svelte-check --ignore './node_modules' --no-tsconfig", "lint": "prettier --check . && eslint .", "format": "prettier --write .", "test:integration": "playwright test", "test:unit": "vitest", "deploy-web": "wrangler pages deploy .svelte-kit/cloudflare --project-name=upfront --commit-dirty=true --env .env.production --branch main", "deploy": "run-s build deploy-web", "db:studio": "drizzle-kit studio", "db:change": "dotenv -e .env --  drizzle-kit generate --config drizzle.config.ts; drizzle-kit migrate --config drizzle.config.ts;", "db:start": "docker compose up", "db:stop": "docker compose down"}, "devDependencies": {"@actor-core/framework-base": "^0.8.0", "@ai-sdk/openai": "^1.3.22", "@axiomhq/js": "^1.3.1", "@dotenvx/dotenvx": "^1.44.1", "@eslint/compat": "^1.2.9", "@eslint/js": "^9.28.0", "@eslym/sveltekit-adapter-bun": "^2.0.5", "@fullcalendar/common": "^5.11.5", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/multimonth": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@internationalized/date": "^3.8.1", "@kitql/helpers": "^0.8.12", "@kitql/internals": "^0.10.4", "@kripod/uuidv7": "^0.3.4", "@lucide/svelte": "^0.511.0", "@mastra/client-js": "^0.10.1", "@number-flow/svelte": "^0.3.7", "@oslojs/crypto": "^1.0.1", "@passlock/client": "^0.9.30", "@passlock/node": "^0.9.30", "@passlock/sveltekit": "^0.9.31", "@passwordless-id/webauthn": "^2.3.0", "@playwright/test": "^1.52.0", "@plunk/node": "^3.0.3", "@poppanator/sveltekit-svg": "^5.0.1", "@react-email/components": "^0.0.41", "@react-email/render": "^1.1.2", "@repo/common": "workspace:*", "@repo/drizzle": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@restatedev/restate-sdk": "^1.6.0", "@restatedev/restate-sdk-clients": "^1.6.0", "@signaldb/core": "^1.6.0", "@signaldb/devtools": "1.0.0-beta.4", "@signaldb/indexeddb": "^1.1.0", "@signaldb/svelte": "^1.1.1", "@signaldb/sync": "^1.3.1", "@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.21.1", "@sveltejs/vite-plugin-svelte": "^5.0.3", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.8", "@tanstack/table-core": "^8.21.3", "@types/eslint": "^9.6.1", "@types/gravatar": "^1.8.6", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/node": "^22.15.29", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "actor-core": "^0.8.0", "ai": "4.3.16", "apigen-ts": "^1.2.1", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "bits-ui": "^2.4.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "decimal.js": "^10.5.0", "dotenv-cli": "8.0.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.44.1", "easy-rbac": "^3.2.0", "embla-carousel-svelte": "^8.6.0", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-config-turbo": "^2.5.4", "eslint-plugin-neverthrow": "^1.1.4", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-svelte": "^3.9.1", "esm-env": "^1.2.2", "formsnap": "^2.0.1", "generate-password-ts": "^1.6.5", "globals": "^16.2.0", "gravatar-gen": "^1.0.2", "gravatar-url": "^4.0.1", "http-status-codes": "^2.3.0", "import": "^0.0.6", "isomorphic-dompurify": "^2.25.0", "jose": "^6.0.11", "js-cookie": "^3.0.5", "jsx-email": "^2.7.2", "jwt-decode": "^4.0.0", "layerchart": "2.0.0-next.10", "linq-extensions": "^1.0.4", "lodash-es": "^4.17.21", "mastra": "^0.10.1", "mode-watcher": "^1.0.7", "npm-run-all": "^4.1.5", "otp-gen-agent": "^1.1.5", "paneforge": "1.0.0-next.5", "postcss": "^8.5.4", "postgres": "^3.4.7", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.12", "react": "^19.1.0", "runed": "^0.28.0", "superjson": "^2.2.2", "svelte": "^5.33.13", "svelte-breadcrumbs": "^2.0.1", "svelte-check": "^4.2.1", "svelte-inview": "^4.0.4", "svelte-kit-cookie-session": "^4.1.1", "svelte-meta-tags": "^4.3.0", "svelte-motion": "^0.12.2", "svelte-radix": "^2.0.1", "svelte-sonner": "^1.0.4", "svelte-tel-input": "^3.6.0", "sveltekit-flash-message": "^2.4.6", "sveltekit-i18n": "^2.4.2", "sveltekit-superactions": "^0.8.0", "sveltekit-superforms": "^2.25.0", "sveltekit-top-loader": "^0.1.0", "sveltekit-view-transition": "^0.5.3", "tailwind-merge": "^3.3.0", "tailwind-scrollbar-hide": "^4.0.0", "tailwind-variants": "^1.0.0", "tailwindcss": "4.1.8", "tsx": "^4.19.4", "tw-animate-css": "^1.3.3", "type-fest": "^4.41.0", "typescript": "^5.8.3", "typescript-eslint": "^8.33.0", "valibot": "^1.1.0", "vaul-svelte": "1.0.0-next.7", "vite": "^6.3.5", "vite-plugin-kit-routes": "^0.8.4", "vitest": "^3.1.4", "zod": "^3.25.48"}, "type": "module", "packageManager": "pnpm@10.11.0"}