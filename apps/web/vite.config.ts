import { sveltekit } from '@sveltejs/kit/vite'
import { defineConfig } from 'vitest/config'
import svg from '@poppanator/sveltekit-svg'
import { kitRoutes } from 'vite-plugin-kit-routes'
import type { KIT_ROUTES } from './src/lib/routes'
import tailwindcss from '@tailwindcss/vite'
import { fileURLToPath, URL } from 'node:url'

export default defineConfig({
	plugins: [
		tailwindcss(),
		svg(),
		kitRoutes<KIT_ROUTES>({ format: 'object[symbol]', format_short: true }),
		sveltekit()
	],
	test: {
		include: ['src/**/*.{test,spec}.{js,ts}']
	},
	server: {
		open: true,
		host: 'localhost',
		port: 5173,
		fs: {
			strict: false // Disable strict file serving restrictions
		}
	}
})
