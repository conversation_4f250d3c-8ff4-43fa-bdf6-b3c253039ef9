import type { TeamDbType } from '@repo/drizzle'
import {
	membersInviteSignal,
	membersSignal,
	teamBankDetailSignal,
	teamLocationsSignal,
	teamPaymentProviderSignal,
	teamSignal,
	usersSignal
} from '$/signals'

export const defaultTeamState = {} as TeamDbType
const createTeamStore = () => {
	let teamState = $derived(teamSignal.getTeamData())
	let teamLocationsState = $derived(teamLocationsSignal.getTeamLocationsData().fetch())
	let teamMembersState = $derived(membersSignal.getMemberData().fetch())
	let usersState = $derived(usersSignal.getUserData().fetch())
	let memberInvitesState = $derived(membersInviteSignal.getMemberInviteData().fetch())
	let teamBankDetailState = $derived(teamBankDetailSignal.getTeamBankDetailData())
	let teamPaymentProviderState = $derived(teamPaymentProviderSignal.getTeamPaymentProviderData())

	return {
		get team() {
			//return team only properties
			return teamState
		},
		get teamNameInitials() {
			return (
				teamState?.teamName
					?.split(' ')
					.map((wrd) => wrd.charAt(0).toUpperCase())
					.join('') ?? 'NA'
			)
		},
		get teamLocations() {
			return (
				teamLocationsState.map((tl) => {
					const locationAdmin = usersState.find((m) => m.id === tl.teamLocationAdmin)
					return {
						...tl,
						locationAdmin: locationAdmin
					}
				}) ?? []
			)
		},
		get teamMembers() {
			return teamMembersState.map((k) => {
				const memberLocation = teamLocationsState.firstOrNull(
					(tl) => tl.id === k.teamLocationId
				)
				const userProfile = usersState.firstOrNull((u) => u.id === k.userId)
				return {
					...k,
					...userProfile,
					...memberLocation
				}
			})
		},
		get teamMemberInvites() {
			return memberInvitesState
		},
		get teamBankDetail() {
			return teamBankDetailState
		},
		get teamPaymentProvider() {
			return teamPaymentProviderState
		},
		get teamIsSetup() {
			return !!teamState?.teamName
		}
	}
}

export type ITeamStore = ReturnType<typeof createTeamStore>
const teamStore = createTeamStore()
// export const teamStoreKey = 'teamState.store'
// export const setTeamStoreContext = () => {
// 	setStoreContext<ITeamStore>(teamStoreKey, createTeamStore())
// }
// export const getTeamStore = () => {
// 	return useStore<ITeamStore>(teamStoreKey)
// }

export const getTeamStore = () => {
	return teamStore
}
