import { SyncManager } from '@signaldb/sync'
import createIndexedDBAdapter from '@signaldb/indexeddb'
import { type ActorNameType, useActor, useActorEvent } from '$/lib/actor-core.svelte'
import { ActorEvents } from '@repo/common'

// import { initializeApp } from 'firebase/app'
// import { getDatabase, ref, get, set, remove, update, onChildAdded, onChildChanged, onChildRemoved } from 'firebase/database'

// initializeApp({
//       databaseURL: 'https://signaldb-d7e71-default-rtdb.firebaseio.com',
// })
// const database = getDatabase()

const syncManager = new SyncManager({
	id: 'spendeed-sync-manager',
	persistenceAdapter: (id) => createIndexedDBAdapter(id),
	onError: (options, error) => {
		console.error(options, error)
	},
	async registerRemoteChange({ name }, onChange) {
		const actorName: ActorNameType = `${name}Actor` as ActorNameType
		const actorStore = useActor(actorName)
		const handleChange = (change: any) => {
			void onChange(change)
		}
		useActorEvent({ actor: actorStore.actor, event: ActorEvents.ItemCreated }, handleChange)
		useActorEvent({ actor: actorStore.actor, event: ActorEvents.ItemUpdated }, handleChange)
		useActorEvent({ actor: actorStore.actor, event: ActorEvents.ItemDeleted }, handleChange)
	},
	async pull({ name }) {
		const actorName = `${name}Actor` as ActorNameType
		const actorStore = useActor(actorName)
		const result = (await actorStore.actor?.['getCollection']()) ?? []
		if (!result) return { items: [] }
		return { items: result }
	},
	async push({ name }, { changes }) {
		const actorName: ActorNameType = `${name}Actor` as ActorNameType
		const actorStore = useActor(actorName)
		await Promise.all([
			...changes.added.map(async (item) => {
				await actorStore.actor?.['create'](item)
			}),
			...changes.modified.map(async (item) => {
				await actorStore.actor?.['update'](item)
			}),
			...changes.removed.map(async (item) => {
				await actorStore.actor?.['delete'](item)
			})
		])
	}
})

export default syncManager
