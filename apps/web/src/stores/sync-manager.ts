import { SyncManager } from '@signaldb/sync'
import createIndexedDBAdapter from '@signaldb/indexeddb'

import { ActorEvents } from '@repo/common'
import type { ActorNameType } from '@repo/common'
import { getActor } from '$/lib/clients'




export const getSyncManager = (jwtToken) => {
	const syncManager = new SyncManager({
		id: 'spendeed-sync-manager',
		persistenceAdapter: (id) => createIndexedDBAdapter(id),
		onError: (options, error) => {
			console.error(options, error)
		},
		async registerRemoteChange({ name }, onChange) {
			const actorName: ActorNameType = `${name}Actor` as ActorNameType
			const actor = await getActor(actorName, jwtToken)
			const handleChange = (change: any) => {
				void onChange(change)
			}
			actor.on(ActorEvents.ItemCreated, handleChange)
			actor.on(ActorEvents.ItemUpdated, handleChange)
			actor.on(ActorEvents.ItemDeleted, handleChange)
		},
		async pull({ name }) {
			const actorName = `${name}Actor` as ActorNameType
			const actor = await getActor(actorName, jwtToken)
			const result: any = await actor?.getCollection()
			if (!result) return { items: [] }
			return { items: result?.data }
		},
		async push({ name }, { changes }) {
			const actorName: ActorNameType = `${name}Actor` as ActorNameType
			const actor = await getActor(actorName, jwtToken)
			await Promise.all([
				...changes.added.map(async (item) => {
					await actor?.['create'](item)
				}),
				...changes.modified.map(async (item) => {
					await actor?.['update'](item)
				}),
				...changes.removed.map(async (item) => {
					await actor?.['delete'](item)
				})
			])
		}
	})
	return syncManager
}
