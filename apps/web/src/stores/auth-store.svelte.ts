import type { ISessionData } from '@repo/common'
import { addDays } from 'date-fns'
import cloneDeep from 'lodash-es/cloneDeep'
import { browser } from '$app/environment'
import { parse, stringify } from 'superjson'

const defaultAuthState: ISessionData = {
	user: undefined,
	isAdmin: false,
	teamConfigIsSetup: false,
	isTeamAdmin: false,
	jwtToken: undefined,
	expires: addDays(new Date(), 7)
}

class AuthStore {
	private authState = $state<ISessionData>(cloneDeep(defaultAuthState))
	constructor() {
		if (browser) {
			const item = localStorage.getItem('authState')
			if (item) {
				this.authState = this.deserialize(item)
			}
		}
		$effect(() => {
			localStorage.setItem('authState', this.serialize(this.authState))
		})
	}
	private serialize(value: ISessionData): string {
		return stringify(value)
	}

	private deserialize(item: string): ISessionData {
		return parse(item)
	}

	updateStore(authData: ISessionData) {
		if (!authData) {
			this.clearStore()
			return
		}
		this.authState = { ...authData }
	}

	clearStore() {
		this.authState = cloneDeep(defaultAuthState)
	}

	get session() {
		return this.authState
	}

	get fullName() {
		return `${this.authState.user?.givenName} ${this.authState.user?.familyName}`
	}

	get user() {
		return this.authState.user
	}

	get isAdmin() {
		return this.authState.isAdmin
	}

	get isTeamAdmin() {
		return this.authState.isTeamAdmin
	}

	get teamConfigIsSetup() {
		return this.authState.teamConfigIsSetup
	}

	get showHomeModal() {
		return !this.authState.user
	}
}
export type IAuthStore = typeof AuthStore

export const getAuthStore = () => {
	return new AuthStore()
}
