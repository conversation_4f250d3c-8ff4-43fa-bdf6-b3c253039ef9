import { getRandomColorName, timeGreeting } from '@repo/common'
import {
	SheetTypes,
	type SheetType,
	type IDynamicModal,
	type IDynamicSheet,
	type AppUserInfo
} from '@repo/common'
import cloneDeep from 'lodash-es/cloneDeep'
import { toast } from 'svelte-sonner'
import { setStoreContext, useStore } from './use-shared-store'

const defaultUtilsState = {
	appLogoName: 'SpenDeed',
	appName: 'SpenDeed',
	isFirstTime: false,
	isNativePlatform: false,
	leftPanelOpen: false,
	showMobileMenu: false,
	activeNavBar: 1,
	homeSheet: {
		authingUserEmail: undefined as string | undefined,
		authingUserId: undefined as string | undefined
	},
	currentHomeSheet: SheetTypes.Marketing as SheetType,
	currentAuthEmail: '',
	appLayout: {
		currentPageTitle: 'Home',
		sidebarVisible: false
	},
	dynamicModalItem: undefined as IDynamicModal | undefined,
	dynamicSheetItem: undefined as IDynamicSheet | undefined,
	isAuthRoute: false,
	areWeLive: false,
	appLogoColor: 'bg-green-900'
}

export type IUtilsState = typeof defaultUtilsState
export type IToastType = 'success' | 'error' | 'warning' | 'info'

export const createUtilsStore = () => {
	let utilsState = $state<IUtilsState>(cloneDeep(defaultUtilsState))

	return {
		get st() {
			return utilsState
		},
		get currentHomeSheet() {
			return utilsState.currentHomeSheet
		},
		setPageTitle: (pageTitle: string) => {
			utilsState.appLayout.currentPageTitle = pageTitle
		},
		clearDynamicModal: () => {
			utilsState.dynamicModalItem = undefined
		},
		openDynamicModal: (dynamicFormObj: IDynamicModal) => {
			utilsState.dynamicModalItem = dynamicFormObj
		},
		closeDynamicSheet: () => {
			utilsState.dynamicSheetItem = undefined
		},
		openDynamicSheet: (dynamicFormObj: IDynamicSheet) => {
			utilsState.dynamicSheetItem = dynamicFormObj
		},

		greetings: (displayName: string) => {
			return `${timeGreeting()}  ${displayName ? displayName : 'Anonymous'}!`
		},

		toggleAppSideBar: () => {
			utilsState.appLayout.sidebarVisible = !utilsState.appLayout.sidebarVisible
		},
		toggleMobileMenu: () => {
			utilsState.showMobileMenu = !utilsState.showMobileMenu
		},

		toastError: (message: string) => {
			toast.error(message)
		},
		toastSuccess: (message: string) => {
			toast.success(message)
		},
		toastInfo: (message: string) => {
			toast.info(message)
		},
		toastWarning: (message: string) => {
			toast.warning(message)
		},
		setAppColor: () => {
			utilsState.appLogoColor = getRandomColorName()
		},
		setHomeSheet: (sheetType: SheetType) => {
			utilsState.currentHomeSheet = sheetType
		},
		setCurrentAuthEmail: (email: string) => {
			utilsState.currentAuthEmail = email
		}
	}
}

export type IUtilsStore = ReturnType<typeof createUtilsStore>
export const utilsStore = createUtilsStore()

// export const utilsStoreKey: string = 'utilsState.store'
// export const setUtilsStoreContext = () => {
// 	setStoreContext<IUtilsStore>(utilsStoreKey, createUtilsStore())
// }

// export const getUtilsStore = () => {
// 	return useStore<IUtilsStore>(utilsStoreKey)
// }

export const getUtilsStore = () => {
	return utilsStore
}
