import { setStoreContext, useStore } from './use-shared-store'
import {
	appointmentsSignal,
	appointmentTypesSignal,
	calendarBlockSignal,
	teamLocationsSignal,
	workdaySettingSignal
} from '$/signals'
import type {
	AppointmentDbType,
	AppointmentTypeDbType,
	CalendarBlockDbType,
	TeamLocationDbType,
	WorkdaySettingDbType
} from '@repo/drizzle'
import { CalendarViewTypes, type CalendarViewType } from '@repo/common'

export const createAppointmentStore = () => {
	let appointmentState$ = $derived<AppointmentDbType[]>(
		appointmentsSignal.getAppointmentData().fetch() as any[]
	)
	let currentViewState = $state<CalendarViewType>(CalendarViewTypes.Day)
	let selectedAppointmentState = $state<AppointmentDbType | undefined>(undefined)
	let calendarBlocksState$ = $derived<CalendarBlockDbType[]>(
		calendarBlockSignal.getCalendarBlockData().fetch() as any[]
	)
	let workdaySettingState$ = $derived<WorkdaySettingDbType>(
		workdaySettingSignal.getWorkdaySettingData() as any
	)
	let appointmentTypeState$ = $derived<AppointmentTypeDbType[]>(
		appointmentTypesSignal.getActiveAppointmentTypes().fetch() as any[]
	)
	let selectedAppointmentLocation$ = $derived.by<TeamLocationDbType | undefined>(() =>
		selectedAppointmentState?.teamLocationId
			? teamLocationsSignal.getTeamLocationById(selectedAppointmentState.teamLocationId)
			: (undefined as any)
	)
	const setSelectedAppointment = (data: AppointmentDbType | undefined) => {
		selectedAppointmentState = data
	}

	const setCurrentView = (view: CalendarViewType) => {
		currentViewState = view
	}
	return {
		setSelectedAppointment,
		setCurrentView,
		get selectedAppointment() {
			return selectedAppointmentState
		},
		get currentView() {
			return currentViewState
		},
		get appointments() {
			return appointmentState$
		},
		get calendarBlocks() {
			return calendarBlocksState$
		},
		get workdaySetting() {
			return workdaySettingState$
		},
		get appointmentTemplates() {
			return appointmentTypeState$
		},
		get selectedAppointmentLocation() {
			return selectedAppointmentLocation$
		},
		get currentDayAppointments() {
			return (
				appointmentState$
					?.where((k) => new Date(k.appointmentDate).getDate() == new Date().getDate())
					.toArray() ?? []
			)
		}
	}
}

export type IAppointmentStore = ReturnType<typeof createAppointmentStore>
const appointmentStore = createAppointmentStore()
// export const appointmentStoreKey = 'appointmentState.store'
// export const setAppointmentStoreContext = () => {
// 	setStoreContext<IAppointmentStore>(appointmentStoreKey, createAppointmentStore())
// }
// export const getAppointmentStore = () => {
// 	return useStore<IAppointmentStore>(appointmentStoreKey)
// }

export const getAppointmentStore = () => {
	return appointmentStore
}
