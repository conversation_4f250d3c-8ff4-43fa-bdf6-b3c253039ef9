import type {
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	ActorH<PERSON>le,
	ExtractAppFromClient,
	ExtractActorsFromApp,
	ClientRaw
} from 'actor-core/client'
import { ActorManager } from '@actor-core/framework-base'
import { actorCoreClient } from './clients'

// Remove the direct import to break circular dependency
// import { getAuthStore } from '$/stores'

export type ActorNameType =
	| 'authActor'
	| 'appointmentsActor'
	| 'teamActor'
	| 'calendarBlocksActor'
	| 'membersActor'
	| 'genderListActor'
	| 'appointmentTypesActor'
	| 'storageBucketActor'
	| 'storageFilesActor'
	| 'userRoleActor'
	| 'rolesListActor'
	| 'users'
	| 'teamPaymentProviderActor'
	| 'timeZonesActor'
	| 'workdaySettingActor'
	| 'teamLocationsActor'
	| 'teamBankDetailActor'
	| 'memberInviteActor'

type App = ExtractAppFromClient<ClientRaw>
type Registry = ExtractActorsFromApp<App>

export const useActor = <N extends keyof Registry, AD extends Registry[N]>(
	name: Exclude<N, symbol | number>
) => {
	// Use a cached auth store to avoid multiple imports
	let authStore: any = null

	// Dynamically import getAuthStore only when needed
	const getAuth = async () => {
		if (!authStore) {
			// Dynamic import to avoid circular dependency
			const stores = await import('$/stores')
			authStore = stores.getAuthStore()
		}
		return authStore
	}

	const getOpts = () => {
		try {
			// Use a synchronous approach with cached value if available
			return { params: { jwt: authStore?.session?.jwtToken ?? null } }
		} catch (e) {
			// Fallback if auth store isn't available yet
			return { params: { jwt: null } }
		}
	}

	const manager = $state(
		new ActorManager<ClientRaw, App, Registry, N, AD>(actorCoreClient, name, [getOpts()])
	)

	// Initialize auth store asynchronously
	getAuth().then(() => {
		manager.setOptions(getOpts() as any)

		// Set up a manual subscription to auth changes
		if (authStore && authStore.subscribe) {
			authStore.subscribe(() => {
				manager.setOptions(getOpts() as any)
			})
		}
	})

	// Create a safe wrapper for $effect that only runs in component contexts
	const safeEffect = (fn: () => void) => {
		try {
			if (typeof window !== 'undefined') {
				$effect(fn)
			}
		} catch (e) {
			// Ignore errors if we're not in a component context
		}
	}

	// Only use $effect if we're in a component context
	safeEffect(() => {
		if (authStore) {
			manager.setOptions(getOpts() as any)
		}
	})

	return {
		get actor() {
			return manager.getState()?.actor
		}
	}
}

export const useActorEvent = <N extends keyof Registry, AD extends Registry[N]>(
	opts: { actor: ActorHandle<AD> | undefined; event: string },
	cb: (...args: unknown[]) => void
) => {
	// Store the callback reference
	let callback = cb

	// Create a safe wrapper for $effect that only runs in component contexts
	const safeEffect = (fn: () => void) => {
		try {
			if (typeof window !== 'undefined') {
				$effect(fn)
			}
		} catch (e) {
			// Ignore errors if we're not in a component context
		}
	}

	// Update callback reference when it changes (only in component context)
	safeEffect(() => {
		callback = cb
	})

	// Direct subscription for non-component contexts
	if (opts.actor) {
		opts.actor.on(opts.event, (...args) => {
			callback(...args)
		})
	}

	// Subscribe to actor events (only in component context)
	safeEffect(() => {
		if (!opts.actor) {
			return noop
		}

		const unsub = opts.actor.on(opts.event, (...args) => {
			callback(...args)
		})

		// Return cleanup function
		return unsub
	})

	// Return a no-op function for non-component contexts
	return noop
}

const noop = () => {
	// noop
}
