import {
      PUBLIC_PASSLOCK_CLIENT_ID,
      PUBLIC_PASSLOCK_ENDPOINT,
      PUBLIC_PASSLOCK_TENANCY_ID
} from '$env/static/public'

import type { <PERSON><PERSON><PERSON><PERSON>, Principal, ResendEmail, VerifyEmailData } from '@passlock/sveltekit'
import { Passlock, ErrorCode, PasslockError } from '@passlock/sveltekit'
import { stringify } from 'superjson'
import type { SuperForm, SuperValidated } from 'sveltekit-superforms'


export type RegistrationData = {
      email: string
      givenName?: string
      familyName?: string
      token?: string
      authType: 'apple' | 'google' | 'email' | 'passkey'
      verificationMethod: 'link' | 'code'
      businessName?: string
      phoneNumber?: string
}

export type LoginData = {
      email?: string
      token?: string
      authType: 'apple' | 'google' | 'email' | 'passkey'
      isEmailVerified?: boolean
}

export type SuperformData<T extends Record<string, unknown>> = {
      cancel: () => void
      form: SuperValidated<T>
}
export type PasslockResp = {
      isOk: boolean
      err: string
}

export type VerifyEmailType = {
      method?: 'code' | 'link'
      redirectUrl?: string
      principal?: string
      email?: string
      returnUrl?: string
      code: string
      token?: string
}

class PasslockCustom extends Passlock {
      constructor(config: PasslockProps) {
            super(config)
      }

      readonly register = async <T extends RegistrationData>(
            options: SuperformData<T>
      ): Promise<PasslockResp> => {
            const { form } = options
            const {
                  email,
                  givenName,
                  familyName,
                  token,
                  authType,
                  phoneNumber,
                  businessName,
                  verificationMethod
            }: RegistrationData = form.data

            if (token && authType) {
                  // a bit hacky but basically the Google button sets the fields on the superform,
                  // who's data is not necessarily posted to the backend unless we use a hidden
                  // form field. We're basically duplicating the role of a hidden field here by
                  // adding the token and authType to the request
                  form.data.token = token
                  form.data.authType = authType
                  return { isOk: true, err: '' }
            }
            const principal: Principal | PasslockError = await this.registerPasskey({
                  email,
                  verifyEmail: { method: verificationMethod as any },
                  ...(givenName ? { givenName } : {}),
                  ...(familyName ? { familyName } : {}),
                  ...(phoneNumber ? { phoneNumber } : {}),
                  ...(businessName ? { businessName } : {})
            })
            if (PasslockError.isError(principal)) {
                  const error = principal.detail
                        ? `${principal.message}. ${principal.detail}`
                        : principal.message
                  return { isOk: false, err: error }
            } else if (!Passlock.isUserPrincipal(principal)) {
                  // set a form level error
                  return { isOk: false, err: 'Sorry, something went wrong' }
            }
            // append the passlock token to the form request
            form.data.authType = principal.authType
            form.data.token = principal.token
            if (verificationMethod) form.data.verificationMethod = verificationMethod
            return { isOk: true, err: '' }
      }

      readonly login = async<T extends LoginData>(options: SuperformData<T>)
            : Promise<PasslockResp> => {
            console.log('login entered')
            const { form } = options
            const { email, token, authType } = form.data
            if (token && authType) {
                  form.data.token = token
                  form.data.authType = authType
                  return { isOk: true, err: '' }
            }
            const principal = await this.authenticatePasskey({
                  email,
                  userVerification: 'discouraged'
            })
            console.log('The principal is', principal['code'])
            switch (true) {
                  case PasslockError.isError(principal) && principal.code === ErrorCode.NotFound: {
                        const error = principal.detail
                              ? `${principal.message}. ${principal.detail}`
                              : principal.message

                        return { isOk: false, err: error }
                  }
                  case PasslockError.isError(principal):
                        return { isOk: false, err: `${principal.message}. ${principal.detail}` }
                  case !Passlock.isUserPrincipal(principal):
                        return { isOk: false, err: 'Sorry, something went wrong' }
                  default:
                        form.data.email = principal.email
                        // append the passlock token to the form request
                        form.data.authType = principal.authType
                        form.data.token = principal.token
                        form.data.isEmailVerified = principal.emailVerified ?? false
                        return { isOk: true, err: '' }
            }
      }
      readonly verifyEmail = async <T extends VerifyEmailType>(
            options: SuperformData<T>
      ): Promise<PasslockResp> => {
            const { form } = options
            const { code } = form.data

            if (code.length >= 6) {
                  const principal = await this.verifyEmailCode({ code })

                  if (PasslockError.isError(principal)) {
                        return { isOk: false, err: principal.message }
                  } else {
                        form.data.token = principal.jti
                        form.data.principal = stringify(principal)
                        form.data.email = principal.email!
                        return { isOk: true, err: '' }
                  }
            } else {
                  return { isOk: false, err: 'Please enter your code' }
            }
      }

      readonly autoVerifyEmail = async <T extends VerifyEmailData>(form: SuperForm<T>) => {
            if (await this.getSessionToken('passkey')) {
                  form.submit()
            }
      }

      readonly resendEmail = async (options: ResendEmail) => {
            await this.resendVerificationEmail(options)
      }
}

export const updateForm =
      <T extends Record<string, unknown>>(form: SuperForm<T>, onComplete?: () => Promise<void>) =>
            (event: CustomEvent<Principal>) => {
                  form.form.update((old) => ({
                        ...old,
                        email: event.detail.email,
                        ...(event.detail.givenName ? { givenName: event.detail.givenName } : {}),
                        ...(event.detail.familyName ? { familyName: event.detail.familyName } : {}),
                        token: event.detail.jti,
                        authType: event.detail.authType
                  }))

                  if (typeof onComplete === 'function') {
                        onComplete()
                  }
            }

//export { getLocalEmail, saveEmailLocally }

export const passlockClient = new PasslockCustom({
      tenancyId: PUBLIC_PASSLOCK_TENANCY_ID,
      clientId: PUBLIC_PASSLOCK_CLIENT_ID,
      endpoint: PUBLIC_PASSLOCK_ENDPOINT
})

export const preConnect = passlockClient.preConnect
export const isPasskeySupport = passlockClient.isPasskeySupport