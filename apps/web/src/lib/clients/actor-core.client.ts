import type { ActorCoreApp } from '$backend'
import type { ActorNameType } from '@repo/common'
import { ActorDefinition, createClient, type ActorHandle } from 'actor-core/client'

export const actorCoreClient = createClient<ActorCoreApp>("http://localhost:6420")


export const getAuthActor = async () => {
      const actor = await actorCoreClient.auth.get({ noCreate: true, params: {} })
      return actor
}

export const getActor = async <T>(actorName: ActorNameType, jwtToken?: string) => {
      const actor: ActorHandle<ActorDefinition<T, any, any, any, any>> = actorCoreClient[actorName].get({ noCreate: true, params: { jwt: jwtToken } })
      return actor
}