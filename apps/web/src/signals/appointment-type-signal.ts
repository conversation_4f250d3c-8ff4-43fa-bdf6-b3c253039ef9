import { BaseEntity } from './base-signal'
import type { AppointmentTypeInsertDbType } from '@repo/drizzle'
import { safeParse } from 'valibot'
import { createAppointmentTypeFormSchema } from '@repo/common'


class AppointmentTypesCollection extends BaseEntity<AppointmentTypeInsertDbType> {
	constructor() {
		super('appointmentType')
	}
	getAppointmentTypeData() {
		return this.find({})
	}
	getAppointmentTypeById(id: string) {
		return this.findOne({ id })
	}
	getAppointmentTypesByTeam(teamId: string) {
		return this.find({ teamId })
	}
	getActiveAppointmentTypes() {
		return this.find({ isActive: true })
	}
}
const appointmentTypesSignal = new AppointmentTypesCollection()
appointmentTypesSignal.on('validate', (appointmentType) => {
	const result = safeParse(createAppointmentTypeFormSchema, appointmentType)
	if (!result.success) throw new Error('Appointment type validation failed')
})

export { appointmentTypesSignal }
