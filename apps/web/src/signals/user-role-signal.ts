
import { BaseEntity } from './base-signal'
import type { UserRoleInsertDbType } from '@repo/drizzle'

class UserRoleCollection extends BaseEntity<UserRoleInsertDbType> {
	constructor() {
		super('userRole')
	}
	getUserRoleData() {
		return this.find({})
	}
	getUserRoleById(id: string) {
		return this.findOne({ id })
	}
	getUserRolesByUser(userId: string) {
		return this.find({ userId })
	}
	getUserRolesByRole(role: string) {
		return this.find({ role })
	}
}
const userRoleSignal = new UserRoleCollection()


export { userRoleSignal }
