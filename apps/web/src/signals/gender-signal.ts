import syncManager from '$/stores/sync-manager'
import { BaseEntity } from './base-signal'
import type { GenderDbType } from '@repo/drizzle'

class GenderCollection extends BaseEntity<GenderDbType> {
	constructor() {
		super('gender')
	}
	getGenderData() {
		return this.find({})
	}
	getGenderByName(name: string) {
		return this.findOne({ name })
	}
}
const genderSignal = new GenderCollection()

syncManager.addCollection(genderSignal, { name: 'gender' })

export { genderSignal }
