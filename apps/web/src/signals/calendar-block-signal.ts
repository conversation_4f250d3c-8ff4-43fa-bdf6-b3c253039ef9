import syncManager from '$/stores/sync-manager'
import { BaseEntity } from './base-signal'
import type { CalendarBlockInsertType } from '@repo/drizzle'
import { safeParse } from 'valibot'

class CalendarBlockCollection extends BaseEntity<CalendarBlockInsertType> {
	constructor() {
		super('calendarBlock')
	}
	getCalendarBlockData() {
		return this.find({})
	}
	getCalendarBlockById(id: string) {
		return this.findOne({ id })
	}
	getCalendarBlocksByTeam(teamId: string) {
		return this.find({ teamId })
	}
}
const calendarBlockSignal = new CalendarBlockCollection()
calendarBlockSignal.on('validate', (calendarBlock) => {
	// const result = safeParse(calendarBlockSchema, calendarBlock)
	// if (!result.success) throw new Error('Calendar block validation failed')
})

syncManager.addCollection(calendarBlockSignal, { name: 'calendarBlock' })
export { calendarBlockSignal }
