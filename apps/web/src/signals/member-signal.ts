import { teamMemberSchema } from '@repo/common'
import { safeParse } from 'valibot'
import { BaseEntity } from './base-signal'
import type { MemberDbType } from '@repo/drizzle'
import syncManager from '$/stores/sync-manager'

class MemberCollection extends BaseEntity<MemberDbType> {
	constructor() {
		super('member')
	}
	getMemberData() {
		return this.find({})
	}
	getMemberById(id: string) {
		return this.findOne({ id })
	}
	getMembersByTeam(teamId: string) {
		return this.find({ teamId })
	}
	getMembersByLocation(teamLocationId: string) {
		return this.find({ teamLocationId })
	}
}
const membersSignal = new MemberCollection()

membersSignal.on('validate', (member) => {
	const result = safeParse(teamMemberSchema, member)
	if (!result.success) throw new Error('Member validation failed')
})
syncManager.addCollection(membersSignal, { name: 'member' })
export { membersSignal }
