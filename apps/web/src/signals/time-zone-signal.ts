import syncManager from '$/stores/sync-manager'
import { BaseEntity } from './base-signal'
import type { TimeZoneDbType } from '@repo/drizzle'

class TimeZonesCollection extends BaseEntity<TimeZoneDbType> {
	constructor() {
		super('timeZone')
	}
	getTimeZoneData() {
		return this.find({})
	}
	getTimeZoneById(id: string) {
		return this.findOne({ id })
	}
	getTimeZoneByName(timeZoneName: string) {
		return this.findOne({ timeZoneName })
	}
}
const timeZonesSignal = new TimeZonesCollection()
syncManager.addCollection(timeZonesSignal, { name: 'timeZones' })
export { timeZonesSignal }
