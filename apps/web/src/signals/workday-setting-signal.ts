import syncManager from '$/stores/sync-manager'
import { BaseEntity } from './base-signal'
import type { WorkdaySettingInsertDbType } from '@repo/drizzle'

class WorkdaySettingCollection extends BaseEntity<WorkdaySettingInsertDbType> {
	constructor() {
		super('workdaySetting')
	}
	getWorkdaySettingData() {
		return this.findOne({})
	}
	getWorkdaySettingById(id: string) {
		return this.findOne({ id })
	}
	getWorkdaySettingByTeam(teamId: string) {
		return this.findOne({ teamId })
	}
	getWorkdaySettingByWeekday(weekday: string) {
		return this.find({ weekday })
	}
	getActiveDays() {
		return this.find({ isDayActive: true })
	}
}

const workdaySettingSignal = new WorkdaySettingCollection()

syncManager.addCollection(workdaySettingSignal, { name: 'workdaySetting' })
export { workdaySettingSignal }
