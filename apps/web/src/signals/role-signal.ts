import type { RoleDbType } from '@repo/drizzle'
import { BaseEntity } from './base-signal'
import syncManager from '$/stores/sync-manager'

class RolesCollection extends BaseEntity<RoleDbType> {
	constructor() {
		super('role')
	}
	getRoleData() {
		return this.find({})
	}
	getRoleByName(role: string) {
		return this.findOne({ role })
	}
}
const rolesSignal = new RolesCollection()
syncManager.addCollection(rolesSignal, { name: 'roles' })

export { rolesSignal }
