import type { AppointmentInsertDbType } from '@repo/drizzle'
import { safeParse } from 'valibot'
import { BaseEntity } from './base-signal'
import { newAppointmentFormSchema } from '@repo/common'


class AppointmentCollection extends BaseEntity<AppointmentInsertDbType> {
	constructor() {
		super('appointment')
	}
	getAppointmentData() {
		return this.find({})
	}
	getAppointmentById(id: string) {
		return this.findOne({ id })
	}
	getAppointmentsByTeam(teamId: string) {
		return this.find({ teamId })
	}
	getAppointmentsByLocation(teamLocationId: string) {
		return this.find({ teamLocationId })
	}
	getAppointmentsByDate(appointmentDate: string) {
		return this.find({ appointmentDate })
	}
	getAppointmentsByStatus(appointmentStatus: string) {
		return this.find({ appointmentStatus })
	}
	getAppointmentsByCustomer(customerEmail: string) {
		return this.find({ customerEmail })
	}
}
const appointmentsSignal = new AppointmentCollection()
appointmentsSignal.on('validate', (appointment) => {
	const result = safeParse(newAppointmentFormSchema, appointment)
	if (!result.success) throw new Error('Appointment validation failed')
})


export { appointmentsSignal }
