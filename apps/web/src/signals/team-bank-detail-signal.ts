import { safeParse } from 'valibot'
import { BaseEntity } from './base-signal'
import type { TeamBankDetailInsertDbType } from '@repo/drizzle'
import { bankAccountFormSchema } from '@repo/common'


class TeamBankDetailCollection extends BaseEntity<TeamBankDetailInsertDbType> {
	constructor() {
		super('teamBankDetail')
	}
	getTeamBankDetailData() {
		return this.findOne({})
	}
	getTeamBankDetailById(id: string) {
		return this.findOne({ id })
	}
	getTeamBankDetailByTeam(teamId: string) {
		return this.findOne({ teamId })
	}
}
const teamBankDetailSignal = new TeamBankDetailCollection()
teamBankDetailSignal.on('validate', (teamBankDetail) => {
	const result = safeParse(bankAccountFormSchema, teamBankDetail)
	if (!result.success) throw new Error('Bank Account detail type validation failed')
})


export { teamBankDetailSignal }
