import { memberInviteSchema } from '@repo/common'
import { safeParse } from 'valibot'
import { BaseEntity } from './base-signal'
import type { MemberInviteInsertDbType } from '@repo/drizzle'
import syncManager from '$/stores/sync-manager'

class MemberInviteCollection extends BaseEntity<MemberInviteInsertDbType> {
	constructor() {
		super('memberInvite')
	}
	getMemberInviteData() {
		return this.find({})
	}
	getMemberInviteById(id: string) {
		return this.findOne({ id })
	}
	getMemberInvitesByTeam(teamId: string) {
		return this.find({ teamId })
	}
	getMemberInvitesByEmail(inviteeEmail: string) {
		return this.find({ inviteeEmail })
	}
	getPendingInvites() {
		return this.find({ isPending: true })
	}
	getMemberInvitesByInviter(invitedBy: string) {
		return this.find({ invitedBy })
	}
}
const membersInviteSignal = new MemberInviteCollection()

membersInviteSignal.on('validate', (memberInvite) => {
	const result = safeParse(memberInviteSchema, memberInvite)
	if (!result.success) throw new Error('Member invite validation failed')
})

syncManager.addCollection(membersInviteSignal, { name: 'membersInvite' })
export { membersInviteSignal }
