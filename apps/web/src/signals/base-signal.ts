import { Collection, type BaseItem } from '@signaldb/core'
import createIndexedDBAdapter from '@signaldb/indexeddb'
import svelteReactivityAdapter from '@signaldb/svelte'

export class BaseEntity<T> extends Collection<T & BaseItem<string>, string, T> {
      constructor(name: string) {
            super({
                  name: name,
                  reactivity: svelteReactivityAdapter,
                  persistence: createIndexedDBAdapter(name)
            })
      }
}
