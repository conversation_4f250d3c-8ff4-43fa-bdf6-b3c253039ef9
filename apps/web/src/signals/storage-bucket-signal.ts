import type { StorageBucketInsertDbType } from '@repo/drizzle'
import { BaseEntity } from './base-signal'


class StorageBucketCollection extends BaseEntity<StorageBucketInsertDbType> {
	constructor() {
		super('storageBucket')
	}
	getStorageBucketData() {
		return this.find({})
	}
	getStorageBucketById(id: string) {
		return this.findOne({ id })
	}
}
const storageBucketSignal = new StorageBucketCollection()


export { storageBucketSignal }
