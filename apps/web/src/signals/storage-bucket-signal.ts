import type { StorageBucketInsertDbType } from '@repo/drizzle'
import { BaseEntity } from './base-signal'
import syncManager from '$/stores/sync-manager'

class StorageBucketCollection extends BaseEntity<StorageBucketInsertDbType> {
	constructor() {
		super('storageBucket')
	}
	getStorageBucketData() {
		return this.find({})
	}
	getStorageBucketById(id: string) {
		return this.findOne({ id })
	}
}
const storageBucketSignal = new StorageBucketCollection()

syncManager.addCollection(storageBucketSignal, { name: 'storageBucket' })
export { storageBucketSignal }
