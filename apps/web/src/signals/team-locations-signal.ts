import { teamLocationSchema } from '@repo/common'
import { safeParse } from 'valibot'
import { BaseEntity } from './base-signal'
import type { TeamLocationInsertDbType } from '@repo/drizzle'
import syncManager from '$/stores/sync-manager'

class TeamLocationCollection extends BaseEntity<TeamLocationInsertDbType> {
	constructor() {
		super('teamLocation')
	}
	getTeamLocationsData() {
		return this.find({})
	}
	getTeamLocationById(id: string) {
		return this.findOne({ id })
	}
	getTeamLocationsByTeam(teamId: string) {
		return this.find({ teamId })
	}
	getActiveLocations() {
		return this.find({ isActive: true })
	}
	getDefaultLocation() {
		return this.findOne({ isDefault: true })
	}
}
const teamLocationsSignal = new TeamLocationCollection()

teamLocationsSignal.on('validate', (teamLocation) => {
	const result = safeParse(teamLocationSchema, teamLocation)
	if (!result.success) throw new Error('Team location validation failed')
})
syncManager.addCollection(teamLocationsSignal, { name: 'teamLocations' })
export { teamLocationsSignal }
