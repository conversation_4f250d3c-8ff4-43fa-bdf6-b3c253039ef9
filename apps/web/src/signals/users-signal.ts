import { userRootSchema } from '@repo/common'
import { safeParse } from 'valibot'
import { BaseEntity } from './base-signal'
import type { UserInsertDbType } from '@repo/drizzle'


class UserCollection extends BaseEntity<UserInsertDbType> {
	constructor() {
		super('user')
	}
	getUserData() {
		return this.find({})
	}
	getUserById(id: string) {
		return this.findOne({ id })
	}
	getUserByEmail(email: string) {
		return this.findOne({ email })
	}
	getUserByAuthKey(authKey: string) {
		return this.findOne({ authKey })
	}
	getActiveUsers() {
		return this.find({ disabled: false })
	}
}
const usersSignal = new UserCollection()

usersSignal.on('validate', (user) => {
	const result = safeParse(userRootSchema, user)
	if (!result.success) throw new Error('User validation failed')
})

export { usersSignal }
