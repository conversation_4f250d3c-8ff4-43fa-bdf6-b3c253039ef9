import type { StorageFileInsertDbType } from '@repo/drizzle'
import { BaseEntity } from './base-signal'


class StorageFilesCollection extends BaseEntity<StorageFileInsertDbType> {
	constructor() {
		super('storageFile')
	}
	getStorageFileData() {
		return this.find({})
	}
	getStorageFileById(id: string) {
		return this.findOne({ id })
	}
	getStorageFilesByBucket(bucketId: string) {
		return this.find({ bucketId })
	}
	getUploadedFiles() {
		return this.find({ isUploaded: true })
	}
}
const storageFilesSignal = new StorageFilesCollection()



export { storageFilesSignal }
