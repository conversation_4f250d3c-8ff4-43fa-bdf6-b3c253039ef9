import { teamSchema } from '@repo/common'
import { safeParse } from 'valibot'
import { BaseEntity } from './base-signal'
import type { TeamInsertDbType } from '@repo/drizzle'
import { membersSignal } from './member-signal'
import { usersSignal } from './users-signal'


class TeamCollection extends BaseEntity<TeamInsertDbType> {
	constructor() {
		super('team')
	}
	getTeamData() {
		return this.findOne({}) as TeamInsertDbType
	}
	getTeamMembers() {
		const membersProfile = membersSignal.getMemberData().map((m) => {
			const profile = usersSignal.getUserById(m.userId)
			return {
				...m,
				profile
			}
		})
		return membersProfile
	}
}
const teamSignal = new TeamCollection()

teamSignal.on('validate', (team) => {
	const result = safeParse(teamSchema, team)
	if (!result.success) throw new Error('Team validation failed')
})


export { teamSignal }
