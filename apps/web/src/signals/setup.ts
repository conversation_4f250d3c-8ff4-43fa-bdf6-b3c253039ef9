import syncManager from '$/stores/sync-manager'
import { appointmentsSignal } from './appointment-signal'
import { appointmentTypesSignal } from './appointment-type-signal'
import { calendarBlockSignal } from './calendar-block-signal'
import { genderSignal } from './gender-signal'
import { membersSignal } from './member-signal'
import { membersInviteSignal } from './members-invite-signal'

export const setupSignals = () => {
      syncManager.addCollection(appointmentsSignal, { name: 'appointment' })
      syncManager.addCollection(appointmentTypesSignal, { name: 'appointmentTypes' })
      syncManager.addCollection(calendarBlockSignal, { name: 'calendarBlock' })
      syncManager.addCollection(genderSignal, { name: 'gender' })
      syncManager.addCollection(membersInviteSignal, { name: 'membersInvite' })
      syncManager.addCollection(membersSignal, { name: 'member' })
      syncManager.addCollection(rolesSignal, { name: 'roles' })
      syncManager.addCollection(storageBucketSignal, { name: 'storageBucket' })
      syncManager.addCollection(storageFilesSignal, { name: 'storageFile' })
      syncManager.addCollection(teamBankDetailSignal, { name: 'teamBankDetail' })
      syncManager.addCollection(teamLocationsSignal, { name: 'teamLocations' })
      syncManager.addCollection(teamPaymentProviderSignal, { name: 'teamPaymentProvider' })
      syncManager.addCollection(teamSignal, { name: 'team' })
      syncManager.addCollection(timeZonesSignal, { name: 'timeZones' })
      syncManager.addCollection(userRoleSignal, { name: 'userRole' })
      syncManager.addCollection(usersSignal, { name: 'users' })
      syncManager.addCollection(workdaySettingSignal, { name: 'workdaySetting' })
}
