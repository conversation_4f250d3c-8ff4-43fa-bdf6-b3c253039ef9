import { getSyncManager } from '$/stores/sync-manager'
import { appointmentsSignal } from './appointment-signal'
import { appointmentTypesSignal } from './appointment-type-signal'
import { calendarBlockSignal } from './calendar-block-signal'
import { genderSignal } from './gender-signal'
import { membersSignal } from './member-signal'
import { membersInviteSignal } from './members-invite-signal'
import { rolesSignal } from './role-signal'
import { storageBucketSignal } from './storage-bucket-signal'
import { storageFilesSignal } from './storage-file-signal'
import { teamBankDetailSignal } from './team-bank-detail-signal'
import { teamLocationsSignal } from './team-locations-signal'
import { teamPaymentProviderSignal } from './team-payment-provider-signal'
import { teamSignal } from './team-signal'
import { timeZonesSignal } from './time-zone-signal'
import { userRoleSignal } from './user-role-signal'
import { usersSignal } from './users-signal'
import { workdaySettingSignal } from './workday-setting-signal'

export const setupSignals = (jwtToken: string) => {
      const syncManager = getSyncManager(jwtToken)

      syncManager.addCollection(appointmentsSignal, { name: 'appointment' })
      syncManager.addCollection(appointmentTypesSignal, { name: 'appointmentTypes' })
      syncManager.addCollection(calendarBlockSignal, { name: 'calendarBlock' })
      syncManager.addCollection(genderSignal, { name: 'gender' })
      syncManager.addCollection(membersInviteSignal, { name: 'membersInvite' })
      syncManager.addCollection(membersSignal, { name: 'member' })
      syncManager.addCollection(rolesSignal, { name: 'roles' })
      syncManager.addCollection(storageBucketSignal, { name: 'storageBucket' })
      syncManager.addCollection(storageFilesSignal, { name: 'storageFile' })
      syncManager.addCollection(teamBankDetailSignal, { name: 'teamBankDetail' })
      syncManager.addCollection(teamLocationsSignal, { name: 'teamLocations' })
      syncManager.addCollection(teamPaymentProviderSignal, { name: 'teamPaymentProvider' })
      syncManager.addCollection(teamSignal, { name: 'team' })
      syncManager.addCollection(timeZonesSignal, { name: 'timeZones' })
      syncManager.addCollection(userRoleSignal, { name: 'userRole' })
      syncManager.addCollection(usersSignal, { name: 'users' })
      syncManager.addCollection(workdaySettingSignal, { name: 'workdaySetting' })

      return syncManager
}
