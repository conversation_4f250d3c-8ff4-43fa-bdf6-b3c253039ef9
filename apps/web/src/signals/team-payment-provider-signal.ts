import { paymentProviderFormSchema } from '@repo/common'
import { BaseEntity } from './base-signal'
import type { TeamPaymentProviderInsertDbType } from '@repo/drizzle'
import { safeParse } from 'valibot'


class TeamPaymentProviderCollection extends BaseEntity<TeamPaymentProviderInsertDbType> {
	constructor() {
		super('teamPaymentProvider')
	}
	getTeamPaymentProviderData() {
		return this.findOne({})
	}
	getTeamPaymentProviderById(id: string) {
		return this.findOne({ id })
	}
	getTeamPaymentProviderByTeam(teamId: string) {
		return this.findOne({ teamId })
	}
	getActiveProviders() {
		return this.find({ isActive: true })
	}
}
const teamPaymentProviderSignal = new TeamPaymentProviderCollection()
teamPaymentProviderSignal.on('validate', (tp) => {
	const result = safeParse(paymentProviderFormSchema, tp)
	if (!result.success) throw new Error('Payment Provider validation failed')
})

export { teamPaymentProviderSignal }
