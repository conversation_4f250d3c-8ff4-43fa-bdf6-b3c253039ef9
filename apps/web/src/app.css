@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.
 
  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentcolor);
	}
}

@layer base {
	:root {
		--radius: 0.625rem;
		--background: oklch(1 0 0);
		--foreground: oklch(0.147 0.004 49.25);
		--card: oklch(1 0 0);
		--card-foreground: oklch(0.147 0.004 49.25);
		--popover: oklch(1 0 0);
		--popover-foreground: oklch(0.147 0.004 49.25);
		--primary: oklch(0.216 0.006 56.043);
		--primary-foreground: oklch(0.985 0.001 106.423);
		--secondary: oklch(0.97 0.001 106.424);
		--secondary-foreground: oklch(0.216 0.006 56.043);
		--muted: oklch(0.97 0.001 106.424);
		--muted-foreground: oklch(0.553 0.013 58.071);
		--accent: oklch(0.97 0.001 106.424);
		--accent-foreground: oklch(0.216 0.006 56.043);
		--destructive: oklch(0.577 0.245 27.325);
		--border: oklch(0.923 0.003 48.717);
		--input: oklch(0.923 0.003 48.717);
		--ring: oklch(0.709 0.01 56.259);
		--chart-1: oklch(0.646 0.222 41.116);
		--chart-2: oklch(0.6 0.118 184.704);
		--chart-3: oklch(0.398 0.07 227.392);
		--chart-4: oklch(0.828 0.189 84.429);
		--chart-5: oklch(0.769 0.188 70.08);
		--sidebar: oklch(0.985 0.001 106.423);
		--sidebar-foreground: oklch(0.147 0.004 49.25);
		--sidebar-primary: oklch(0.216 0.006 56.043);
		--sidebar-primary-foreground: oklch(0.985 0.001 106.423);
		--sidebar-accent: oklch(0.97 0.001 106.424);
		--sidebar-accent-foreground: oklch(0.216 0.006 56.043);
		--sidebar-border: oklch(0.923 0.003 48.717);
		--sidebar-ring: oklch(0.709 0.01 56.259);
		--radius: 0rem;
	}
	.dark {
		--background: oklch(0.147 0.004 49.25);
		--foreground: oklch(0.985 0.001 106.423);
		--card: oklch(0.216 0.006 56.043);
		--card-foreground: oklch(0.985 0.001 106.423);
		--popover: oklch(0.216 0.006 56.043);
		--popover-foreground: oklch(0.985 0.001 106.423);
		--primary: oklch(0.923 0.003 48.717);
		--primary-foreground: oklch(0.216 0.006 56.043);
		--secondary: oklch(0.268 0.007 34.298);
		--secondary-foreground: oklch(0.985 0.001 106.423);
		--muted: oklch(0.268 0.007 34.298);
		--muted-foreground: oklch(0.709 0.01 56.259);
		--accent: oklch(0.268 0.007 34.298);
		--accent-foreground: oklch(0.985 0.001 106.423);
		--destructive: oklch(0.704 0.191 22.216);
		--border: oklch(1 0 0 / 10%);
		--input: oklch(1 0 0 / 15%);
		--ring: oklch(0.553 0.013 58.071);
		--chart-1: oklch(0.488 0.243 264.376);
		--chart-2: oklch(0.696 0.17 162.48);
		--chart-3: oklch(0.769 0.188 70.08);
		--chart-4: oklch(0.627 0.265 303.9);
		--chart-5: oklch(0.645 0.246 16.439);
		--sidebar: oklch(0.216 0.006 56.043);
		--sidebar-foreground: oklch(0.985 0.001 106.423);
		--sidebar-primary: oklch(0.488 0.243 264.376);
		--sidebar-primary-foreground: oklch(0.985 0.001 106.423);
		--sidebar-accent: oklch(0.268 0.007 34.298);
		--sidebar-accent-foreground: oklch(0.985 0.001 106.423);
		--sidebar-border: oklch(1 0 0 / 10%);
		--sidebar-ring: oklch(0.553 0.013 58.071);
		--radius: 0rem;
	}
}

@layer components {
	.section-heading {
		@apply text-xl font-bold tracking-tighter sm:text-2xl md:text-3xl;
	}
}

/* view transition API styles  */

@keyframes fade-in {
	from {
		opacity: 0;
	}
}

@keyframes fade-out {
	to {
		opacity: 0;
	}
}

@keyframes slide-from-right {
	from {
		transform: translateX(30px);
	}
}

@keyframes slide-to-left {
	to {
		transform: translateX(-30px);
	}
}

:root::view-transition-old(root) {
	animation:
		90ms cubic-bezier(0.4, 0, 1, 1) both fade-out,
		300ms cubic-bezier(0.4, 0, 0.2, 1) both slide-to-left;
}

:root::view-transition-new(root) {
	animation:
		210ms cubic-bezier(0, 0, 0.2, 1) 90ms both fade-in,
		300ms cubic-bezier(0.4, 0, 0.2, 1) both slide-from-right;
}

.dark\:shadow-calendar-dark:is(.dark *) {
	--tw-shadow: 0px 1px 2px -1px hsla(0, 0%, 100%, 0.1);
	--tw-shadow-colored: 0px 1px 2px -1px var(--tw-shadow-color);
	box-shadow:
		var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

@theme inline {
	/* Radius (for rounded-*) */
	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);

	/* Colors */
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);
	--color-ring: var(--ring);
	--color-radius: var(--radius);
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground w-full max-w-full;
	}
}

.no-trans {
	view-transition-name: static;
}
