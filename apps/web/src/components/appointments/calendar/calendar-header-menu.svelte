<script lang="ts">
	import { Button } from '$/components/ui/button'
	import { Plus, List, Columns2, Grid3X3 } from '@lucide/svelte'
	import { ButtonGroup } from '$/components/common'
	import { CalendarViewTypes } from '@repo/common'
	import { getAppointmentStore } from '$/stores'

	let { showAddAppointment = true }: { showAddAppointment: boolean } = $props()
	let appointmentStore = getAppointmentStore()
	//let currentView = $state<CalendarViewType>(CalendarViewTypes.day)
</script>

<div class="flex items-center justify-between gap-3">
	<div class="inline-flex">
		<ButtonGroup>
			<Button
				variant="outline"
				class="rounded-r-none"
				onclick={() => appointmentStore.setCurrentView(CalendarViewTypes.Day)}
			>
				<List class="mr-2" />
				<span class="hidden xl:block">Day</span>
			</Button>
			<Button
				variant="outline"
				class="hidden rounded-l-none rounded-r-none border-l-0 lg:inline-flex"
				onclick={() => appointmentStore.setCurrentView(CalendarViewTypes.Week)}
			>
				<Columns2 class="xl:mr-2" />
				<span class="hidden xl:block">Week</span>
			</Button>
			<Button
				variant="outline"
				class="rounded-l-none border-l-0"
				onclick={() => appointmentStore.setCurrentView(CalendarViewTypes.Month)}
			>
				<Grid3X3 class="mr-2" />
				<span class="hidden xl:block">Month</span>
			</Button>
		</ButtonGroup>
	</div>
	{#if showAddAppointment}
		<Button class="transition-colors">
			<Plus class="mr-2 size-4" />
			<span>Create Appointment</span>
		</Button>variant
	{/if}
</div>
