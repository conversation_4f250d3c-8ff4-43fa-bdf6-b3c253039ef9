<script lang="ts">
	import { ReceiptText } from '@lucide/svelte'
	import { cn } from '$/lib/shadcn.utils'
	let { clsName = 'flex items-center justify-center' }: { clsName?: string } = $props()
</script>

<a class={cn('focus:outline-none', clsName)} href="/">
	<ReceiptText class="text-primary size-6" />
	<span class="ml-0 truncate text-xl font-bold">
		<span class="text-primary">Spen</span>
		<span class="text-muted-foreground -ml-1">Deed</span>
	</span>
</a>
