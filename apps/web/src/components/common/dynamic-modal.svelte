<script lang="ts">
	import { getUtilsStore } from '$/stores'
	import type { IComponentCache, IDynamicModal } from '@repo/common'
	import * as Dialog from '$/components/ui/dialog'

	let { componentsCollection }: { componentsCollection: IComponentCache[] } = $props()
	const utilsStore = getUtilsStore()

	let CurrentComponent: any = $state(null)
	let modalTitle: string | null = $state(null)

	let dialogClass: string = $state('')
	let localComponentProps: any = $state({})
	let openModal = $state(false)
	let modalDescription: string | null = $state(null)

	let canClose = $derived(utilsStore.st?.dynamicModalItem?.canClose)

	const showHidePopup = (dynamicForm: IDynamicModal | undefined) => {
		if (dynamicForm?.componentName) {
			const currentItem = componentsCollection.firstOrNull(
				(x) => x.componentName === utilsStore.st?.dynamicModalItem?.componentName
			)
			if (currentItem) {
				CurrentComponent = currentItem?.component
				modalTitle = dynamicForm.title
				modalDescription = dynamicForm.description
				const { componentProps } = dynamicForm
				localComponentProps = componentProps
				dialogClass = currentItem.dialogClass
			}
		}
	}

	$effect(() => {
		if (utilsStore.st?.dynamicModalItem) {
			openModal = true
			showHidePopup(utilsStore.st?.dynamicModalItem)
		} else {
			openModal = false
		}
	})
	const openStateChanged = (isOpen: any) => {
		console.log('open state changed', isOpen)
		if (!openModal) {
			utilsStore.closeDynamicSheet()
		}
	}
</script>

<Dialog.Root bind:open={openModal} onOpenChange={openStateChanged}>
	<Dialog.Trigger />
	<Dialog.Content
		class={dialogClass}
		showCloseButton={canClose ?? false}
		interactOutsideBehavior={canClose ? 'close' : 'ignore'}
	>
		<Dialog.Header>
			<Dialog.Title>
				{modalTitle}
			</Dialog.Title>
			<Dialog.Description>
				{modalDescription}
			</Dialog.Description>
		</Dialog.Header>
		<CurrentComponent {...localComponentProps} />
	</Dialog.Content>
</Dialog.Root>
