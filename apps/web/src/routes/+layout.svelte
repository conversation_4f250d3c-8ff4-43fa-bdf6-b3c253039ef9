<script lang="ts">
	import 'linq-extensions'
	import '../app.css'
	import { TailwindIndicator } from '$/components/common'
	import { dev } from '$app/environment'
	import { onNavigate } from '$app/navigation'
	import { page } from '$app/state'
	import { ModeWatcher } from 'mode-watcher'
	import { type MetaTagsProps, deepMerge, MetaTags } from 'svelte-meta-tags'
	import { initSuperJson } from '@repo/common'
	import { onMount } from 'svelte'
	import { setupSignals } from '$/signals'

	let { children }: { children: any } = $props()

	const baseMetaTags: MetaTagsProps = $state({
		title: 'Default',
		titleTemplate: '%s | SpenDeed',
		description:
			'Ai Driven small business platform for running everything from AI assisted sales to rewards and appointments',
		canonical: new URL(page.url.pathname, page.url.origin).href,
		openGraph: {
			type: 'website',
			url: new URL(page.url.pathname, page.url.origin).href,
			locale: 'en_GB',
			title: 'Spendeed',
			description:
				'Ai Driven small business platform for running everything from AI assisted sales to rewards and appointments',
			siteName: 'spendeed.com',
			images: [
				{
					url: 'https://www.example.ie/og-image.jpg',
					alt: 'Og Image Alt',
					width: 800,
					height: 600,
					secureUrl: 'https://www.example.ie/og-image.jpg',
					type: 'image/jpeg'
				}
			]
		}
	})
	let metaTags = $derived(deepMerge(baseMetaTags, page.data.pageMetaTags))

	onNavigate((navigation) => {
		if (!document.startViewTransition) return

		return new Promise((resolve) => {
			document.startViewTransition(async () => {
				resolve()
				await navigation.complete
			})
		})
	})

	// Initialize before the app starts

	initSuperJson()
	onMount(() => {
		setupSignals()
	})

	// if (dev) {
	// 	loadDeveloperTools()
	// }
</script>

<svelte:head>
	<script src="https://accounts.google.com/gsi/client" async></script>
	<script
		src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"
		async
	></script>
</svelte:head>
<MetaTags {...metaTags} />
<ModeWatcher />
{@render children?.()}

{#if dev}
	<TailwindIndicator />
{/if}
