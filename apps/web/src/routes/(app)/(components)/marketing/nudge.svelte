<script lang="ts">
	import { ArrowRight } from '@lucide/svelte'
	import { Button } from '$/components/ui/button'
	import { Input } from '$/components/ui/input'
</script>

<section class="bg- bg-muted/40 lg:py-26 w-full py-4 md:py-12 xl:py-20">
	<div class="container px-4 md:px-6">
		<div class="flex flex-col items-center space-y-4 text-center">
			<div class="space-y-2">
				<p class="section-heading">Ready to Grow Your Business?</p>
				<p
					class="text-muted-foreground mx-auto max-w-[600px] md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed"
				>
					Join thousands of small businesses using SpenDeed to boost customer engagement and
					increase sales.
				</p>
			</div>
			<div class="w-full max-w-sm space-y-2">
				<form class="flex space-x-2">
					<Input
						class="bg-primary max-w-lg flex-1"
						placeholder="Enter your email"
						type="email"
					/>
					<Button type="submit">
						Get Started
						<ArrowRight class="ml-2 size-4" />
					</Button>
				</form>
				<p class="mt-4 text-sm">Start your 14-day free trial. No credit card required.</p>
			</div>
		</div>
	</div>
</section>
