<script lang="ts">
	import { ReceiptText, Menu } from '@lucide/svelte'
	import { Button } from '$/components/ui/button'
	import { Logo, LightSwitch } from '$/components/common'
	import { getUtilsStore } from '$/stores'
	import { SheetTypes } from '@repo/common'

	const utilsStore = getUtilsStore()
	const doLogin = () => {
		console.log('do login clicked')
		utilsStore.setHomeSheet(SheetTypes.Login)
	}
	const doSignUp = () => {
		utilsStore.setHomeSheet(SheetTypes.SignUp)
	}
</script>

<header class="bg-background sticky -top-1 z-50 border-b">
	<div class="container mx-auto flex h-16 items-center justify-between px-2 lg:px-4">
		<Logo />
		<Button variant="ghost" size="icon" class="md:hidden">
			<Menu class="size-6" />
			<span class="sr-only">Toggle menu</span>
		</Button>
		<nav class="hidden flex-1 justify-center gap-4 sm:gap-6 md:flex">
			<a class="text-sm font-medium underline-offset-4 hover:underline" href="#features">
				Features
			</a>
			<a class="text-sm font-medium underline-offset-4 hover:underline" href="#how-it-works">
				How It Works
			</a>
			<a class="text-sm font-medium underline-offset-4 hover:underline" href="#pricing">
				Pricing
			</a>
		</nav>
		<div class="flex items-center gap-2">
			<Button variant="outline" size="sm" onclick={doLogin}>Login</Button>
			<Button size="sm" onclick={doSignUp}>Sign Up</Button>
			<LightSwitch />
		</div>
	</div>
</header>
