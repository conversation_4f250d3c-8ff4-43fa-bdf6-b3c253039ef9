<script lang="ts">
	import * as Sheet from '$/components/ui/sheet'
	import { getAuthStore, getUtilsStore } from '$/stores'
	import { Marketing } from './marketing'
	import { Login, SignUp, SignUpCode } from './auth'
	import { SheetTypes } from '@repo/common'

	const utilsStore = getUtilsStore()
	const authStore = getAuthStore()
	let sheetSide: 'right' | 'top' | 'bottom' | 'left' | undefined = $state('right')
	let nameInput = $state<HTMLInputElement>()
	const componentCollection = [
		{
			componentName: SheetTypes.Marketing,
			component: Marketing,
			dialogClass: '',
			componentProps: {}
		},
		{
			componentName: SheetTypes.Login,
			component: Login,
			dialogClass: '',
			componentProps: {}
		},
		{
			componentName: SheetTypes.SignUp,
			component: SignUp,
			dialogClass: '',
			componentProps: {}
		},
		{
			componentName: SheetTypes.SignUpCode,
			component: SignUpCode,
			dialogClose: '',
			componentProps: {}
		}
	]

	let { CurrentComponent, componentProps }: any = $derived.by(() => {
		const componentEntry = componentCollection.firstOrNull(
			(k) => k.componentName === utilsStore.currentHomeSheet
		)
		return {
			CurrentComponent: componentEntry?.component,
			componentProps: componentEntry?.componentProps
		}
	})
</script>

<Sheet.Root open={!authStore.user}>
	<Sheet.Trigger />
	<Sheet.Content
		escapeKeydownBehavior="ignore"
		interactOutsideBehavior="ignore"
		class="w-full overflow-y-auto p-0 sm:max-w-xl sm:p-1 md:max-w-2xl lg:max-w-3xl xl:max-w-4xl"
		side={sheetSide}
		showCloseButton={false}
		onOpenAutoFocus={(e) => {
			e.preventDefault()
			nameInput?.focus()
		}}
	>
		<div class="flex min-h-screen flex-col">
			<CurrentComponent {...componentProps} />
		</div>
	</Sheet.Content>
</Sheet.Root>
