<script lang="ts">
	import * as Breadcrumb from '$/components/ui/breadcrumb'
	import { page } from '$app/state'
	import { Breadcrumbs } from 'svelte-breadcrumbs'
</script>

<Breadcrumbs
	url={page.url}
	routeId={page.route.id}
	pageData={undefined}
	skipRoutesWithNoPage={true}
>
	{#snippet children({ crumbs })}
		<Breadcrumb.Root>
			<Breadcrumb.List>
				<Breadcrumb.Item class="hidden md:block">
					<Breadcrumb.Link href="#">SpenDeed</Breadcrumb.Link>
				</Breadcrumb.Item>
				{#each crumbs as c (c.url)}
					<Breadcrumb.Separator class="hidden md:block" />
					<Breadcrumb.Item>
						<Breadcrumb.Link href={c.url} class="decoration-none">
							{c.title}
						</Breadcrumb.Link>
					</Breadcrumb.Item>
				{/each}
			</Breadcrumb.List>
		</Breadcrumb.Root>
	{/snippet}
</Breadcrumbs>
