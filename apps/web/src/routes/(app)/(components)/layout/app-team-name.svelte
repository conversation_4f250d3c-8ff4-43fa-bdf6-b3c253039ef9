<script lang="ts">
	import * as Avatar from '$/components/ui/avatar'
	import { teamSignal } from '$/signals'
	import { getTeamStore } from '$/stores'
	import { getInitials } from '@repo/common'

	let teamStore = getTeamStore()
	const teamInitials = $derived(
		teamStore.team?.teamName ? getInitials(teamStore.team.teamName, '') : ''
	)
	$inspect('team Name', teamInitials)
</script>

<span class="font-semibold">{teamInitials}</span>

<!-- <Avatar.Root class="h-8 w-8 rounded-lg">
	<Avatar.Image src="" alt={teamName} />
	<Avatar.Fallback class="rounded-lg">{teamInitials}</Avatar.Fallback>
</Avatar.Root> -->
