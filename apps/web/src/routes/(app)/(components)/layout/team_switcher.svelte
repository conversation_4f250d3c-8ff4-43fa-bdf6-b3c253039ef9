<script lang="ts">
	import * as Sidebar from '$/components/ui/sidebar'
	import { getTeamStore } from '$/stores'
	import { default as AppTeamName } from './app-team-name.svelte'
	import 'linq-extensions'

	const teamStore = getTeamStore()
</script>

<Sidebar.Menu>
	<Sidebar.MenuItem>
		<Sidebar.MenuButton
			size="lg"
			class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
		>
			<div class="flex aspect-square size-8 items-center justify-center rounded-none">
				<AppTeamName />
			</div>
			<div class="grid flex-1 text-left text-sm leading-tight">
				<span class="truncate font-semibold">
					{teamStore.team?.teamName}
				</span>
				<span class="truncate text-xs">{teamStore.team?.currentPlan}</span>
			</div>
			<!--						<ChevronsUpDown class="ml-auto" />-->
		</Sidebar.MenuButton>
	</Sidebar.MenuItem>
</Sidebar.Menu>
