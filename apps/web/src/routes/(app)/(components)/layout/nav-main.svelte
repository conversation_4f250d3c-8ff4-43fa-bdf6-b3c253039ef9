<script lang="ts">
	import * as Collapsible from '$/components/ui/collapsible'
	import * as Sidebar from '$/components/ui/sidebar'
	import { ChevronRight, Building } from '@lucide/svelte'

	let {
		items
	}: {
		items: {
			title: string
			url: string
			// this should be `Component` after @lucide/svelte updates types

			icon?: any
			isActive?: boolean
			items?: {
				title: string
				url: string
			}[]
		}[]
	} = $props()
	const dashboard = {
		title: 'Dashboard',
		url: '/',
		icon: Building,
		isActive: false
	}
</script>

<Sidebar.Group>
	<Sidebar.GroupLabel>Modules</Sidebar.GroupLabel>
	<Sidebar.Menu>
		<Sidebar.MenuItem>
			<Sidebar.MenuButton>
				<dashboard.icon />
				<a href={dashboard.url}>
					<span>{dashboard.title}</span>
				</a>
			</Sidebar.MenuButton>
		</Sidebar.MenuItem>
		{#each items as mainItem (mainItem.title)}
			<Collapsible.Root open={mainItem.isActive} class="group/collapsible">
				{#snippet child({ props })}
					<Sidebar.MenuItem {...props}>
						<Collapsible.Trigger>
							{#snippet child({ props })}
								<Sidebar.MenuButton tooltipContent={mainItem.title} {...props}>
									{#if mainItem.icon}
										<mainItem.icon />
									{/if}
									<span>{mainItem.title}</span>
									<ChevronRight
										class="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"
									/>
								</Sidebar.MenuButton>
							{/snippet}
						</Collapsible.Trigger>
						<Collapsible.Content>
							{#if mainItem.items}
								<Sidebar.MenuSub>
									{#each mainItem.items ?? [] as subItem (subItem.title)}
										<Sidebar.MenuSubItem>
											<Sidebar.MenuSubButton>
												{#snippet child({ props })}
													<a href={subItem.url} {...props}>
														<span>{subItem.title}</span>
													</a>
												{/snippet}
											</Sidebar.MenuSubButton>
										</Sidebar.MenuSubItem>
									{/each}
								</Sidebar.MenuSub>
							{/if}
						</Collapsible.Content>
					</Sidebar.MenuItem>
				{/snippet}
			</Collapsible.Root>
		{/each}
	</Sidebar.Menu>
</Sidebar.Group>
