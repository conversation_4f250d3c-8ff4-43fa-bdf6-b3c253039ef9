<script lang="ts">
	import * as Form from '$/components/ui/form'
	import * as InputOTP from '$/components/ui/input-otp'
	import Cookies from 'js-cookie'
	import { stringify } from 'superjson'
	import { getAuthStore, getUtilsStore } from '$/stores'
	import {
		SheetTypes,
		signUpCodeFormSchema,
		type FormMessage,
		type ISessionData,
		type SignUpCodeFormType
	} from '@repo/common'
	import { defaults, superForm } from 'sveltekit-superforms'
	import { valibot } from 'sveltekit-superforms/adapters'
	import { default as AuthWrapper } from './auth_wrapper.svelte'
	import { FormAlert } from '$/components/common'
	import { Loader2 } from '@lucide/svelte'
	import { Button } from '$/components/ui/button'
	import { page } from '$app/state'
	import { onMount } from 'svelte'
	import { getFlashModule } from '$/lib/helpers'
	import { PUBLIC_COOKIE_SECRET } from '$env/static/public'
	import { toast } from 'svelte-sonner'
	import { passlockClient } from '$/lib/clients'
	import { getActorOrEvent } from '$/lib/actor-core.svelte'

	const utilsStore = getUtilsStore()
	const authStore = getAuthStore()
	const form = superForm<SignUpCodeFormType, FormMessage>(
		defaults(valibot(signUpCodeFormSchema)),
		{
			id: 'signup-code-form',
			delayMs: 0,
			invalidateAll: true,
			multipleSubmits: 'prevent',
			SPA: true,
			validators: valibot(signUpCodeFormSchema),
			onUpdate: async ({ form, cancel }) => {
				if (!form.valid) return
				try {
					const { isOk, err } = await passlockClient.verifyEmail({ form, cancel })
					if (!isOk) {
						toast.error(err)
						cancel()
					}
					form.data.returnUrl = page.url.pathname
					const { data: formData } = form
					const { actor: authActor } = getActorOrEvent('authActor')
					const result = await authActor.verifyEmail(formData)
					if (result.status === 'success') {
						const sessionData = result.data?.session as ISessionData
						if (sessionData?.user?.id) {
							authStore.updateStore(sessionData)
							Cookies.set(PUBLIC_COOKIE_SECRET, btoa(stringify(result.data?.session)))
							toast.success('Email verified successfully')
						} else {
							toast.error('Email verification failed')

							cancel()
						}
					} else {
						console.log('code verify failure result', result.text)
						toast.error(`Signup failed: ${result.text}`)
						cancel()
					}
				} catch (err) {
					console.log('sign up code error ', (err as any)?.message)
					toast.error(`Signup failed: ${(err as Error).message}`)
					cancel()
				}
			},
			...getFlashModule()
		}
	)
	const { form: formData, enhance, submitting, message, allErrors } = form

	let resendDisabled = $state(false)
	const resend = async () => {
		if (utilsStore.st.homeSheet?.authingUserId) {
			console.log('has user for resend')
			resendDisabled = true
			await passlockClient.resendEmail({
				userId: utilsStore.st.homeSheet?.authingUserId,
				method: 'code'
			})

			setTimeout(() => {
				resendDisabled = false
			}, 3000)
		} else {
			console.log('no user to resend to ')
			utilsStore.toastError('No user found to resend to.')
			utilsStore.setHomeSheet(SheetTypes.SignUp)
		}
	}
	const signIn = () => {
		utilsStore.setHomeSheet(SheetTypes.Login)
	}

	onMount(() => {
		const email = utilsStore.st.currentAuthEmail
		if (email) $formData.email = email
	})
</script>

<AuthWrapper pageTitle="Sign Up" pageDescription="Verify your Email">
	<div class="grid gap-6">
		<form method="POST" use:enhance>
			<FormAlert {message} />
			<Form.Field {form} name="code" class="mx-auto justify-items-center">
				<Form.Control>
					{#snippet children({ props })}
						<InputOTP.Root maxlength={6} {...props} bind:value={$formData.code}>
							{#snippet children({ cells })}
								<InputOTP.Group>
									{#each cells.slice(0, 2) as cell (cell)}
										<InputOTP.Slot {cell} />
									{/each}
								</InputOTP.Group>
								<InputOTP.Separator />
								<InputOTP.Group>
									{#each cells.slice(2, 4) as cell (cell)}
										<InputOTP.Slot {cell} />
									{/each}
								</InputOTP.Group>
								<InputOTP.Separator />
								<InputOTP.Group>
									{#each cells.slice(4, 6) as cell (cell)}
										<InputOTP.Slot {cell} />
									{/each}
								</InputOTP.Group>
							{/snippet}
						</InputOTP.Root>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Button disabled={$submitting || $allErrors?.length > 0} class="mt-4 w-full">
				{#if $submitting}
					<Loader2 class="mr-2 size-4 animate-spin" />
				{/if}
				{$submitting ? 'Please wait...' : 'Verify Email'}
			</Form.Button>
		</form>
		<div class="relative">
			<p class="text-muted-foreground px-8 text-center text-sm">
				{#if resendDisabled}
					Email sent
				{:else}
					Still waiting?
					<button onclick={() => resend()} type="button" class="hover:underline"
						>Resend code</button
					>

					<Button variant="link" onclick={signIn} class="hover:text-brand no-underline">
						Already have an account? Sign In
					</Button>
				{/if}
			</p>
		</div>
	</div>
</AuthWrapper> -->
