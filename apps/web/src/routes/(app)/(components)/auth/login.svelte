<script lang="ts">
	import * as Form from '$/components/ui/form'
	import { Button } from '$/components/ui/button'
	import { Input } from '$/components/ui/input'
	import { stringify } from 'superjson'
	import { getAuthStore, getUtilsStore } from '$/stores'
	import { toast } from 'svelte-sonner'
	import {
		SheetTypes,
		type FormMessage,
		type ISessionData,
		loginFormSchema,
		type LoginFormType
	} from '@repo/common'
	import { Loader2 } from '@lucide/svelte'
	import { onMount } from 'svelte'
	import Cookies from 'js-cookie'
	import { defaults, superForm } from 'sveltekit-superforms'
	import { valibot } from 'sveltekit-superforms/adapters'
	import { FormAlert, HiddenInput } from '$/components/common'
	import { default as AuthWrapper } from './auth_wrapper.svelte'
	import { page } from '$app/state'
	import { getFlashModule } from '$/lib/helpers'
	import { checkWebAuthnSupport } from '$/lib'
	import { PUBLIC_COOKIE_SECRET } from '$env/static/public'
	import { getLocalEmail } from '@repo/common'
	import { passlockClient } from '$/lib/clients'
	import { getAuthActor, useActor } from '$/lib/actor-core.svelte'

	let passKeyEnabled = $state(true)
	let currentRoute = $derived(page.url.pathname ?? '/')
	const utilsStore = getUtilsStore()
	const authStore = getAuthStore()
	const form = superForm<LoginFormType, FormMessage>(defaults(valibot(loginFormSchema)), {
		id: 'login-form',
		validators: valibot(loginFormSchema),
		multipleSubmits: 'prevent',
		SPA: true,
		onUpdate: async ({ form, cancel }) => {
			if (!form.valid) return
			try {
				form.data.returnUrl = currentRoute
				form.data.authType = passKeyEnabled ? 'passkey' : 'email'
				const { isOk, err } = await passlockClient.login<LoginFormType>({ form, cancel })
				if (!isOk) {
					toast.error(err)
					cancel()
				}
				const authActor = getAuthActor()
				const result = await authActor.login(form.data)
				if (result.status === 'success') {
					const sessionData = result.data?.session as ISessionData
					if (sessionData?.user?.id) {
						authStore.updateStore(sessionData)
						Cookies.set(PUBLIC_COOKIE_SECRET, btoa(stringify(result.data?.session)))
						toast.success('Login successful')
					} else {
						toast.error('Login failed')
						cancel()
					}
				} else {
					console.log(`login failure result: status:${result.status} details:${result.text}`)
					toast.error(`Login failed: ${result.text}`)
					cancel()
				}
			} catch (err) {
				console.error(`Error logging in ${(err as Error).message}`)
				console.error(`Error logging in ${(err as Error).stack}`)
				console.error(`Error logging in ${(err as Error).cause}`)
				toast.error(`Error logging in ${(err as Error).message}`)
				cancel()
			}
		},
		...getFlashModule()
	})
	const { form: formData, enhance, submitting, message, allErrors } = form

	const switchToSignUp = () => {
		utilsStore.setHomeSheet(SheetTypes.SignUp)
	}

	onMount(async () => {
		const webAuthNSupport = await checkWebAuthnSupport()
		loginButtonText = webAuthNSupport.isAvailable ? 'Login with Passkey' : 'Login with Email'
		const email = getLocalEmail()
		if (email) $formData.email = email
	})

	let loginButtonText = $state('Log In')
	let submittingPasskey = $derived.by(() => $submitting && $formData.authType === 'passkey')
	let readonlyEmail = $derived.by(() => submittingPasskey || undefined)
</script>

<AuthWrapper pageTitle="Welcome back" pageDescription="Sign in to your account">
	<div class="grid gap-6">
		<form method="POST" class="space-y-6" use:enhance>
			<FormAlert {message} />
			<fieldset disabled={$submitting} class="grid gap-2">
				<Form.Field {form} name="email">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Email</Form.Label>
							<Input
								{...props}
								bind:value={$formData.email}
								autocomplete="email"
								readonly={readonlyEmail}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="returnUrl">
					<Form.Control>
						{#snippet children({ props })}
							<HiddenInput {props} value={currentRoute} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Button disabled={submittingPasskey || $allErrors?.length > 0}>
					{#if submittingPasskey}
						<Loader2 class="mr-2 size-4 animate-spin" />
					{/if}
					{submittingPasskey ? 'Please wait...' : loginButtonText}
				</Form.Button>
			</fieldset>
		</form>
		<div class="relative">
			<p class="text-muted-foreground px-8 text-center text-sm">
				<Button variant="link" onclick={switchToSignUp} class="hover:text-brand no-underline">
					Don't have an account? Sign Up
				</Button>
			</p>
		</div>
		<!-- {#if PUBLIC_APPLE_CLIENT_ID || PUBLIC_GOOGLE_CLIENT_ID}
			<AuthDivider />
		{/if}
		<div class="flex w-full flex-col items-stretch gap-2 min-[460px]:flex-row">
			{#if PUBLIC_APPLE_CLIENT_ID}
				<SignInWithApple
					context="signin"
					submitting={submittingApple}
					on:principal={applyUpdate}
				/>
			{/if}

			{#if PUBLIC_GOOGLE_CLIENT_ID}
				<SignInWithGoogle
					context="signin"
					submitting={submittingGoogle}
					on:principal={applyUpdate}
				/>
			{/if}
		</div> -->
	</div>
</AuthWrapper>
