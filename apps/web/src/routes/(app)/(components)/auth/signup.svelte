<script lang="ts">
	import { FormAlert } from '$/components/common'
	import { Button } from '$/components/ui/button'
	import * as Form from '$/components/ui/form'
	import { Input } from '$/components/ui/input'
	import { getUtilsStore } from '$/stores'
	import { defaults, setError, superForm } from 'sveltekit-superforms'
	import { valibotClient } from 'sveltekit-superforms/adapters'
	import { default as AuthWrapper } from './auth_wrapper.svelte'
	import { onMount } from 'svelte'
	import { PasskeyIcon } from '$/components/icons'
	import { getFlashModule, checkWebAuthnSupport } from '$/lib'
	import { toast } from 'svelte-sonner'
	import { PUBLIC_APP_NAME } from '$env/static/public'
	import { Textarea } from '$/components/ui/textarea'
	import {
		saveEmailLocally,
		SheetTypes,
		signUpFormSchema,
		type FormMessage,
		type SignUpFormType
	} from '@repo/common'
	import { passlockClient } from '$/lib/clients'
	import { getActorOrEvent } from '$/lib/actor-core.svelte'

	let appName = $state(PUBLIC_APP_NAME)
	let passKeyEnabled = $state(true)
	const utilsStore = getUtilsStore()
	const form = superForm<SignUpFormType, FormMessage>(
		defaults({} as any, valibotClient(signUpFormSchema)),
		{
			id: 'signup-form',
			validators: valibotClient(signUpFormSchema),
			multipleSubmits: 'prevent',
			dataType: 'json',
			SPA: true,
			onUpdate: async ({ form, cancel }) => {
				form.data.authType = passKeyEnabled ? 'passkey' : 'email'
				form.data.acceptTerms = true
				form.data.verificationMethod = 'code'
				if (!form.valid) {
					setError(form, '', 'Please fill in all fields')
					cancel()
				}

				utilsStore.setCurrentAuthEmail((form.data as any).email)
				try {
					const { isOk, err } = await passlockClient.register({ form, cancel })
					if (!isOk) {
						toast.error(err)
						cancel()
					}
					if (form.data.token) {
						const { data: formData } = form
						const { actor: authActor } = getActorOrEvent('authActor')
						const apiResult = await authActor.signUp(formData)

						console.log('Signup Form Response', apiResult)
						if (apiResult.status === 'success') {
							utilsStore.st.homeSheet = {
								authingUserId: apiResult.data?.userId,
								authingUserEmail: formData.email
							}
							if (formData.authType === 'passkey') {
								saveEmailLocally(formData.email)
								//TODO: routing for pass key
								utilsStore.setHomeSheet(SheetTypes.SignUpCode)
								toast.success(
									'Signup successful! Please check your email for verification code.'
								)
							} else {
								utilsStore.setHomeSheet(SheetTypes.Login)
								toast.error(`Signup failed: ${apiResult.text}`)
								cancel()
							}
						} else {
							toast.error(`Signup failed: ${apiResult.text}`)
							cancel()
						}
					} else {
						toast.error('Signup Failed, please try again')
						cancel()
					}
				} catch (err) {
					console.log('signup error ', (err as any)?.message)
					console.log('signup error ', (err as any)?.stack)
					toast.error(`Signup failed: ${(err as Error).message}`)
					cancel()
				}
			},
			...getFlashModule()
		}
	)
	const { form: formData, enhance, submitting, message, allErrors } = form

	const switchToLogin = () => {
		utilsStore.setHomeSheet(SheetTypes.Login)
	}

	let signupButtonText = $state('Sign Up')
	let readonly = $derived(($formData.token?.length ?? 0 > 0) ? true : undefined)
	onMount(async () => {
		const webAuthNSupport = await checkWebAuthnSupport()
		passKeyEnabled = webAuthNSupport.isAvailable
		signupButtonText = passKeyEnabled ? 'Sign Up with Passkey' : 'Sign Up'
	})
</script>

<AuthWrapper pageTitle="Sign Up" pageDescription="Create your account">
	<div class="grid gap-6">
		<form method="POST" class="space-y-6" use:enhance>
			<FormAlert {message} />
			<fieldset disabled={$submitting} class="grid gap-2">
				<Form.Field {form} name="givenName">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>First Name</Form.Label>
							<Input
								{...props}
								{readonly}
								placeholder="First Name"
								bind:value={$formData.givenName}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="familyName">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Last Name</Form.Label>
							<Input
								{...props}
								placeholder="Last Name"
								autocomplete="family-name"
								{readonly}
								bind:value={$formData.familyName}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="email">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Email</Form.Label>
							<Input
								type="email"
								{...props}
								placeholder="Email"
								autocomplete="email"
								{readonly}
								bind:value={$formData.email}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="businessName">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Business Name</Form.Label>
							<Input
								{...props}
								placeholder="Business Name"
								{readonly}
								bind:value={$formData.businessName}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="phoneNumber">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Phone Number</Form.Label>
							<Input
								{...props}
								placeholder="Phone Number"
								{readonly}
								bind:value={$formData.phoneNumber}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="businessAddress">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Business Address</Form.Label>
							<Textarea
								{...props}
								placeholder="Business Address"
								{readonly}
								bind:value={$formData.businessAddress}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			</fieldset>
			<Form.Button disabled={$submitting || $allErrors?.length > 0} class="mt-4 w-full">
				{#if $submitting}
					<PasskeyIcon class="animate-caret-blink size-5 fill-current" />
				{/if}
				{$submitting ? 'Please wait...' : signupButtonText}
			</Form.Button>
		</form>
		<div class="relative">
			<p class="text-muted-foreground px-8 text-center text-sm">
				<Button variant="link" onclick={switchToLogin} class="hover:text-brand no-underline">
					Already have an account? Sign In
				</Button>
			</p>
		</div>
		<!-- {#if PUBLIC_APPLE_CLIENT_ID || PUBLIC_GOOGLE_CLIENT_ID}
			<AuthDivider />
		{/if}
		<div class="flex w-full flex-col items-stretch gap-2 min-[460px]:flex-row">
			{#if PUBLIC_APPLE_CLIENT_ID}
				<SignInWithApple
					context="signup"
					disabled={disableSocialBtns}
					on:principal={() =>
						updateForm(form, async () => {
							await tick()
							form.submit()
						})}
				/>
			{/if}

			{#if PUBLIC_GOOGLE_CLIENT_ID}
				<SignInWithGoogle
					context="signup"
					disabled={disableSocialBtns}
					on:principal={() =>
						updateForm(form, async () => {
							await tick()
							form.submit()
						})}
				/>
			{/if}
		</div> -->
	</div>
</AuthWrapper>
