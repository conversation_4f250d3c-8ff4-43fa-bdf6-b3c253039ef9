<script lang="ts">
	import { AppShell, AppShellHeader } from '$/components/layout'
	import { teamSignal } from '$/signals'
	import { MainModuleDashboard } from './(components)'

	const teamStore = $derived(teamSignal.getTeamData())
	const heading = $derived(`${teamStore?.teamName ?? 'SpenDeed'} Dashboard`)
	const subHeading = $state('Birds Eye View of Your Business')
	//$effect.pre(authGuard)
</script>

<AppShell pageTitle="Dashboard">
	<AppShellHeader {heading} {subHeading} />
	<MainModuleDashboard />
</AppShell>
