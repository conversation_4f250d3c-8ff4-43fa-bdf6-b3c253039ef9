<script lang="ts">
	import { NoData } from '$/components/common'
	import { Button } from '$/components/ui/button'
	import type { AddTeamMemberFormSchemaType, IDynamicModal } from '@repo/common'
	import { List } from '@lucide/svelte'
	import type { SuperValidated } from 'sveltekit-superforms/client'
	import { default as InvitesTable } from './invites-table.svelte'
	import { getUtilsStore } from '$/stores'
	import { membersInviteSignal } from '$/signals'

	let { addTeamMemberForm }: { addTeamMemberForm: SuperValidated<AddTeamMemberFormSchemaType> } =
		$props()
	const uStore = getUtilsStore()

	let hasInvites = $derived(membersInviteSignal.getPendingInvites().count() > 0)

	const startCreateTeamMember = () => {
		const newTeamMemberForm: IDynamicModal = {
			canClose: false,
			title: 'Invite new Team Member',
			componentName: 'AddTeamMember',
			size: 'lg',
			color: 'default',
			description: 'Send Team member invite to your team',
			componentProps: {
				addTeamMemberForm
			}
		}
		uStore.openDynamicModal(newTeamMemberForm)
	}
</script>

{#if hasInvites}
	<div class="space-y-4">
		<InvitesTable />
	</div>
{:else}
	{#snippet listSnippet()}
		<List class="h-10 w-10" />
	{/snippet}
	<NoData
		noDataTitle="No Pending Invites"
		noDataDescription="You do not have any pending invites. start creating employee Data"
		snippet={listSnippet}
	>
		<div class="flex justify-between space-x-2">
			<Button variant="default" onclick={startCreateTeamMember}>New Invite</Button>
		</div>
	</NoData>
{/if}
