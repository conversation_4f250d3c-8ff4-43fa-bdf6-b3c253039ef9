<script lang="ts">
	import { settingsFormSchema, type FormMessage, type SettingsFormSchemaType } from '@repo/common'
	import { CalendarIcon, HardDriveUpload, Loader2, Pencil } from '@lucide/svelte'
	import { Calendar } from '$/components/ui/calendar'
	import { valibot } from 'sveltekit-superforms/adapters'
	import { defaults, superForm } from 'sveltekit-superforms/client'
	import * as Card from '$/components/ui/card'
	import * as Form from '$/components/ui/form'
	import * as Popover from '$/components/ui/popover'
	import { Input } from '$/components/ui/input'
	import { Button } from '$/components/ui/button'
	import { Label } from '$/components/ui/label'
	import { FormAlert, HiddenInput, NoData } from '$/components/common'
	import { today, getLocalTimeZone, CalendarDate } from '@internationalized/date'
	import { cn } from 'tailwind-variants'
	import { toDate } from 'date-fns'
	import { teamSignal } from '$/signals'
	import type { TeamInsertDbType } from '@repo/drizzle'

	const teamData = teamSignal.getTeamData()
	const teamState = $derived(teamData)
	const form = superForm<SettingsFormSchemaType, FormMessage>(
		defaults(teamData as any, valibot(settingsFormSchema)),
		{
			SPA: true,
			resetForm: true,
			clearOnSubmit: 'errors-and-message',
			dataType: 'json',
			validators: valibot(settingsFormSchema),
			async onUpdate({ form }) {
				if (!form.valid) return
				try {
					const { data: formData } = form

					const settingsObj: Partial<TeamInsertDbType> = {
						phoneNumber: formData.phoneNumber,
						businessEmail: formData.businessEmail,
						teamName: formData.teamName,
						businessRegNo: formData.businessRegNo,
						registrationDate: formData.registrationDate
							? toDate(formData.registrationDate).getTime().toString()
							: undefined
					}
					teamSignal.updateOne(
						{ id: teamData.id },
						{
							$set: { ...settingsObj }
						}
					)

					form.message = {
						status: 'success',
						text: 'Settings updated successfully!'
					}
				} catch (error) {
					console.error(error)
					form.message = {
						text: 'An error occurred while updating the settings',
						status: 'error'
					}
				}
			}
		}
	)
	const { form: formValues, enhance, message, submitting, allErrors, delayed } = form
	let editSettings = $state(false)
	const noDataString = $state(
		'You may not have the permissions required to view this data. Please contact your Team administrator.'
	)

	let regDateValue = $state<Date | undefined>()

	let placeholder = $state(today(getLocalTimeZone()))
	let regDate = $derived(
		teamState?.registrationDate ? new Date(teamState?.registrationDate).toString() : ''
	)
	$effect(() => {
		regDateValue = $formValues.registrationDate ? $formValues.registrationDate : undefined
	})
	$inspect(teamData, teamData.businessRegNo).with(console.trace)
</script>

{#if teamData?.id}
	<div class="grid auto-cols-auto grid-flow-col gap-2 max-md:grid-rows-2">
		<Card.Root class="w-full rounded-none">
			<Card.Header>
				<Card.Title class="flex items-center justify-between">
					Current Settings
					<Button variant="ghost" size="icon" onclick={() => (editSettings = true)}>
						<Pencil class="size-4" />
					</Button>
				</Card.Title>
			</Card.Header>
			<Card.Content>
				<div class="grid grid-cols-1 gap-4 space-y-4 md:grid-cols-3">
					<div class="space-y-1">
						<Label>Team Name</Label>
						<p class="text-muted-foreground text-sm">{teamState?.teamName}</p>
					</div>
					<div class="space-y-1">
						<Label>Email</Label>
						<p class="text-muted-foreground text-sm">{teamState?.businessEmail}</p>
					</div>
					<div class="space-y-1">
						<Label>Phone Number</Label>
						<p class="text-muted-foreground text-sm">{teamState?.phoneNumber}</p>
					</div>
					<div class="space-y-1">
						<Label>Registration No.</Label>
						<p class="text-muted-foreground text-sm">{teamState?.businessRegNo}</p>
					</div>
					<div class="space-y-1">
						<Label>Registration Date:</Label>
						<p class="text-muted-foreground text-sm">
							{new Date(teamState?.registrationDate ?? '').toLocaleDateString()}
						</p>
					</div>
				</div>
			</Card.Content>
		</Card.Root>
		{#if editSettings}
			<Card.Root class="w-full">
				<Card.Header>
					<Card.Title>Edit Settings</Card.Title>
				</Card.Header>
				<Card.Content>
					<form method="POST" use:enhance class="space-y-4">
						<FormAlert {message} />
						<Form.Field {form} name="teamName" class="space-y-2">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Team Name</Form.Label>
									<Input
										{...props}
										placeholder="Team Name"
										bind:value={$formValues.teamName}
									/>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
						<Form.Field {form} name="businessEmail" class="space-y-2">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Email</Form.Label>
									<Input
										{...props}
										placeholder="Business Email"
										bind:value={$formValues.businessEmail}
									/>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
						<Form.Field {form} name="phoneNumber" class="space-y-2">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Phone Number</Form.Label>
									<Input
										{...props}
										placeholder="Phone Number"
										bind:value={$formValues.phoneNumber}
									/>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
						<Form.Field {form} name="businessRegNo" class="space-y-2">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Registration No:</Form.Label>
									<Input
										{...props}
										placeholder="Registration No"
										bind:value={$formValues.businessRegNo}
									/>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
						<Form.Field {form} name="registrationDate" class="flex flex-col">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Date Incorporated</Form.Label>
									<Popover.Root>
										<Popover.Trigger {...props}>
											{#snippet child({ props })}
												<Button
													variant="outline"
													class={cn(
														'w-[280px] justify-start pl-4 text-left font-normal',
														!regDateValue && 'text-muted-foreground'
													).toString()}
													{...props}
												>
													{regDate}
													<CalendarIcon class="ml-auto size-4 opacity-50" />
												</Button>
											{/snippet}
										</Popover.Trigger>
										<Popover.Content class="w-auto p-0" side="bottom">
											<Calendar
												type="single"
												value={regDateValue as any}
												bind:placeholder
												minValue={new CalendarDate(1900, 1, 1)}
												maxValue={today(getLocalTimeZone())}
												calendarLabel="Date of birth"
												onValueChange={(v) => {
													if (v) {
														$formValues.registrationDate =
															v.toDate(getLocalTimeZone())
													} else {
														$formValues.registrationDate = new Date()
													}
												}}
											/>
										</Popover.Content>
									</Popover.Root>
									<!-- <Input
										{...props}
										type="date"
										placeholder="Date Incorporated"
										bind:value={$formValues.registrationDate}
									/> -->
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
						<Form.Field {form} name="id">
							<Form.Control>
								{#snippet children({ props })}
									<HiddenInput {props} value={$formValues.id} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
						<Form.Button disabled={$allErrors.length > 0 || $submitting} class="mt-4 w-full">
							{#if $delayed}
								<Loader2 class="mr-2 size-4 animate-spin" />
							{/if}
							{$submitting ? 'Please wait...' : 'Update Settings'}
						</Form.Button>
					</form>
				</Card.Content>
			</Card.Root>
		{/if}
	</div>
{:else}
	{#snippet iconSnippet()}
		<HardDriveUpload class="h-10 w-10" />
	{/snippet}

	<NoData
		noDataTitle="No Teams Settings Data"
		noDataDescription="You may not have the permissions required to view this data"
		snippet={iconSnippet}
	>
		<div class="flex justify-between space-x-2">
			<p>{noDataString}</p>
		</div>
	</NoData>
{/if}
