<script lang="ts">
	import type { LayoutData } from './$types'
	import { DynamicModal } from '$/components/common'
	import type { IComponentCache } from '@repo/common'
	import { AddTeamMember } from './staff/(components)'
	import { AddBankDetails, AddPaymentProvider } from './payment-engine/(components)'

	let { children }: { data: LayoutData; children: any } = $props()

	const componentsCollection = $state<IComponentCache[]>([
		{
			componentName: 'AddTeamMember',
			component: AddTeamMember,
			dialogClass: 'md:min-w-[920px]'
		},
		{
			componentName: 'AddBankDetails',
			component: AddBankDetails,
			dialogClass: 'md:min-w-[620px]'
		},
		{
			componentName: 'AddPaymentProvider',
			component: AddPaymentProvider,
			dialogClass: 'md:min-w-[620px]'
		}
	])
</script>

{@render children?.()}
<DynamicModal {componentsCollection} />
