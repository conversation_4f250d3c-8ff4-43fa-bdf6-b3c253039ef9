<script lang="ts">
	import { type IDynamicModal } from '@repo/common'
	import Ellipsis from '@lucide/svelte/icons/ellipsis'
	import type { Row } from '@tanstack/table-core'
	import * as DropdownMenu from '$/components/ui/dropdown-menu'
	import { Button } from '$/components/ui/button'
	import { getUtilsStore } from '$/stores'
	import { TeamMemberStatuses, type TeamMemberStatus } from '@repo/common'
	import type { MemberDbType } from '@repo/drizzle'
	import { membersSignal } from '$/signals'

	let { row }: { row: Row<MemberDbType> } = $props()
	const uStore = getUtilsStore()
	let toggeStatusText = $state<string>(
		row.original.memberStatus === TeamMemberStatuses.Active ? 'Deactivate User' : 'Activate User'
	)
	const toggleUserStatus = async () => {
		const status =
			row.original.memberStatus === TeamMemberStatuses.Active
				? TeamMemberStatuses.InActive
				: TeamMemberStatuses.Active
		await updateMemberStatus(status)
	}
	const editTeamMember = () => {
		const newTeamMemberForm: IDynamicModal = {
			canClose: false,
			title: 'Edit Team Member',
			componentName: 'AddTeamMember',
			size: 'lg',
			color: 'default',
			description: 'Edit Team member details',
			componentProps: {
				data: row.original
			}
		}
		uStore.openDynamicModal(newTeamMemberForm)
	}
	const setMemberOnLeave = async () => {
		await updateMemberStatus(TeamMemberStatuses.OnLeave)
	}
	const updateMemberStatus = async (status: TeamMemberStatus) => {
		try {
			membersSignal.updateOne({ id: row.original.id }, { $set: { memberStatus: status } })
			return uStore.toastSuccess('Status updated successfully')
		} catch (error) {
			console.error(error)
			return uStore.toastError(`Failed to update status: ${(error as Error).message}`)
		}
	}
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger>
		{#snippet child({ props })}
			<Button {...props} variant="ghost" class="data-[state=open]:bg-muted flex h-8 w-8 p-0">
				<Ellipsis />
				<span class="sr-only">Open Menu</span>
			</Button>
		{/snippet}
	</DropdownMenu.Trigger>
	<DropdownMenu.Content class="w-[160px]" align="end">
		<DropdownMenu.Item onclick={editTeamMember}>Edit</DropdownMenu.Item>
		<DropdownMenu.Item onclick={setMemberOnLeave}>Set On Leave</DropdownMenu.Item>
		<DropdownMenu.Separator />
		<DropdownMenu.Item onclick={() => toggleUserStatus()}>
			{toggeStatusText}
			<DropdownMenu.Shortcut>⌘⌫</DropdownMenu.Shortcut>
		</DropdownMenu.Item>
	</DropdownMenu.Content>
</DropdownMenu.Root>
