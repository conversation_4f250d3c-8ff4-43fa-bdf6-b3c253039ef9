<script lang="ts">
	import { defaults, superForm } from 'sveltekit-superforms/client'
	import 'linq-extensions'
	import {
		addTeamMemberFormSchema,
		type AddTeamMemberFormSchemaType,
		type FormMessage,
		Genders,
		Roles
	} from '@repo/common'
	import * as Form from '$/components/ui/form'
	import * as Card from '$/components/ui/card'
	import * as Select from '$/components/ui/select'
	import { Button } from '$/components/ui/button'
	import { Input } from '$/components/ui/input'
	import { getAuthStore, getTeamStore, getUtilsStore } from '$/stores'
	import { FormAlert } from '$/components/common'
	import { valibotClient } from 'sveltekit-superforms/adapters'
	import { getFlashModule } from '$/lib/helpers'
	import { Loader2 } from '@lucide/svelte'
	import { onMount } from 'svelte'
	import type { MemberInviteInsertDbType, UserDbType } from '@repo/drizzle'
	import { membersInviteSignal, teamLocationsSignal, teamSignal } from '$/signals'

	let { data = {} }: { data?: Partial<UserDbType> } = $props()
	const authStore = getAuthStore()
	const teamLocationsData = $derived(teamLocationsSignal.getTeamLocationsData().fetch())
	const form = superForm<AddTeamMemberFormSchemaType, FormMessage>(
		defaults(data as any, valibotClient(addTeamMemberFormSchema)),
		{
			SPA: true,
			resetForm: true,
			clearOnSubmit: 'errors-and-message',
			validators: valibotClient(addTeamMemberFormSchema),
			async onUpdate({ form }) {
				try {
					const { data: formData } = form

					const memberInvites = membersInviteSignal.getMemberInviteData().fetch()
					const teamLocation = teamLocationsSignal.getTeamLocationById(formData.location)
					const teamId = teamSignal.findOne({})?.id

					const params: Partial<MemberInviteInsertDbType> = {
						givenName: formData.givenName,
						familyName: formData.familyName,
						gender: formData.gender,
						inviteeEmail: formData.email,
						invitedBy: authStore?.user?.id,
						role: formData.role as any,
						teamId: teamId,
						invitedByEmail: authStore?.user?.email,
						teamLocationId: teamLocation?.id,
						isPending: true
					}
					memberInvites?.push(params as any)
					//TODO: generate invite URL
					//TODO : Call encore endpoint and send invite
					form.message = {
						text: 'Team member invite sent successfully!',
						status: 'success'
					}
				} catch (error) {
					console.error(error)
					form.message = {
						text: 'Failed to send team member invite!',
						status: 'error'
					}
				}
			},
			...getFlashModule()
		}
	)

	const { form: formData, enhance, submitting, allErrors, message, validateForm } = form

	let utilsStore = getUtilsStore()
	const capitalizeWords = (word: string): string => {
		return word
			.split('-')
			.map((w) => w.charAt(0).toUpperCase() + w.slice(1))
			.join(' ')
	}

	onMount(() => {
		if (data?.id) validateForm?.({ update: true })
	})
</script>

<form method="POST" use:enhance>
	<FormAlert {message} />
	<Card.Root class="rounded-none">
		<Card.Content class=" mb-4 grid grid-cols-1 gap-3  md:grid-cols-3">
			<Form.Field {form} name="givenName">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>First Name</Form.Label>
						<Input {...props} placeholder="First Name" bind:value={$formData.givenName} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<Form.Field {form} name="familyName">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>First Name</Form.Label>
						<Input {...props} placeholder="First Name" bind:value={$formData.familyName} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<Form.Field {form} name="email">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Email</Form.Label>
						<Input {...props} placeholder="Email" bind:value={$formData.email} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<Form.Field {form} name="phoneNumber">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Phone No.</Form.Label>
						<Input {...props} placeholder="Phone Number" bind:value={$formData.phoneNumber} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<Form.Field {form} name="idDocumentNumber">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>ID Number</Form.Label>
						<Input
							{...props}
							placeholder="ID Number"
							bind:value={$formData.idDocumentNumber}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<Form.Field {form} name="gender">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Gender</Form.Label>
						<Select.Root type="single" bind:value={$formData.gender} name={props.name}>
							<Select.Trigger {...props}>
								{$formData.gender ? $formData.gender : 'Select a Gender'}
							</Select.Trigger>
							<Select.Content>
								<Select.Item value={Genders.Male} label={Genders.Male} />
								<Select.Item value={Genders.Female} label={Genders.Female} />
								<Select.Item value={Genders.Other} label={Genders.Other} />
							</Select.Content>
						</Select.Root>
						<input name={props.name} value={$formData.gender} hidden />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<Form.Field {form} name="role">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Role</Form.Label>
						<Select.Root type="single" bind:value={$formData.role} name={props.name}>
							<Select.Trigger {...props}>
								{$formData.role ? $formData.role : 'Select a Role'}
							</Select.Trigger>
							<Select.Content>
								<Select.Item
									value={Roles.TeamMember}
									label={capitalizeWords(Roles.TeamMember)}
								/>
								<Select.Item
									value={Roles.TeamLocationAdmin}
									label={capitalizeWords(Roles.TeamLocationAdmin)}
								/>
								<Select.Item
									value={Roles.TeamAdmin}
									label={capitalizeWords(Roles.TeamAdmin)}
								/>
							</Select.Content>
						</Select.Root>
						<input name={props.name} value={$formData.gender} hidden />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<Form.Field {form} name="location">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Assigned Location</Form.Label>
						<Select.Root type="single" bind:value={$formData.location} name={props.name}>
							<Select.Trigger>
								{$formData.location ? $formData.location : 'Select a Location'}
							</Select.Trigger>
							<Select.Content>
								{#each teamLocationsData as teamLocation (teamLocation.id)}
									<Select.Item
										value={teamLocation.id!}
										label={teamLocation.locationName}
									/>
								{/each}
							</Select.Content>
						</Select.Root>
						<input name={props.name} value={$formData.gender} hidden />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
		</Card.Content>
		<Card.Footer class="flex justify-between">
			<Button
				disabled={$submitting}
				variant="outline"
				class="mt-4 w-1/3"
				onclick={() => utilsStore.clearDynamicModal()}
				>Cancel
			</Button>
			<Form.Button disabled={$allErrors.length > 0 || $submitting} class="w-4/10 mt-4">
				{#if $submitting}
					<Loader2 class="mr-2 size-4 animate-spin" />
				{/if}
				{$submitting ? 'Please wait...' : 'Invite Team Member'}
			</Form.Button>
		</Card.Footer>
	</Card.Root>
</form>
