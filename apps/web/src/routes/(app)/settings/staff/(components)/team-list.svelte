<script lang="ts">
	import { NoData } from '$/components/common'
	import { Button } from '$/components/ui/button'
	import type { AddTeamMemberFormSchemaType, IDynamicModal } from '@repo/common'
	import { FileText } from '@lucide/svelte'
	import type { SuperValidated } from 'sveltekit-superforms/client'
	import { default as TeamListTable } from './member/team-list-table.svelte'
	import { getUtilsStore } from '$/stores'
	import { membersSignal, teamLocationsSignal, userRoleSignal, usersSignal } from '$/signals'
	import type { MemberDbType } from '@repo/drizzle'

	let { addTeamMemberForm }: { addTeamMemberForm: SuperValidated<AddTeamMemberFormSchemaType> } =
		$props()
	const uStore = getUtilsStore()
	const teamMembersData = membersSignal.getMemberData()
	const teamMembers: MemberDbType[] = $derived(teamMembersData.fetch())
	let hasTeamMembers = $derived(teamMembersData?.count() ?? 0 > 0)
	let searchTerm = $state('')
	let filteredMembers = $derived(
		teamMembers
			?.filter((m) => {
				return (
					usersSignal
						.find({
							$or: [
								{ givenName: { $like: searchTerm } },
								{ familyName: { $like: searchTerm } },
								{ email: { $like: searchTerm } },
								{ gender: { $like: searchTerm } }
							]
						})
						.count() > 0 ||
					teamLocationsSignal.findOne({ locationName: { $like: searchTerm } }) !== undefined
				)
			})
			.map((k) => {
				const profile = usersSignal.getUserById(k.userId)
				const location = teamLocationsSignal.findOne({ id: k.teamLocationId })
				const role = userRoleSignal.findOne({ userId: k.userId, id: profile?.defaultRole })
				return {
					profile,
					location,
					role,
					memberStatus: k.memberStatus,
					joinedAt: k.createdAt
				}
			})
	)
	const startCreateTeamMember = () => {
		const newTeamMemberForm: IDynamicModal = {
			canClose: true,
			title: 'Invite new Team Member',
			componentName: 'AddTeamMember',
			size: 'lg',
			color: 'default',
			description: 'Send Team member invite to your team',
			componentProps: {
				addTeamMemberForm
			}
		}
		uStore.openDynamicModal(newTeamMemberForm)
	}
</script>

{#if hasTeamMembers}
	<div class="space-y-4">
		<TeamListTable data={filteredMembers as any} />
	</div>
{:else}
	{#snippet iconSnippet()}
		<FileText class="h-10 w-10" />
	{/snippet}
	<NoData
		noDataTitle="No Employee Data Found"
		noDataDescription="You do not have any employee data captured yet. start creating employee Data"
		snippet={iconSnippet}
	>
		<div class="flex justify-between space-x-2">
			<Button variant="default" onclick={startCreateTeamMember}>New Invite</Button>
		</div>
	</NoData>
{/if}
