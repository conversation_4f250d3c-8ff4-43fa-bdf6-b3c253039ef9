import { Roles } from '@repo/common'
import {
	BluetoothConnected,
	PlugZap,
	ScreenShareOff,
	Unplug,
	UserCheck,
	UserPlus,
	UserRoundPlus,
	Users
} from '@lucide/svelte'
import { default as StaffTableRowActions } from './staff-table-row-actions.svelte'
import type { ColumnDef } from '@tanstack/table-core'

import { renderComponent } from '$/components/ui/data-table'
import {
	DataTableCell,
	DataTableCheckbox,
	DataTableColumnHeader,
	DataTableStatusCell
} from '$/components/common/data-table'
import { getKeyByValue, TeamMemberStatuses } from '@repo/common'
import type { MemberDbType, TeamLocationDbType, UserDbType } from '@repo/drizzle'

export const columns: ColumnDef<MemberDbType & UserDbType & TeamLocationDbType>[] = [
	{
		id: 'select',
		header: ({ table }) =>
			renderComponent(DataTableCheckbox, {
				checked: table.getIsAllPageRowsSelected(),
				onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value),
				'aria-label': 'Select all',
				class: 'translate-y-[2px]'
			}),
		cell: ({ row }) =>
			renderComponent(DataTableCheckbox, {
				checked: row.getIsSelected(),
				onCheckedChange: (value) => row.toggleSelected(!!value),
				'aria-label': 'Select row',
				class: 'translate-y-[2px]'
			}),
		enableSorting: false,
		enableHiding: false
	},
	{
		accessorKey: 'fullName',
		header: ({ column }) =>
			renderComponent(DataTableColumnHeader, {
				column,
				title: 'Full Name'
			}),
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: `${row.original.givenName} ${row.original.familyName}`
			})
		},
		enableSorting: true,
		enableColumnFilter: true
	},
	{
		accessorKey: 'userStatus',
		header: ({ column }) =>
			renderComponent(DataTableColumnHeader, {
				column,
				title: 'User Status'
			}),
		cell: ({ row }) => {
			return renderComponent(DataTableStatusCell, {
				value: row.original.memberStatus,
				statuses: statuses
			})
		},
		enableSorting: true,
		filterFn: (row, id, value) => {
			return value.includes(row.getValue(id))
		}
	},
	{
		accessorKey: 'phoneNumber',
		header: ({ column }) =>
			renderComponent(DataTableColumnHeader, {
				column,
				title: 'Phone'
			}),
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: row.original?.phoneNumber || ''
			})
		},
		enableSorting: true,
		enableColumnFilter: true
	},
	{
		accessorKey: 'email',
		header: ({ column }) =>
			renderComponent(DataTableColumnHeader, {
				column,
				title: 'Email'
			}),
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: row.original.email || ''
			})
		},
		enableSorting: true,
		enableColumnFilter: true
	},
	{
		accessorKey: 'gender',
		header: ({ column }) =>
			renderComponent(DataTableColumnHeader, {
				column,
				title: 'Gender'
			}),
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: row.original.gender?.toString()
			})
		},
		enableColumnFilter: false,
		enableSorting: true
	},
	{
		accessorKey: 'location',
		header: ({ column }) => {
			return renderComponent(DataTableColumnHeader, {
				title: 'Branch Location',
				column
			})
		},
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: row.original.locationName
			})
		},
		enableColumnFilter: true,
		enableSorting: true,
		enableGrouping: true
	},
	{
		accessorKey: 'role',
		header: ({ column }) => {
			return renderComponent(DataTableColumnHeader, {
				title: 'Role',
				column
			})
		},
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: getKeyByValue(Roles, row.original.defaultRole)
			})
		},
		enableColumnFilter: true,
		enableSorting: true,
		enableGrouping: true
	},
	{
		id: 'actions',
		cell: ({ row }) => renderComponent(StaffTableRowActions, { row: row as any })
	}
]

export const roles = [
	{
		label: 'Admin',
		value: Roles.Admin,
		icon: UserCheck
	},
	{
		label: 'Team Admin',
		value: Roles.TeamAdmin,
		icon: UserRoundPlus
	},
	{
		label: 'Location Admin',
		value: Roles.TeamLocationAdmin,
		icon: UserPlus
	},
	{
		label: 'Team Member',
		value: Roles.TeamMember,
		icon: Users
	}
]
export const statuses = [
	{
		value: TeamMemberStatuses.Active,
		label: 'Active',
		icon: BluetoothConnected
	},
	{
		value: TeamMemberStatuses.InActive,
		label: 'Inactive',
		icon: ScreenShareOff
	},
	{
		value: TeamMemberStatuses.OnLeave,
		label: 'On Leave',
		icon: Unplug
	},
	{
		value: TeamMemberStatuses.Resigned,
		label: 'Resigned',
		icon: PlugZap
	}
]
