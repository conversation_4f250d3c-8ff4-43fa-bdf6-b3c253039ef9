<script lang="ts">
	import { AppShell, AppShellHeader } from '$/components/layout'

	import * as Card from '$/components/ui/card'
	import { defaults } from 'sveltekit-superforms/client'
	import { valibotClient } from 'sveltekit-superforms/adapters'
	import { addTeamMemberFormSchema } from '@repo/common'
	import { TeamList } from './(components)'

	const heading = $state('Team Manager')
	const subHeading = $state(
		'Manage all Team members/Employee Data. Change State, Add new , Edit and Disable Team members'
	)

	const addTeamMemberForm = defaults({} as any, valibotClient(addTeamMemberFormSchema))
</script>

<AppShell>
	<AppShellHeader {heading} {subHeading} />
	<div class="grid gap-10">
		<Card.Root class="rounded-none">
			<Card.Header class="px-7">
				<Card.Title>Team Members</Card.Title>
				<Card.Description>Setup and Manage Team Members</Card.Description>
			</Card.Header>
			<Card.Content>
				<TeamList {addTeamMemberForm} />
			</Card.Content>
		</Card.Root>
	</div>
</AppShell>
