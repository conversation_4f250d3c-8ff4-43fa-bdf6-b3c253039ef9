<script lang="ts">
	import { Button } from '$/components/ui/button'
	import { defaults, superForm } from 'sveltekit-superforms/client'
	import {
		paymentProviderFormSchema,
		type PaymentProviderFormSchemaType,
		type FormMessage
	} from '@repo/common'
	import * as Form from '$/components/ui/form'
	import * as Card from '$/components/ui/card'
	import { Input } from '$/components/ui/input'
	import { Switch } from '$/components/ui/switch'
	import { FormAlert } from '$/components/common'
	import { valibotClient } from 'sveltekit-superforms/adapters'
	import { getFlashModule } from '$/lib/helpers'
	import { Loader2, Eye, EyeOff } from '@lucide/svelte'
	import { onMount } from 'svelte'
	import { getTeamStore, getUtilsStore } from '$/stores'
	import { teamPaymentProviderSignal } from '$/signals'
	import type { TeamPaymentProviderInsertDbType } from '@repo/drizzle'

	let { data = {} }: { data?: Partial<PaymentProviderFormSchemaType> } = $props()
	const paymentProviderData = teamPaymentProviderSignal.getTeamPaymentProviderData()
	let paymentProviderState = $derived(paymentProviderData)
	let hasPaymentProvider = $derived(!!paymentProviderState)
	const form = superForm<PaymentProviderFormSchemaType, FormMessage>(
		defaults(paymentProviderData as any, valibotClient(paymentProviderFormSchema)),
		{
			SPA: true,
			resetForm: true,
			clearOnSubmit: 'errors-and-message',
			validators: valibotClient(paymentProviderFormSchema),
			async onUpdate({ form }) {
				if (!form.valid) return
				try {
					const { data: formData } = form

					const paymentProvider: Partial<TeamPaymentProviderInsertDbType> = {
						providerName: formData.providerName,
						providerApiKey: formData.providerApiKey,
						providerApiSecret: formData.providerApiSecret,
						providerApiUrl: formData.providerApiUrl,
						providerClientId: formData.providerClientId,
						providerWebhookUrl: formData.providerWebhookUrl
					}
					if (!hasPaymentProvider) {
						const providerId = teamPaymentProviderSignal.insert(paymentProvider)
					} else {
						teamPaymentProviderSignal.updateOne(
							{ id: paymentProviderState?.id },
							{ $set: paymentProvider }
						)
					}
					utilsStore.toastSuccess('Payment Provider SDK details updated successfully!')
					utilsStore.clearDynamicModal()
					form.message = {
						status: 'success',
						text: 'Payment provider added successfully'
					}
				} catch (err) {
					form.message = {
						status: 'error',
						text: `Error adding payment provider ${(err as Error).message}`
					}
				}
			},
			...getFlashModule()
		}
	)

	const { form: formData, enhance, submitting, allErrors, message, validateForm } = form

	// State for showing/hiding sensitive fields
	let showApiKey = $state(false)
	let showClientSecret = $state(false)
	let utilsStore = getUtilsStore()
	onMount(() => {
		if (data?.providerName) validateForm?.({ update: true })
	})

	const toggleVisibility = (field: 'apiKey' | 'clientSecret') => {
		if (field === 'apiKey') showApiKey = !showApiKey
		if (field === 'clientSecret') showClientSecret = !showClientSecret
	}
</script>

<form method="POST" use:enhance>
	<FormAlert {message} />
	<Card.Root class="mx-4 rounded-none">
		<Card.Header>
			<Card.Title>{data?.providerName ? 'Edit' : 'Add'} Payment Provider</Card.Title>
			<Card.Description>
				Configure payment provider details to enable payment processing
			</Card.Description>
		</Card.Header>
		<Card.Content class="mb-4 grid grid-cols-1 gap-3 md:grid-cols-2">
			<Form.Field {form} name="providerName">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Provider Name</Form.Label>
						<Input
							{...props}
							placeholder="e.g.,PayStack,Stitch,etc"
							bind:value={$formData.providerName}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="providerApiUrl">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>API URL</Form.Label>
						<Input
							{...props}
							placeholder="https://api.provider.com"
							bind:value={$formData.providerApiUrl}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="providerApiKey">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>API Key</Form.Label>
						<div class="relative">
							<Input
								{...props}
								type={showApiKey ? 'text' : 'password'}
								placeholder="Enter API Key"
								bind:value={$formData.providerApiKey}
							/>
							<button
								type="button"
								class="absolute right-3 top-1/2 -translate-y-1/2"
								onclick={() => toggleVisibility('apiKey')}
							>
								{#if showApiKey}
									<EyeOff class="size-4" />
								{:else}
									<Eye class="size-4" />
								{/if}
							</button>
						</div>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="providerClientId">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Client ID (Optional)</Form.Label>
						<Input
							{...props}
							placeholder="Enter Client ID"
							bind:value={$formData.providerClientId}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="providerApiSecret">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Client Secret (Optional)</Form.Label>
						<div class="relative">
							<Input
								{...props}
								type={showClientSecret ? 'text' : 'password'}
								placeholder="Enter Client Secret"
								bind:value={$formData.providerApiSecret}
							/>
							<button
								type="button"
								class="absolute right-3 top-1/2 -translate-y-1/2"
								onclick={() => toggleVisibility('clientSecret')}
							>
								{#if showClientSecret}
									<EyeOff class="size-4" />
								{:else}
									<Eye class="size-4" />
								{/if}
							</button>
						</div>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="providerWebhookUrl">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Webhook URL (Optional)</Form.Label>
						<Input
							{...props}
							placeholder="https://your-webhook-endpoint.com"
							bind:value={$formData.providerWebhookUrl}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="isActive">
				<Form.Control>
					{#snippet children({ props })}
						<div class="flex items-center space-x-2">
							<Switch
								{...props}
								checked={$formData.isActive ?? false}
								onCheckedChange={(checked) => ($formData.isActive = checked)}
							/>
							<Form.Label>Active Provider</Form.Label>
						</div>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
		</Card.Content>
		<Card.Footer class="flex justify-between">
			<Button
				disabled={$submitting}
				variant="outline"
				class="mt-4 w-1/3"
				onclick={() => utilsStore.clearDynamicModal()}
				>Cancel
			</Button>
			<Form.Button disabled={$allErrors.length > 0 || $submitting} class="mt-4 w-1/3">
				{#if $submitting}
					<Loader2 class="mr-2 size-4 animate-spin" />
				{/if}
				{$submitting
					? 'Please wait...'
					: data?.providerName
						? 'Update Provider'
						: 'Add Provider'}
			</Form.Button>
		</Card.Footer>
	</Card.Root>
</form>
