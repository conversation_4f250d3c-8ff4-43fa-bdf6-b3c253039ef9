<script lang="ts">
	import { Button } from '$/components/ui/button'
	import { defaults, superForm } from 'sveltekit-superforms/client'
	import {
		bankAccountFormSchema,
		type BankAccountFormSchemaType,
		type FormMessage
	} from '@repo/common'
	import * as Form from '$/components/ui/form'
	import * as Card from '$/components/ui/card'
	import { Input } from '$/components/ui/input'
	import { FormAlert } from '$/components/common'
	import { valibotClient } from 'sveltekit-superforms/adapters'
	import { getFlashModule } from '$/lib/helpers'
	import { Loader2 } from '@lucide/svelte'
	import { onMount } from 'svelte'
	import { getUtilsStore } from '$/stores'
	import { teamBankDetailSignal } from '$/signals'
	import type { TeamBankDetailInsertDbType } from '@repo/drizzle'

	let { data = {} }: { data?: Partial<BankAccountFormSchemaType> } = $props()
	let utilsStore = getUtilsStore()
	const teamBankDetail = teamBankDetailSignal.getTeamBankDetailData()
	let teamBankDetailState = $derived(teamBankDetail)
	const hasDetail = $derived(!!teamBankDetail)
	const form = superForm<BankAccountFormSchemaType, FormMessage>(
		defaults(teamBankDetail as any, valibotClient(bankAccountFormSchema)),
		{
			SPA: true,
			resetForm: true,
			clearOnSubmit: 'errors-and-message',
			validators: valibotClient(bankAccountFormSchema),
			async onUpdate({ form }) {
				if (!form.valid) return
				try {
					const { data: formData } = form
					const bankDetail: Partial<TeamBankDetailInsertDbType> = {
						accountName: formData.accountName,
						accountNumber: formData.accountNumber,
						bankName: formData.bankName,
						branchCode: formData.branchCode,
						swiftCode: formData.swiftCode
					}
					if (!hasDetail) {
						const teamBankDetailId = teamBankDetailSignal.insert(bankDetail)
					} else {
						teamBankDetailSignal.updateOne({ id: teamBankDetail?.id }, { $set: bankDetail })
					}
					utilsStore.toastSuccess('Bank details added successfully!')
					utilsStore.clearDynamicModal()
					form.message = { status: 'success', text: 'Bank details added successfully' }
				} catch (err) {
					form.message = {
						status: 'error',
						text: `Error adding bank details ${(err as Error).message}`
					}
				}
			},
			...getFlashModule()
		}
	)

	const { form: formData, enhance, submitting, allErrors, message, validateForm } = form

	onMount(() => {
		if (data?.accountNumber) validateForm?.({ update: true })
	})
</script>

<form method="POST" use:enhance>
	<FormAlert {message} />
	<Card.Root class="mx-4 rounded-none">
		<Card.Header>
			<Card.Title>{teamBankDetailState?.accountNumber ? 'Edit' : 'Add'} Bank Account</Card.Title>
			<Card.Description>
				Please provide your bank account details for receiving payments
			</Card.Description>
		</Card.Header>
		<Card.Content class="mb-4 grid grid-cols-1 gap-3 md:grid-cols-2">
			<Form.Field {form} name="accountName">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Account Name</Form.Label>
						<Input {...props} placeholder="Account Name" bind:value={$formData.accountName} />
						<Form.Description>Nickname for the account</Form.Description>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="accountNumber">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Account Number</Form.Label>
						<Input
							{...props}
							placeholder="Account Number"
							bind:value={$formData.accountNumber}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="bankName">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Bank Name</Form.Label>
						<Input {...props} placeholder="Bank Name" bind:value={$formData.bankName} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="branchCode">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Branch Code</Form.Label>
						<Input {...props} placeholder="Branch Code" bind:value={$formData.branchCode} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="swiftCode">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>SWIFT Code</Form.Label>
						<Input {...props} placeholder="SWIFT Code" bind:value={$formData.swiftCode} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
		</Card.Content>
		<Card.Footer class="flex justify-between">
			<Button
				disabled={$submitting}
				variant="outline"
				class="mt-4 w-1/3"
				onclick={() => utilsStore.clearDynamicModal()}
				>Cancel
			</Button>
			<Form.Button
				disabled={$allErrors.length > 0 || $submitting}
				type="submit"
				class="px-auto mt-4 w-4/10"
			>
				{#if $submitting}
					<Loader2 class="mr-2 size-4 animate-spin" />
				{/if}
				{$submitting
					? 'Please wait...'
					: teamBankDetailState?.accountNumber
						? 'Update Bank Account'
						: 'Add Bank Account'}
			</Form.Button>
		</Card.Footer>
	</Card.Root>
</form>
