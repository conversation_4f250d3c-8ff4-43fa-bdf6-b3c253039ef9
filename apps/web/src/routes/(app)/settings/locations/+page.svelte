<script lang="ts">
	import { NoData } from '$/components/common'
	import { CardShell } from '$/components/layout'
	import { Button } from '$/components/ui/button'
	import * as Card from '$/components/ui/card'
	import { teamLocationsSignal } from '$/signals'
	import { getUtilsStore } from '$/stores'
	import type { IDynamicModal } from '@repo/common'
	import { List } from '@lucide/svelte'
	import LocationsTable from './(components)/locations-table.svelte'

	const uStore = getUtilsStore()
	let hasLocations = $derived(teamLocationsSignal.find({}).count() > 0)
	const heading = $state('Locations Manager')
	const subHeading = $state(
		'Manage team (business) locations. Change State, Add new , Edit and Disable Locations'
	)

	const startCreateTeamLocation = () => {
		const newTeamMemberForm: IDynamicModal = {
			canClose: false,
			title: 'New team Location',
			componentName: 'AddTeamLocation',
			size: 'lg',
			color: 'default',
			description: 'Create new Team Location',
			componentProps: {}
		}
		uStore.openDynamicModal(newTeamMemberForm)
	}
</script>

<CardShell {heading} {subHeading}>
	<Card.Root class="rounded-none">
		<Card.Content>
			{#if hasLocations}
				<LocationsTable />
			{:else}
				{#snippet listSnippet()}
					<List class="h-10 w-10" />
				{/snippet}
				<NoData
					noDataTitle="No Pending Invites"
					noDataDescription="You do not have any pending invites. start creating employee Data"
					snippet={listSnippet}
				>
					<div class="flex justify-between space-x-2">
						<Button variant="default" onclick={startCreateTeamLocation}
							>Create Location</Button
						>
					</div>
				</NoData>
			{/if}
		</Card.Content>
	</Card.Root>
</CardShell>
