<script lang="ts">
	import { DataTablePagination, DataTableToolbar } from '$/components/common/data-table'
	import { Button } from '$/components/ui/button'

	import { createSvelteTable, FlexRender } from '$/components/ui/data-table'
	import * as Table from '$/components/ui/table'

	import {
		getCoreRowModel,
		getFacetedRowModel,
		getFacetedUniqueValues,
		getFilteredRowModel,
		getPaginationRowModel,
		getSortedRowModel,
		type ColumnFiltersState,
		type PaginationState,
		type RowSelectionState,
		type SortingState,
		type VisibilityState
	} from '@tanstack/table-core'
	import { X } from '@lucide/svelte'
	import { columns } from './locations-list-data'
	import { membersSignal, teamLocationsSignal, usersSignal } from '$/signals'
	import type { TeamLocationInsertDbType } from '@repo/drizzle'

	let rowSelection = $state<RowSelectionState>({})
	let columnVisibility = $state<VisibilityState>({})
	let columnFilters = $state<ColumnFiltersState>([])
	let sorting = $state<SortingState>([])
	let pagination = $state<PaginationState>({ pageIndex: 0, pageSize: 10 })
	const teamLocations = $derived(teamLocationsSignal.getTeamLocationsData().fetch())
	const table = createSvelteTable({
		get data() {
			return teamLocations?.map((tl) => {
				const locationAdmin = usersSignal.getUserById(tl.teamLocationAdmin)
				return {
					...tl,
					locationAdminName: `${locationAdmin?.givenName} ${locationAdmin?.familyName}`
				}
			}) as (TeamLocationInsertDbType & { locationAdminName: string })[]
		},
		state: {
			get sorting() {
				return sorting
			},
			get columnVisibility() {
				return columnVisibility
			},
			get rowSelection() {
				return rowSelection
			},
			get columnFilters() {
				return columnFilters
			},
			get pagination() {
				return pagination
			}
		},
		columns,
		enableRowSelection: true,
		onRowSelectionChange: (updater) => {
			if (typeof updater === 'function') {
				rowSelection = updater(rowSelection)
			} else {
				rowSelection = updater
			}
		},
		onSortingChange: (updater) => {
			if (typeof updater === 'function') {
				sorting = updater(sorting)
			} else {
				sorting = updater
			}
		},
		onColumnFiltersChange: (updater) => {
			if (typeof updater === 'function') {
				columnFilters = updater(columnFilters)
			} else {
				columnFilters = updater
			}
		},
		onColumnVisibilityChange: (updater) => {
			if (typeof updater === 'function') {
				columnVisibility = updater(columnVisibility)
			} else {
				columnVisibility = updater
			}
		},
		onPaginationChange: (updater) => {
			if (typeof updater === 'function') {
				pagination = updater(pagination)
			} else {
				pagination = updater
			}
		},
		getCoreRowModel: getCoreRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFacetedRowModel: getFacetedRowModel(),
		getFacetedUniqueValues: getFacetedUniqueValues()
	})

	const isFiltered = $derived(table.getState().columnFilters.length > 0)
</script>

<div class="space-y-4">
	<DataTableToolbar {table}>
		{#if isFiltered}
			<Button
				variant="ghost"
				onclick={() => table.resetColumnFilters()}
				class="h-8 px-2 lg:px-3"
			>
				Reset
				<X />
			</Button>
		{/if}
	</DataTableToolbar>
	<div class="rounded-md border">
		<Table.Root>
			<Table.Header>
				{#each table.getHeaderGroups() as headerGroup (headerGroup.id)}
					<Table.Row>
						{#each headerGroup.headers as header (header.id)}
							<Table.Head colspan={header.colSpan}>
								{#if !header.isPlaceholder}
									<FlexRender
										content={header.column.columnDef.header}
										context={header.getContext()}
									/>
								{/if}
							</Table.Head>
						{/each}
					</Table.Row>
				{/each}
			</Table.Header>
			<Table.Body>
				{#each table.getRowModel().rows as row (row.id)}
					<Table.Row data-state={row.getIsSelected() && 'selected'}>
						{#each row.getVisibleCells() as cell (cell.id)}
							<Table.Cell>
								<FlexRender
									content={cell.column.columnDef.cell}
									context={cell.getContext()}
								/>
							</Table.Cell>
						{/each}
					</Table.Row>
				{:else}
					<Table.Row>
						<Table.Cell colspan={columns.length} class="h-24 text-center">
							No results.
						</Table.Cell>
					</Table.Row>
				{/each}
			</Table.Body>
		</Table.Root>
	</div>
	<DataTablePagination {table} />
</div>
