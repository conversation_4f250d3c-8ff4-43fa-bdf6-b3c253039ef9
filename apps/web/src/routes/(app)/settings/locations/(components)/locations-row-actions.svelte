<script lang="ts">
	import type { IDynamicModal } from '@repo/common'
	import Ellipsis from '@lucide/svelte/icons/ellipsis'
	import type { Row } from '@tanstack/table-core'
	import * as DropdownMenu from '$/components/ui/dropdown-menu'
	import { Button } from '$/components/ui/button'
	import { getUtilsStore } from '$/stores'
	import type { TeamLocationDbType } from '@repo/drizzle'
	import { teamLocationsSignal } from '$/signals'

	let { row }: { row: Row<TeamLocationDbType> } = $props()
	const uStore = getUtilsStore()
	let toggeStatusText = $state<string>(
		row.original?.isActive === true ? 'Deactivate Location' : 'Activate Location'
	)
	const toggleLocationStatus = async () => {
		const status = !row.original?.isActive
		await updateTeamLocationStatus(status)
	}
	const editTeamLocation = () => {
		const newTeamLocationForm: IDynamicModal = {
			canClose: false,
			title: 'Edit Team Location',
			componentName: 'AddTeamLocation',
			size: 'lg',
			color: 'default',
			description: 'Edit Team Location details',
			componentProps: {
				data: row.original
			}
		}
		uStore.openDynamicModal(newTeamLocationForm)
	}
	const setInactiveLocation = async () => {
		await updateTeamLocationStatus(false)
	}
	const updateTeamLocationStatus = async (isActive: boolean) => {
		try {
			await teamLocationsSignal.updateOne({ id: row.original.id }, { $set: { isActive } })
			return uStore.toastSuccess('Team location status updated successfully')
		} catch (err) {
			uStore.toastError(`Failed to update status: ${(err as Error).message}`)
		}
	}
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger>
		{#snippet child({ props })}
			<Button {...props} variant="ghost" class="data-[state=open]:bg-muted flex h-8 w-8 p-0">
				<Ellipsis />
				<span class="sr-only">Open Menu</span>
			</Button>
		{/snippet}
	</DropdownMenu.Trigger>
	<DropdownMenu.Content class="w-[160px]" align="end">
		<DropdownMenu.Item onclick={editTeamLocation}>Edit</DropdownMenu.Item>
		<DropdownMenu.Item onclick={setInactiveLocation}>Set InActive</DropdownMenu.Item>
		<DropdownMenu.Separator />
		<DropdownMenu.Item onclick={() => toggleLocationStatus()}>
			{toggeStatusText}
			<DropdownMenu.Shortcut>⌘⌫</DropdownMenu.Shortcut>
		</DropdownMenu.Item>
	</DropdownMenu.Content>
</DropdownMenu.Root>
