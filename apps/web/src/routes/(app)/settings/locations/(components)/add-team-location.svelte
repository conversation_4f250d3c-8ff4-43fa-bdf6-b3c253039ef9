<script lang="ts">
	import { defaults, superForm } from 'sveltekit-superforms/client'
	import { type FormMessage, locationFormSchema, type LocationFormSchemaType } from '@repo/common'
	import * as Form from '$/components/ui/form'
	import * as Card from '$/components/ui/card'
	import * as Select from '$/components/ui/select'
	import { Textarea } from '$/components/ui/textarea'
	import { Input } from '$/components/ui/input'
	import { FormAlert } from '$/components/common'
	import { valibotClient } from 'sveltekit-superforms/adapters'
	import { getFlashModule } from '$/lib/helpers'
	import { Loader2, User } from '@lucide/svelte'
	import { onMount } from 'svelte'
	import { NoData } from '$/components/common'
	import type { TeamLocationDbType, UserDbType, MemberDbType } from '@repo/drizzle'
	import { teamLocationsSignal, membersSignal, teamSignal } from '$/signals'

	let { data }: { data?: TeamLocationDbType } = $props()
	let teamMembers = $derived(teamSignal.getTeamMembers())
	const form = superForm<LocationFormSchemaType, FormMessage>(
		defaults(data as any, valibotClient(locationFormSchema)),
		{
			SPA: true,
			resetForm: true,
			clearOnSubmit: 'errors-and-message',
			validators: valibotClient(locationFormSchema),
			async onUpdate({ form }) {
				if (!form.valid) return

				try {
					const { data: formData } = form

					const teamLocationAdmin = teamLocationsSignal.findOne({ id: formData.locationAdmin })

					const teamLoc: Partial<TeamLocationDbType> = {
						isActive: true,
						isDefault: false,
						locationName: formData.locationName,
						locationAddress: formData.locationAddress,
						locationEmail: formData.locationEmail,
						locationPhoneNumber: formData.locationPhoneNumber!,
						teamLocationAdmin: formData.locationAdmin
					}
					const teamLocationId = teamLocationsSignal.insert(teamLoc as any)
					form.message = { status: 'success', text: 'Team Location created successfully' }
				} catch (err) {
					form.message = {
						status: 'error',
						text: `Error Saving location ${(err as Error).message}`
					}
				}
			},
			...getFlashModule()
		}
	)

	const { form: formData, enhance, submitting, allErrors, message, validateForm } = form
	let noDataString = $state(
		'Other than the team Owner, there are no other members that could be assigned as location admin'
	)

	const hasTeamMembers = $derived(membersSignal.find({}).count() > 0)

	onMount(() => {
		if (data?.id) {
			validateForm?.({ update: true })
		}
	})

	const getFullName = (memberData: MemberDbType & UserDbType) => {
		return `${memberData.givenName} ${memberData.familyName}`
	}
</script>

{#if hasTeamMembers}``
	<form method="POST" use:enhance>
		<FormAlert {message} />
		<Card.Root class="rounded-none">
			<Card.Header>
				<Card.Title>Add Employee</Card.Title>
				<Card.Description>Please provide location details</Card.Description>
			</Card.Header>
			<Card.Content class="mx-4 mb-4 grid grid-cols-1 gap-3  md:grid-cols-3">
				<Form.Field {form} name="locationName">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>First Name</Form.Label>
							<Input
								{...props}
								placeholder="Location Name"
								bind:value={$formData.locationName}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="locationAddress">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Location Address</Form.Label>
							<Textarea
								{...props}
								placeholder="Address of Location"
								class="resize-none"
								bind:value={$formData.locationAddress}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="locationEmail">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Email</Form.Label>
							<Input {...props} placeholder="Email" bind:value={$formData.locationEmail} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="locationPhoneNumber">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Phone Number</Form.Label>
							<Input
								{...props}
								placeholder="Phone Number"
								bind:value={$formData.locationPhoneNumber}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="locationAdmin">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Gender</Form.Label>
							<Select.Root
								type="single"
								bind:value={$formData.locationAdmin}
								name={props.name}
							>
								<Select.Trigger {...props}>
									{$formData.locationAdmin ? $formData.locationAdmin : 'Pick an Admin'}
								</Select.Trigger>
								<Select.Content>
									{#each teamMembers as member (member.id)}
										<Select.Item
											value={member.id}
											label={getFullName(member as any as MemberDbType & UserDbType)}
										/>
									{/each}
								</Select.Content>
							</Select.Root>
							<input name={props.name} value={$formData.locationAdmin} hidden />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			</Card.Content>
			<Card.Footer>
				<Form.Button
					disabled={$allErrors.length > 0 || $submitting}
					class="ml-auto mr-0 mt-4 w-1/2"
				>
					{#if $submitting}
						<Loader2 class="mr-2 size-4 animate-spin" />
					{/if}
					{$submitting ? 'Please wait...' : 'Invite Team Member'}
				</Form.Button>
			</Card.Footer>
		</Card.Root>
	</form>
{:else}
	{#snippet iconSnippet()}
		<User class="h-10 w-10" />
	{/snippet}
	<NoData
		title="No Team Members"
		description="You need to add team members before you can create a location"
		snippet={iconSnippet}
	>
		<div class="flex justify-between space-x-2">
			<p>{noDataString}</p>
		</div>
	</NoData>
{/if}
