<script lang="ts">
	import { CardShell } from '$/components/layout'
	import { default as AppointmentTypeList } from './(components)/appointment-type-list.svelte'
	import { defaults } from 'sveltekit-superforms'
	import { valibotClient } from 'sveltekit-superforms/adapters'
	import { createAppointmentTypeFormSchema, deleteAppointmentTypeFormSchema } from '@repo/common'

	const createAppointmentTypeForm = defaults(
		{} as any,
		valibotClient(createAppointmentTypeFormSchema)
	)
	const deleteAppointmentTypeForm = defaults(
		{} as any,
		valibotClient(deleteAppointmentTypeFormSchema)
	)
</script>

<CardShell
	heading="Appointment Types"
	subHeading="Create and manage appointment types across all locations"
>
	<AppointmentTypeList {createAppointmentTypeForm} {deleteAppointmentTypeForm} />
</CardShell>
