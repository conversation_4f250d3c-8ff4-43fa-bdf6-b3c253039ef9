<script lang="ts">
	import * as Form from '$/components/ui/form'
	import * as Dialog from '$/components/ui/dialog'
	import { Button } from '$/components/ui/button'
	import { Input } from '$/components/ui/input'
	import { Label } from '$/components/ui/label'
	import { Switch } from '$/components/ui/switch'
	import * as Select from '$/components/ui/select'
	import { valibotClient } from 'sveltekit-superforms/adapters'
	import {
		appointmentRepeatTypes,
		createAppointmentTypeFormSchema,
		type AppointmentRepeatType,
		type CreateAppointmentTypeFormSchemaType
	} from '@repo/common'
	import { superForm, type SuperValidated } from 'sveltekit-superforms'
	import { ComboInput } from '$/components/common'
	import FormAlerts from '$/components/common/form-alerts.svelte'
	import { TIMEZONES } from '@repo/common'
	import type { AppointmentTypeInsertDbType } from '@repo/drizzle'
	import { appointmentTypesSignal } from '$/signals'

	let {
		createAppointmentTypeForm,
		showDialog
	}: {
		createAppointmentTypeForm: SuperValidated<CreateAppointmentTypeFormSchemaType>
		showDialog: boolean
	} = $props()
	const form = superForm(createAppointmentTypeForm, {
		validators: valibotClient(createAppointmentTypeFormSchema),
		multipleSubmits: 'prevent',
		SPA: true,
		clearOnSubmit: 'errors-and-message',
		async onUpdate({ form }) {
			try {
				if (!form.valid) return
				const timeZone = TIMEZONES.find((tz) => tz.id === form.data.timeZoneId)
				const appType: Partial<AppointmentTypeInsertDbType> = {
					appointmentDetail: form.data.name,
					durationInMinutes: form.data.durationInMinutes,
					isActive: form.data.isActive,
					isRepeatable: form.data.isRepeatEnabled,
					repeatType: form.data.repeatInterval as AppointmentRepeatType,
					requiresUpfrontPayment: form.data.requiresUpfrontPayment,
					upfrontPaymentAmount: form.data.requiresUpfrontPayment
						? form.data.upfrontPaymentAmount?.toString()
						: '0',
					timezoneId: timeZone?.id
				}

				appointmentTypesSignal.insert(appType as any)
				form.message = {
					status: 'success',
					message: 'Appointment type created successfully'
				}
			} catch (error) {
				console.error(error)
				form.message = {
					status: 'error',
					message: 'Failed to create appointment type'
				}
			}
		}
	})
	const { form: formData, enhance, message, allErrors, submitting } = form
</script>

<Dialog.Root bind:open={showDialog}>
	<Dialog.Content class="sm:max-w-[425px]" showCloseButton={false}>
		<Dialog.Header>
			<Dialog.Title>Create Appointment Type</Dialog.Title>
			<Dialog.Description>
				Create a new event type for bookings. Click save when you're done.
			</Dialog.Description>
		</Dialog.Header>

		<form method="POST" use:enhance class="grid gap-4 py-4">
			<FormAlerts {message} />
			<Form.Field {form} name="name">
				<Form.Control>
					{#snippet children({ props })}
						<Label for="name">Name</Label>
						<Input {...props} bind:value={$formData.name} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="durationInMinutes">
				<Form.Control>
					{#snippet children({ props })}
						<Label for="duration">Duration (minutes)</Label>
						<Input type="number" {...props} bind:value={$formData.durationInMinutes} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="isActive">
				<Form.Control>
					{#snippet children({ props })}
						<div class="flex items-center gap-2">
							<Label for="isActive">Active</Label>
							<Switch {...props} bind:checked={$formData.isActive} />
						</div>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="isRepeatEnabled">
				<Form.Control>
					{#snippet children({ props })}
						<div class="flex items-center gap-2">
							<Label for="repeat">Enable Repeat</Label>
							<Switch {...props} bind:checked={$formData.isRepeatEnabled} />
						</div>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			{#if $formData.isRepeatEnabled}
				<Form.Field {form} name="repeatInterval">
					<Form.Control>
						{#snippet children({ props })}
							<Label for="repeatInterval">Repeat Interval</Label>
							<Select.Root type="single" bind:value={$formData.repeatInterval}>
								<Select.Trigger class="w-full" {...props}>
									{$formData.repeatInterval
										? $formData.repeatInterval
										: 'Select a repeat interval'}
								</Select.Trigger>
								<Select.Content>
									{#each Object.values(appointmentRepeatTypes) as type (type)}
										<Select.Item value={type as string}>{type}</Select.Item>
									{/each}
								</Select.Content>
							</Select.Root>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			{/if}
			<Form.Field {form} name="requiresUpfrontPayment">
				<Form.Control>
					{#snippet children({ props })}
						<div class="flex items-center gap-2">
							<Label for="requiresUpfrontPayment">Upfront Payment</Label>
							<Switch {...props} bind:checked={$formData.requiresUpfrontPayment} />
						</div>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			{#if $formData.requiresUpfrontPayment}
				<Form.Field {form} name="upfrontPaymentAmount">
					<Form.Control>
						{#snippet children({ props })}
							<Label for="upfrontPaymentAmount">Upfront Payment Amount</Label>
							<Input type="number" {...props} bind:value={$formData.upfrontPaymentAmount} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			{/if}
			<Form.Field {form} name="timeZoneId">
				<Form.Control>
					{#snippet children({ props })}
						<Label for="timeZoneId">Timezone</Label>
						<ComboInput
							{props}
							comboLabel="Select a timezone"
							comboData={TIMEZONES}
							comboValue={$formData.timeZoneId}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<Dialog.Footer>
				<Button variant="outline" disabled={$submitting} onclick={() => (showDialog = false)}
					>Cancel</Button
				>
				<Button type="submit" disabled={$submitting || $allErrors.length > 0}>Create</Button>
			</Dialog.Footer>
		</form>
	</Dialog.Content>
</Dialog.Root>
