<!-- Appointments module layout -->
<script lang="ts">
	//import { authGuard } from '$/lib'
	import { type IComponentCache } from '@repo/common'
	import { EditAppointment, ViewAppointment } from '$/components/appointments'
	import { DynamicModal } from '$/components/common'
	import { subDays, startOfTomorrow, differenceInMilliseconds } from 'date-fns'

	let { children }: { children: import('svelte').Snippet } = $props()

	let currentDate = $state(new Date())
	let previousDate = $derived(subDays(currentDate, 1))

	let componentsCollection = $state<IComponentCache[]>([
		{
			componentName: 'ViewAppointment',
			component: ViewAppointment,
			dialogClass: 'md:min-w-[620px]'
		},
		{
			componentName: 'EditAppointment',
			component: EditAppointment,
			dialogClass: 'md:min-w-[620px]'
		}
	])
	$effect(() => {
		const tomorrow = startOfTomorrow()
		const timeUntilMidnight = differenceInMilliseconds(tomorrow, new Date())

		const timer = setTimeout(() => {
			currentDate = new Date()
		}, timeUntilMidnight)

		return () => clearTimeout(timer)
	})
</script>

{@render children?.()}

<DynamicModal {componentsCollection} />
