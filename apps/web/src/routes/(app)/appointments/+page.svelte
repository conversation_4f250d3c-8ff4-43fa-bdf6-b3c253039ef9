<script lang="ts">
	import 'linq-extensions'
	import { Calendar } from '$/components/appointments'
	import { CardShell } from '$/components/layout'
	import { getAppointmentStore, getUtilsStore } from '$/stores'
	import type { IDynamicModal } from '@repo/common'

	let uStore = getUtilsStore()
	const aStore = getAppointmentStore()
	const openAppointmentDetails = () => {
		const newAppointmentModal: IDynamicModal = {
			canClose: false,
			title: 'Appointment Details',
			componentName: 'ViewAppointment',
			size: 'lg',
			color: 'default',
			description: 'View appointment detail',
			componentProps: {}
		}
		uStore.openDynamicModal(newAppointmentModal)
	}

	$effect(() => {
		if (aStore.selectedAppointment) {
			openAppointmentDetails()
		}
	})
</script>

<!-- <div class="grid gap-10">
	<Card.Root class="w-full rounded-none">
		<Card.Header>
			<Card.Title class="flex items-center justify-between">Appointment Dashboard</Card.Title>
		</Card.Header>
		<Card.Content>
			<Calendar showAddAppointment={false} />
		</Card.Content>
	</Card.Root>
</div> -->

<CardShell
	heading="Appointment Dashboard"
	subHeading="View and manage appointments across all locations"
>
	<!-- <Calendar2 showAddAppointment={false} /> -->
	<Calendar showAddAppointment={false} />
</CardShell>
