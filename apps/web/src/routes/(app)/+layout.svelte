<script lang="ts">
	import { LayoutWrapper } from '$/components/layout'

	import { getAuthStore } from '$/stores'
	import { HomeSheetModal, LayoutMain } from './(components)'

	let { children }: { children: any } = $props()
	const authStore = getAuthStore()
</script>

<LayoutWrapper>
	<LayoutMain>
		{@render children?.()}
	</LayoutMain>
	{#if !authStore.user}
		<HomeSheetModal />
	{/if}
</LayoutWrapper>
