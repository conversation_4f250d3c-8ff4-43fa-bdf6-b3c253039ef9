{"name": "worker", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "npx @actor-core/cli@latest dev src/index.ts", "dev:worker": "npx @actor-core/cli@latest dev src/index.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "description": "", "dependencies": {"@actor-core/cli": "^0.8.0", "@actor-core/framework-base": "^0.8.0", "@ai-sdk/openai": "^1.3.20", "@dotenvx/dotenvx": "^1.44.1", "@kripod/uuidv7": "^0.3.4", "@mastra/core": "^0.10.1", "@mastra/fastembed": "^0.10.0", "@mastra/loggers": "^0.10.0", "@mastra/memory": "latest", "@mastra/pg": "latest", "@mastra/rag": "latest", "@passlock/node": "^0.9.30", "@react-email/components": "^0.0.41", "@repo/common": "workspace:*", "@repo/drizzle": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "actor-core": "^0.8.0", "date-fns": "^4.1.0", "debug": "^4.4.1", "drizzle-orm": "^0.42.0", "gravatar-gen": "^1.0.2", "jose": "^6.0.11", "linq-extensions": "^1.0.4", "lodash-es": "^4.17.21", "plunk": "^0.6.0", "superjson": "^2.2.2", "valibot": "^1.1.0", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.15.2", "mastra": "latest", "typescript": "^5.8.3"}}