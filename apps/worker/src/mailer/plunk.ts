import { stringify } from 'superjson'
import { safeJsonParse } from '../server.helpers'


export interface SendParams {
	to: string | string[]
	subject: string
	body: string
	type?: 'html' | 'markdown'
	from?: string
	name?: string
	subscribed?: boolean
}

export interface PublishParams {
	event: string
	email: string
	subscribed?: boolean
	data?: { [p: string]: string | { persistent: boolean; value: string } }
}

type PlunkErrorType = 'TokenError' | 'NotFoundError'

const sendMail = async <T>({
	json,
	url,
	...options
}: RequestInit & {
	url: string
	json: any
	headers?: Record<string, string>
}) => {
	const apiUrl = 'https://api.useplunk.com/v1/send'
	const apiKey = process.env.PLUNK_SECRET_KEY
	const res = await fetch(
		new URL(url, apiUrl).toString(),
		{
			...options,
			headers: {
				Authorization: `Bearer ${apiKey}`,
				...(json && { 'Content-Type': 'application/json' }),
				...options.headers
			},
			body: json ? stringify(json) : undefined
		}
	)

	const text = await res.text()

	const data: any = safeJsonParse(text)

	if (res?.status === 401) {
		throw new Error(data?.message)
	}

	if (res?.status === 404) {
		throw new Error(data?.message)
	}

	if (!res.ok) {
		throw new Error(data?.message ?? 'Unknown API Error')
	}
	return data as T
}

export const plunk = {
	events: {
		/**
		 * Publishes an event to Plunk
		 * @param {string} event.event - The event you want to publish
		 * @param {string} event.email - The email associated with this event
		 * @param {Object=} event.data - The user data associated with this event
		 * @param {boolean=true} event.subscribed - Whether the user is subscribed to marketing emails
		 */
		track: async (event: PublishParams) => {
			return await sendMail<{
				success: true
			}>({
				method: 'POST',
				url: 'track',
				json: { ...event }
			})
		}
	},
	emails: {
		/**
		 * Sends a transactional email with Plunk
		 *
		 * @param {string} body.to - The email you want to send to
		 * @param {string} body.subject - The subject of the email
		 * @param {string} body.body - The body of the email
		 * @param {string=} body.from - The email you want to send from
		 * @param {string=} body.name - The name you want to send as
		 * @param {string=html} body.type - The type of email you want to send
		 * @param {boolean=false} body.subscribed - Whether the user is subscribed to marketing emails
		 */
		send: async (body: SendParams) => {
			return await sendMail<{
				success: true
			}>({
				method: 'POST',
				url: 'send',
				json: {
					...body
				}
			})
		}
	}
}

