/**
 * Example showing how to integrate state-helper with an actual actor
 * This demonstrates the complete integration pattern
 */

import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import type { ConnState } from '../server.helpers'
import { ActionTypes } from '@repo/common'
import {
	createStateRecord,
	updateStateRecord,
	deleteStateRecord,
	getStateRecords,
	getStateRecordsWithWhere,
	getStateRecordById,
	getStateRecordsByTeamId,
	searchStateRecords,
	createMultipleStateRecords,
	createWhereClause,
	createCondition,
	type ActorContext
} from './state-helper'

// Example interface for the actor's data
interface ExampleItem {
	id: string
	name: string
	description: string
	teamId: string
	categoryId: string
	isActive: boolean
	priority: number
	tags: string[]
	createdAt: Date
	updatedAt: Date
}

/**
 * Example actor using state-helper functions
 */
export const exampleStateActor = actor({
	state: [] as ExampleItem[],
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		/**
		 * Create a new item
		 */
		create: async (c, item: Partial<ExampleItem>): Promise<ExampleItem | undefined> => {
			c.log.info(`Creating item: ${item.name}`)
			
			const result = await createStateRecord<ExampleItem>(
				c as ActorContext,
				item,
				ActionTypes.manageTeam // Use appropriate RBAC action
			)

			if (result.success) {
				c.log.info(`Item created successfully: ${result.data?.id}`)
				return result.data
			} else {
				c.log.error(`Failed to create item: ${result.error}`)
				return undefined
			}
		},

		/**
		 * Update an existing item
		 */
		update: async (c, updates: Partial<ExampleItem> & { id: string }): Promise<ExampleItem | undefined> => {
			c.log.info(`Updating item: ${updates.id}`)
			
			const result = await updateStateRecord<ExampleItem>(
				c as ActorContext,
				updates,
				ActionTypes.manageTeam
			)

			if (result.success) {
				c.log.info(`Item updated successfully: ${updates.id}`)
				return result.data
			} else {
				c.log.error(`Failed to update item: ${result.error}`)
				return undefined
			}
		},

		/**
		 * Delete an item
		 */
		delete: async (c, itemId: string): Promise<boolean> => {
			c.log.info(`Deleting item: ${itemId}`)
			
			const result = await deleteStateRecord<ExampleItem>(
				c as ActorContext,
				itemId,
				ActionTypes.manageTeam
			)

			if (result.success) {
				c.log.info(`Item deleted successfully: ${itemId}`)
				return true
			} else {
				c.log.error(`Failed to delete item: ${result.error}`)
				return false
			}
		},

		/**
		 * Get all items
		 */
		getCollection: async (c): Promise<ExampleItem[]> => {
			const result = await getStateRecords<ExampleItem>(
				c as ActorContext,
				ActionTypes.readTeam
			)

			return result.success ? result.data || [] : []
		},

		/**
		 * Get item by ID
		 */
		getById: async (c, itemId: string): Promise<ExampleItem | undefined> => {
			const result = await getStateRecordById<ExampleItem>(
				c as ActorContext,
				itemId,
				ActionTypes.readTeam
			)

			return result.success ? result.data : undefined
		},

		/**
		 * Get items by team ID
		 */
		getByTeam: async (c, teamId: string): Promise<ExampleItem[]> => {
			const result = await getStateRecordsByTeamId<ExampleItem>(
				c as ActorContext,
				teamId,
				ActionTypes.readTeam
			)

			return result.success ? result.data || [] : []
		},

		/**
		 * Search items by name
		 */
		search: async (c, searchTerm: string): Promise<ExampleItem[]> => {
			const result = await searchStateRecords<ExampleItem>(
				c as ActorContext,
				'name',
				searchTerm,
				ActionTypes.readTeam
			)

			return result.success ? result.data || [] : []
		},

		/**
		 * Get active items with high priority
		 */
		getHighPriorityActive: async (c): Promise<ExampleItem[]> => {
			const whereClause = createWhereClause([
				createCondition('isActive', 'eq', true),
				createCondition('priority', 'gte', 8)
			], 'AND')

			const result = await getStateRecordsWithWhere<ExampleItem>(
				c as ActorContext,
				ActionTypes.readTeam,
				whereClause
			)

			return result.success ? result.data || [] : []
		},

		/**
		 * Get items by category
		 */
		getByCategory: async (c, categoryId: string): Promise<ExampleItem[]> => {
			const whereClause = createWhereClause([
				createCondition('categoryId', 'eq', categoryId),
				createCondition('isActive', 'eq', true)
			], 'AND')

			const result = await getStateRecordsWithWhere<ExampleItem>(
				c as ActorContext,
				ActionTypes.readTeam,
				whereClause
			)

			return result.success ? result.data || [] : []
		},

		/**
		 * Get items with specific tags
		 */
		getByTags: async (c, tags: string[]): Promise<ExampleItem[]> => {
			// Note: This is a simplified example. For array fields like tags,
			// you might need custom filtering logic
			const whereClause = createWhereClause([
				createCondition('isActive', 'eq', true)
			])

			const result = await getStateRecordsWithWhere<ExampleItem>(
				c as ActorContext,
				ActionTypes.readTeam,
				whereClause
			)

			if (result.success && result.data) {
				// Custom filtering for tags (since array operations are complex)
				const filteredItems = result.data.filter(item => 
					tags.some(tag => item.tags.includes(tag))
				)
				return filteredItems
			}

			return []
		},

		/**
		 * Bulk create items
		 */
		bulkCreate: async (c, items: Partial<ExampleItem>[]): Promise<ExampleItem[]> => {
			c.log.info(`Bulk creating ${items.length} items`)
			
			const result = await createMultipleStateRecords<ExampleItem>(
				c as ActorContext,
				items,
				ActionTypes.manageTeam
			)

			if (result.success) {
				c.log.info(`Bulk created ${result.data?.length} items successfully`)
				return result.data || []
			} else {
				c.log.error(`Failed to bulk create items: ${result.error}`)
				return []
			}
		},

		/**
		 * Advanced filtering with multiple criteria
		 */
		getFiltered: async (c, filters: {
			teamId?: string
			categoryId?: string
			isActive?: boolean
			minPriority?: number
			searchTerm?: string
		}): Promise<ExampleItem[]> => {
			const conditions = []

			// Build conditions dynamically based on provided filters
			if (filters.teamId) {
				conditions.push(createCondition('teamId', 'eq', filters.teamId))
			}

			if (filters.categoryId) {
				conditions.push(createCondition('categoryId', 'eq', filters.categoryId))
			}

			if (filters.isActive !== undefined) {
				conditions.push(createCondition('isActive', 'eq', filters.isActive))
			}

			if (filters.minPriority !== undefined) {
				conditions.push(createCondition('priority', 'gte', filters.minPriority))
			}

			if (filters.searchTerm) {
				conditions.push(createCondition('name', 'ilike', `%${filters.searchTerm}%`))
			}

			// If no conditions, return all items
			if (conditions.length === 0) {
				return await this.getCollection(c)
			}

			const whereClause = createWhereClause(conditions, 'AND')
			const result = await getStateRecordsWithWhere<ExampleItem>(
				c as ActorContext,
				ActionTypes.readTeam,
				whereClause
			)

			return result.success ? result.data || [] : []
		},

		/**
		 * Get statistics about the items
		 */
		getStats: async (c): Promise<{
			total: number
			active: number
			inactive: number
			highPriority: number
			byCategory: Record<string, number>
		}> => {
			const allItems = await this.getCollection(c)
			
			const stats = {
				total: allItems.length,
				active: allItems.filter(item => item.isActive).length,
				inactive: allItems.filter(item => !item.isActive).length,
				highPriority: allItems.filter(item => item.priority >= 8).length,
				byCategory: {} as Record<string, number>
			}

			// Count by category
			allItems.forEach(item => {
				stats.byCategory[item.categoryId] = (stats.byCategory[item.categoryId] || 0) + 1
			})

			return stats
		}
	}
})

// Export the actor type for client usage
export type ExampleStateActorType = typeof exampleStateActor
