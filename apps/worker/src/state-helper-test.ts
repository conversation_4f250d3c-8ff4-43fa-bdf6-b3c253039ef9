/**
 * Test file to verify the state-helper functionality
 * This demonstrates how to use the state helper in actor context
 */

import {
	createStateRecord,
	updateState<PERSON><PERSON>ord,
	deleteStateRecord,
	getStateRecords,
	getStateRecordsWithWhere,
	getStateRecordById,
	createWhere<PERSON>lause,
	createCondition,
	type ActorContext
} from './state-helper'
import { ActionTypes } from '@repo/common'

// Mock interfaces for testing
interface TestUser {
	id: string
	email: string
	displayName: string
	teamId: string
	disabled: boolean
	emailVerified: boolean
	defaultRole: string
	createdAt: Date
	updatedAt: Date
}

// Mock actor context for testing
const createMockContext = (): ActorContext => {
	const state: TestUser[] = []
	const broadcasts: Array<{ event: string; data?: any }> = []

	return {
		state,
		broadcast: (event: string, data?: any) => {
			broadcasts.push({ event, data })
			console.log(`📡 Broadcast: ${event}`, data ? `with data: ${JSON.stringify(data, null, 2)}` : '')
		},
		log: {
			info: (message: string) => console.log(`ℹ️  ${message}`),
			error: (message: string) => console.error(`❌ ${message}`),
			warn: (message: string) => console.warn(`⚠️  ${message}`)
		},
		conn: {
			state: {
				authData: {
					userId: 'test-user-123',
					teamId: 'test-team-456',
					defaultRole: 'team-admin',
					roles: ['team-admin']
				}
			}
		}
	}
}

/**
 * Test basic CRUD operations
 */
export async function testBasicCRUD() {
	console.log('🚀 Testing basic CRUD operations...\n')
	
	const c = createMockContext()

	try {
		// Test Create
		console.log('1. Testing CREATE operation:')
		const createResult = await createStateRecord<TestUser>(c, {
			email: '<EMAIL>',
			displayName: 'John Doe',
			teamId: 'test-team-456',
			disabled: false,
			emailVerified: true,
			defaultRole: 'team-member'
		}, ActionTypes.manageMembers)

		if (createResult.success) {
			console.log(`✅ User created: ${createResult.data?.email}`)
			console.log(`   ID: ${createResult.data?.id}`)
		} else {
			console.log(`❌ Create failed: ${createResult.error}`)
			return
		}

		const userId = createResult.data!.id

		// Test Read
		console.log('\n2. Testing READ operation:')
		const readResult = await getStateRecordById<TestUser>(c, userId, ActionTypes.readmembers)
		
		if (readResult.success) {
			console.log(`✅ User found: ${readResult.data?.displayName}`)
		} else {
			console.log(`❌ Read failed: ${readResult.error}`)
		}

		// Test Update
		console.log('\n3. Testing UPDATE operation:')
		const updateResult = await updateStateRecord<TestUser>(c, {
			id: userId,
			displayName: 'John Smith',
			emailVerified: false
		}, ActionTypes.manageMembers)

		if (updateResult.success) {
			console.log(`✅ User updated: ${updateResult.data?.displayName}`)
			console.log(`   Email verified: ${updateResult.data?.emailVerified}`)
		} else {
			console.log(`❌ Update failed: ${updateResult.error}`)
		}

		// Test Delete
		console.log('\n4. Testing DELETE operation:')
		const deleteResult = await deleteStateRecord<TestUser>(c, userId, ActionTypes.manageMembers)

		if (deleteResult.success) {
			console.log(`✅ User deleted: ${deleteResult.data?.email}`)
		} else {
			console.log(`❌ Delete failed: ${deleteResult.error}`)
		}

		console.log(`\n📊 Final state count: ${c.state.length}`)

	} catch (error) {
		console.error('❌ Test failed:', error)
	}
}

/**
 * Test dynamic where clauses
 */
export async function testWhereClause() {
	console.log('\n🔍 Testing dynamic where clauses...\n')
	
	const c = createMockContext()

	// Create test data
	const testUsers = [
		{
			email: '<EMAIL>',
			displayName: 'Admin User',
			teamId: 'test-team-456',
			disabled: false,
			emailVerified: true,
			defaultRole: 'team-admin'
		},
		{
			email: '<EMAIL>',
			displayName: 'Team Member',
			teamId: 'test-team-456',
			disabled: false,
			emailVerified: false,
			defaultRole: 'team-member'
		},
		{
			email: '<EMAIL>',
			displayName: 'Disabled User',
			teamId: 'test-team-456',
			disabled: true,
			emailVerified: true,
			defaultRole: 'team-member'
		}
	]

	// Create test users
	for (const userData of testUsers) {
		await createStateRecord<TestUser>(c, userData, ActionTypes.manageMembers)
	}

	console.log(`Created ${c.state.length} test users\n`)

	try {
		// Test 1: Get active users
		console.log('1. Testing: Get active users')
		const activeUsersResult = await getStateRecordsWithWhere<TestUser>(c, ActionTypes.readmembers,
			createWhereClause([
				createCondition('disabled', 'eq', false)
			])
		)

		if (activeUsersResult.success) {
			console.log(`✅ Found ${activeUsersResult.data?.length} active users`)
			activeUsersResult.data?.forEach(user => 
				console.log(`   - ${user.displayName} (${user.email})`)
			)
		}

		// Test 2: Get verified admins
		console.log('\n2. Testing: Get verified team admins')
		const verifiedAdminsResult = await getStateRecordsWithWhere<TestUser>(c, ActionTypes.readmembers,
			createWhereClause([
				createCondition('defaultRole', 'eq', 'team-admin'),
				createCondition('emailVerified', 'eq', true),
				createCondition('disabled', 'eq', false)
			], 'AND')
		)

		if (verifiedAdminsResult.success) {
			console.log(`✅ Found ${verifiedAdminsResult.data?.length} verified admins`)
			verifiedAdminsResult.data?.forEach(user => 
				console.log(`   - ${user.displayName} (${user.defaultRole})`)
			)
		}

		// Test 3: Get users with specific roles
		console.log('\n3. Testing: Get users with specific roles')
		const roleUsersResult = await getStateRecordsWithWhere<TestUser>(c, ActionTypes.readmembers,
			createWhereClause([
				createCondition('defaultRole', 'in', undefined, ['team-admin', 'team-member'])
			])
		)

		if (roleUsersResult.success) {
			console.log(`✅ Found ${roleUsersResult.data?.length} users with specified roles`)
			roleUsersResult.data?.forEach(user => 
				console.log(`   - ${user.displayName} (${user.defaultRole})`)
			)
		}

		// Test 4: Text search
		console.log('\n4. Testing: Text search')
		const searchResult = await getStateRecordsWithWhere<TestUser>(c, ActionTypes.readmembers,
			createWhereClause([
				createCondition('displayName', 'ilike', '%admin%')
			])
		)

		if (searchResult.success) {
			console.log(`✅ Found ${searchResult.data?.length} users matching 'admin'`)
			searchResult.data?.forEach(user => 
				console.log(`   - ${user.displayName}`)
			)
		}

	} catch (error) {
		console.error('❌ Where clause test failed:', error)
	}
}

/**
 * Test event broadcasting
 */
export async function testEventBroadcasting() {
	console.log('\n📡 Testing event broadcasting...\n')
	
	const c = createMockContext()
	const broadcasts: Array<{ event: string; data?: any }> = []

	// Override broadcast to capture events
	const originalBroadcast = c.broadcast
	c.broadcast = (event: string, data?: any) => {
		broadcasts.push({ event, data })
		originalBroadcast(event, data)
	}

	try {
		// Create a user (should broadcast create events)
		const createResult = await createStateRecord<TestUser>(c, {
			email: '<EMAIL>',
			displayName: 'Broadcast Test',
			teamId: 'test-team-456',
			disabled: false,
			emailVerified: true,
			defaultRole: 'team-member'
		}, ActionTypes.manageMembers)

		if (createResult.success) {
			const userId = createResult.data!.id

			// Update the user (should broadcast update events)
			await updateStateRecord<TestUser>(c, {
				id: userId,
				displayName: 'Updated Broadcast Test'
			}, ActionTypes.manageMembers)

			// Delete the user (should broadcast delete events)
			await deleteStateRecord<TestUser>(c, userId, ActionTypes.manageMembers)
		}

		console.log(`📊 Total broadcasts captured: ${broadcasts.length}`)
		broadcasts.forEach((broadcast, index) => {
			console.log(`   ${index + 1}. ${broadcast.event}`)
		})

	} catch (error) {
		console.error('❌ Broadcasting test failed:', error)
	}
}

/**
 * Run all tests
 */
export async function runAllStateHelperTests() {
	console.log('🧪 Starting State Helper Tests...\n')
	
	await testBasicCRUD()
	await testWhereClause()
	await testEventBroadcasting()
	
	console.log('\n✅ All state helper tests completed!')
}

// Export individual test functions for selective testing
export {
	testBasicCRUD,
	testWhereClause,
	testEventBroadcasting
}
