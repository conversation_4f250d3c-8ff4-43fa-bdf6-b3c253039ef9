import { setup } from 'actor-core'

import '@dotenvx/dotenvx/config'
import { initSuperJson } from '@repo/common'
import { db } from './clients'
import 'linq-extensions'

import {
	authActor,
	appointmentsActor,
	teamActor,
	calendarBlocksActor,
	membersActor,
	genderListActor,
	appointmentTypesActor,
	storageBucketActor,
	storageFilesActor,
	userRoleActor,
	rolesListActor,
	usersActor,
	teamPaymentProviderActor,
	timeZonesActor,
	workdaySettingActor,
	teamLocationsActor,
	teamBankDetailActor,
	membersInviteActor
} from './actors'

initSuperJson()

export const app = setup({
	actors: {
		auth: authActor,
		appointmentsActor,
		teamActor,
		calendarBlocksActor,
		membersActor,
		genderListActor,
		appointmentTypesActor,
		storageBucketActor,
		storageFilesActor,
		userRoleActor,
		rolesListActor,
		usersActor,
		teamPaymentProviderActor,
		timeZonesActor,
		workdaySettingActor,
		teamLocationsActor,
		teamBankDetailActor,
		memberInviteActor: membersInviteActor
	},
	cors: { origin: '*' } // Configure CORS for your production domains in production
})

// Export app type for client usage
export type ActorCoreApp = typeof app

