import 'linq-extensions'
import { ActorEvents, PostgresEvents, rbac, type ActionType, type paramsType } from '@repo/common'

// Types for dynamic where clauses (same as drizzle-helper)
export type WhereOperator =
	| 'eq'
	| 'ne'
	| 'gt'
	| 'gte'
	| 'lt'
	| 'lte'
	| 'like'
	| 'ilike'
	| 'in'
	| 'notIn'
	| 'isNull'
	| 'isNotNull'
	| 'between'

export interface WhereCondition {
	field: string
	operator: WhereOperator
	value?: any
	values?: any[] // For 'in', 'notIn', 'between' operators
}

export interface WhereClause {
	conditions: WhereCondition[]
	logic?: 'AND' | 'OR' // How to combine multiple conditions
}

// Actor context interface for type safety
export interface ActorContext {
	state: any[]
	broadcast: (event: string, data?: any) => void
	log: {
		info: (message: string) => void
		error: (message: string) => void
		warn: (message: string) => void
	}
	conn: {
		state: {
			authData: {
				userId: string
				teamId: string
				defaultRole: string
				roles: string[]
			}
		}
	}
}

// Helper function to build where clauses dynamically for state filtering using linq-extensions
function buildStateFilter<T>(whereClause: WhereClause): (item: T) => boolean {
	if (!whereClause.conditions || whereClause.conditions.length === 0) {
		return () => true
	}

	return (item: T) => {
		const results = whereClause.conditions.map(condition => {
			const fieldValue = (item as any)[condition.field]

			switch (condition.operator) {
				case 'eq':
					return fieldValue === condition.value
				case 'ne':
					return fieldValue !== condition.value
				case 'gt':
					return fieldValue > condition.value
				case 'gte':
					return fieldValue >= condition.value
				case 'lt':
					return fieldValue < condition.value
				case 'lte':
					return fieldValue <= condition.value
				case 'like':
					return typeof fieldValue === 'string' && fieldValue.includes(condition.value)
				case 'ilike':
					return typeof fieldValue === 'string' &&
						fieldValue.toLowerCase().includes(condition.value.toLowerCase())
				case 'in':
					return condition.values && condition.values.includes(fieldValue)
				case 'notIn':
					return condition.values && !condition.values.includes(fieldValue)
				case 'isNull':
					return fieldValue == null
				case 'isNotNull':
					return fieldValue != null
				case 'between':
					return condition.values && condition.values.length === 2 &&
						fieldValue >= condition.values[0] && fieldValue <= condition.values[1]
				default:
					throw new Error(`Unsupported operator: ${condition.operator}`)
			}
		})

		// Combine results with AND or OR using linq-extensions
		return whereClause.logic === 'OR'
			? results.any(result => result)
			: results.all(result => result)
	}
}

// Convenience functions for common query patterns
export function createWhereClause(conditions: WhereCondition[], logic: 'AND' | 'OR' = 'AND'): WhereClause {
	return { conditions, logic }
}

export function createCondition(field: string, operator: WhereOperator, value?: any, values?: any[]): WhereCondition {
	return { field, operator, value, values }
}

// Permission checking function
export const checkPermission = async (
	action: ActionType,
	payload: Record<string, any>,
	authData: any
): Promise<boolean> => {
	try {
		const params: paramsType = {
			payload,
			authData
		}

		// Use the user's role to check permission
		const role = authData.defaultRole
		const hasPermission = await rbac.can(role, action, params)
		return hasPermission
	} catch (error) {
		console.error('Permission check failed:', error)
		return false
	}
}

// Create a new record in state
export async function createStateRecord<T extends { id: string }>(
	c: ActorContext,
	data: Partial<T>,
	rbacAction: ActionType
): Promise<{ success: boolean; data?: T; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, data, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		// Generate ID if not provided
		const newRecord = {
			...data,
			id: data.id || crypto.randomUUID(),
			createdAt: new Date(),
			updatedAt: new Date()
		} as T

		// Add to state
		c.state.push(newRecord)

		// Broadcast events
		c.broadcast(ActorEvents.ItemCreated, newRecord)
		c.broadcast(PostgresEvents.ItemCreated, newRecord)

		c.log.info(`Created record with ID: ${newRecord.id}`)
		return { success: true, data: newRecord }
	} catch (error) {
		c.log.error(`Create record error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Get all records from state (original function for backward compatibility)
export async function getStateRecords<T>(
	c: ActorContext,
	rbacAction: ActionType
): Promise<{ success: boolean; data?: T[]; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		return { success: true, data: c.state as T[] }
	} catch (error) {
		c.log.error(`Get records error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Enhanced function with dynamic where clauses using linq-extensions
export async function getStateRecordsWithWhere<T>(
	c: ActorContext,
	rbacAction: ActionType,
	whereClause?: WhereClause
): Promise<{ success: boolean; data?: T[]; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		let records = c.state as T[]

		// Apply where clause if provided using linq-extensions
		if (whereClause) {
			const filter = buildStateFilter<T>(whereClause)
			records = records.where(filter).toArray()
		}

		return { success: true, data: records }
	} catch (error) {
		c.log.error(`Get records with where error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Get a single record by ID using linq-extensions
export async function getStateRecordById<T extends { id: string }>(
	c: ActorContext,
	id: string,
	rbacAction: ActionType
): Promise<{ success: boolean; data?: T; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, { id }, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		const record = (c.state as T[]).firstOrDefault(item => item.id === id)

		return record
			? { success: true, data: record }
			: { success: false, error: "Record not found" }
	} catch (error) {
		c.log.error(`Get record by ID error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Update a record by ID using linq-extensions
export async function updateStateRecord<T extends { id: string }>(
	c: ActorContext,
	data: Partial<T> & { id: string },
	rbacAction: ActionType
): Promise<{ success: boolean; data?: T; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, data, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		const stateArray = c.state as T[]
		const existingRecord = stateArray.firstOrDefault(item => item.id === data.id)

		if (!existingRecord) {
			return { success: false, error: "Record not found" }
		}

		// Find index for direct array manipulation
		const index = stateArray.findIndex(item => item.id === data.id)

		// Update the record
		const updatedRecord = {
			...existingRecord,
			...data,
			updatedAt: new Date()
		} as T

		c.state[index] = updatedRecord

		// Broadcast events
		c.broadcast(ActorEvents.ItemUpdated, updatedRecord)
		c.broadcast(PostgresEvents.ItemUpdated, updatedRecord)

		c.log.info(`Updated record with ID: ${data.id}`)
		return { success: true, data: updatedRecord }
	} catch (error) {
		c.log.error(`Update record error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Delete a record by ID using linq-extensions
export async function deleteStateRecord<T extends { id: string }>(
	c: ActorContext,
	id: string,
	rbacAction: ActionType
): Promise<{ success: boolean; data?: T; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, { id }, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		const stateArray = c.state as T[]
		const recordToDelete = stateArray.firstOrDefault(item => item.id === id)

		if (!recordToDelete) {
			return { success: false, error: "Record not found" }
		}

		// Find index for direct array manipulation
		const index = stateArray.findIndex(item => item.id === id)

		// Remove the record
		const deletedRecord = c.state.splice(index, 1)[0] as T

		// Broadcast events
		c.broadcast(ActorEvents.ItemDeleted, deletedRecord)
		c.broadcast(PostgresEvents.ItemDeleted, deletedRecord)

		c.log.info(`Deleted record with ID: ${id}`)
		return { success: true, data: deletedRecord }
	} catch (error) {
		c.log.error(`Delete record error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Bulk operations
export async function createMultipleStateRecords<T extends { id: string }>(
	c: ActorContext,
	dataArray: Partial<T>[],
	rbacAction: ActionType
): Promise<{ success: boolean; data?: T[]; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		const newRecords: T[] = []

		for (const data of dataArray) {
			const newRecord = {
				...data,
				id: data.id || crypto.randomUUID(),
				createdAt: new Date(),
				updatedAt: new Date()
			} as T

			c.state.push(newRecord)
			newRecords.push(newRecord)

			// Broadcast individual item events
			c.broadcast(ActorEvents.ItemCreated, newRecord)
			c.broadcast(PostgresEvents.ItemCreated, newRecord)
		}

		// Broadcast collection updated event
		c.broadcast(PostgresEvents.CollectionUpdated, { action: 'bulk_create', count: newRecords.length })

		c.log.info(`Created ${newRecords.length} records`)
		return { success: true, data: newRecords }
	} catch (error) {
		c.log.error(`Bulk create error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Get records by team ID (common pattern) using linq-extensions
export async function getStateRecordsByTeamId<T extends { teamId: string }>(
	c: ActorContext,
	teamId: string,
	rbacAction: ActionType
): Promise<{ success: boolean; data?: T[]; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, { teamId }, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		const records = (c.state as T[]).where(item => item.teamId === teamId).toArray()

		return { success: true, data: records }
	} catch (error) {
		c.log.error(`Get records by team ID error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Search records with text (common pattern) using linq-extensions
export async function searchStateRecords<T>(
	c: ActorContext,
	searchField: string,
	searchTerm: string,
	rbacAction: ActionType
): Promise<{ success: boolean; data?: T[]; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		const searchTermLower = searchTerm.toLowerCase()
		const records = (c.state as T[]).where(item => {
			const fieldValue = (item as any)[searchField]
			return typeof fieldValue === 'string' &&
				fieldValue.toLowerCase().includes(searchTermLower)
		}).toArray()

		return { success: true, data: records }
	} catch (error) {
		c.log.error(`Search records error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Additional linq-extensions helper functions

// Count records with optional filtering using linq-extensions
export async function countStateRecords<T>(
	c: ActorContext,
	rbacAction: ActionType,
	whereClause?: WhereClause
): Promise<{ success: boolean; data?: number; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		let records = c.state as T[]

		// Apply where clause if provided using linq-extensions
		if (whereClause) {
			const filter = buildStateFilter<T>(whereClause)
			records = records.where(filter).toArray()
		}

		return { success: true, data: records.length }
	} catch (error) {
		c.log.error(`Count records error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Check if any record exists matching criteria using linq-extensions
export async function stateRecordExists<T>(
	c: ActorContext,
	rbacAction: ActionType,
	whereClause: WhereClause
): Promise<{ success: boolean; data?: boolean; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		const filter = buildStateFilter<T>(whereClause)
		const exists = (c.state as T[]).any(filter)

		return { success: true, data: exists }
	} catch (error) {
		c.log.error(`Record exists check error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Get first record matching criteria using linq-extensions
export async function getFirstStateRecord<T>(
	c: ActorContext,
	rbacAction: ActionType,
	whereClause: WhereClause
): Promise<{ success: boolean; data?: T; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		const filter = buildStateFilter<T>(whereClause)
		const record = (c.state as T[]).firstOrDefault(filter)

		return record
			? { success: true, data: record }
			: { success: false, error: "No record found matching criteria" }
	} catch (error) {
		c.log.error(`Get first record error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Get last record matching criteria using linq-extensions
export async function getLastStateRecord<T>(
	c: ActorContext,
	rbacAction: ActionType,
	whereClause?: WhereClause
): Promise<{ success: boolean; data?: T; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		let records = c.state as T[]

		// Apply where clause if provided using linq-extensions
		if (whereClause) {
			const filter = buildStateFilter<T>(whereClause)
			records = records.where(filter).toArray()
		}

		const record = records.lastOrDefault()

		return record
			? { success: true, data: record }
			: { success: false, error: "No record found" }
	} catch (error) {
		c.log.error(`Get last record error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Select specific fields from records using linq-extensions
export async function selectStateRecordFields<T, TResult>(
	c: ActorContext,
	rbacAction: ActionType,
	selector: (item: T) => TResult,
	whereClause?: WhereClause
): Promise<{ success: boolean; data?: TResult[]; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		let query = c.state as T[]

		// Apply where clause if provided using linq-extensions
		if (whereClause) {
			const filter = buildStateFilter<T>(whereClause)
			query = query.where(filter).toArray()
		}

		const results = query.select(selector).toArray()

		return { success: true, data: results }
	} catch (error) {
		c.log.error(`Select fields error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Group records by a key using linq-extensions
export async function groupStateRecords<T, TKey>(
	c: ActorContext,
	rbacAction: ActionType,
	keySelector: (item: T) => TKey,
	whereClause?: WhereClause
): Promise<{ success: boolean; data?: Array<{ key: TKey; items: T[] }>; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		let query = c.state as T[]

		// Apply where clause if provided using linq-extensions
		if (whereClause) {
			const filter = buildStateFilter<T>(whereClause)
			query = query.where(filter).toArray()
		}

		const groups = query.groupBy(keySelector).select(group => ({
			key: group.key,
			items: group.toArray()
		})).toArray()

		return { success: true, data: groups }
	} catch (error) {
		c.log.error(`Group records error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Order records using linq-extensions
export async function orderStateRecords<T, TKey>(
	c: ActorContext,
	rbacAction: ActionType,
	keySelector: (item: T) => TKey,
	descending: boolean = false,
	whereClause?: WhereClause
): Promise<{ success: boolean; data?: T[]; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		let query = c.state as T[]

		// Apply where clause if provided using linq-extensions
		if (whereClause) {
			const filter = buildStateFilter<T>(whereClause)
			query = query.where(filter).toArray()
		}

		const orderedRecords = descending
			? query.orderByDescending(keySelector).toArray()
			: query.orderBy(keySelector).toArray()

		return { success: true, data: orderedRecords }
	} catch (error) {
		c.log.error(`Order records error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Take a specific number of records using linq-extensions
export async function takeStateRecords<T>(
	c: ActorContext,
	rbacAction: ActionType,
	count: number,
	whereClause?: WhereClause
): Promise<{ success: boolean; data?: T[]; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		let query = c.state as T[]

		// Apply where clause if provided using linq-extensions
		if (whereClause) {
			const filter = buildStateFilter<T>(whereClause)
			query = query.where(filter).toArray()
		}

		const records = query.take(count).toArray()

		return { success: true, data: records }
	} catch (error) {
		c.log.error(`Take records error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Skip and take records for pagination using linq-extensions
export async function paginateStateRecords<T>(
	c: ActorContext,
	rbacAction: ActionType,
	skip: number,
	take: number,
	whereClause?: WhereClause
): Promise<{ success: boolean; data?: T[]; total?: number; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		let query = c.state as T[]

		// Apply where clause if provided using linq-extensions
		if (whereClause) {
			const filter = buildStateFilter<T>(whereClause)
			query = query.where(filter).toArray()
		}

		const total = query.length
		const records = query.skip(skip).take(take).toArray()

		return { success: true, data: records, total }
	} catch (error) {
		c.log.error(`Paginate records error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Get distinct values using linq-extensions
export async function getDistinctStateValues<T, TKey>(
	c: ActorContext,
	rbacAction: ActionType,
	keySelector: (item: T) => TKey,
	whereClause?: WhereClause
): Promise<{ success: boolean; data?: TKey[]; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		let query = c.state as T[]

		// Apply where clause if provided using linq-extensions
		if (whereClause) {
			const filter = buildStateFilter<T>(whereClause)
			query = query.where(filter).toArray()
		}

		const distinctValues = query.select(keySelector).distinct().toArray()

		return { success: true, data: distinctValues }
	} catch (error) {
		c.log.error(`Get distinct values error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Aggregate functions using linq-extensions
export async function aggregateStateRecords<T>(
	c: ActorContext,
	rbacAction: ActionType,
	whereClause?: WhereClause
): Promise<{
	success: boolean
	data?: {
		count: number
		min?: T
		max?: T
	}
	error?: string
}> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		let query = c.state as T[]

		// Apply where clause if provided using linq-extensions
		if (whereClause) {
			const filter = buildStateFilter<T>(whereClause)
			query = query.where(filter).toArray()
		}

		const count = query.length
		const min = query.length > 0 ? query.first() : undefined
		const max = query.length > 0 ? query.last() : undefined

		return {
			success: true,
			data: { count, min, max }
		}
	} catch (error) {
		c.log.error(`Aggregate records error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}
