/**
 * Updated examples demonstrating the simplified drizzle-helper functions
 * WITHOUT RBAC permission checks - pure Drizzle ORM operations
 */

import {
	createRecord,
	updateR<PERSON>ord,
	deleteR<PERSON>ord,
	getRecords,
	getRecordsWithWhere,
	getRecordById,
	getRecordsByTeamId,
	createMultipleRecords,
	updateR<PERSON>ordsWhere,
	deleteRecordsWhere,
	countRecords,
	recordExists,
	upsertRecord,
	createWhereClause,
	createCondition,
	type Where<PERSON>laus<PERSON>
} from './drizzle-helper'

// Example interfaces
interface User {
	id: string
	email: string
	displayName: string
	teamId: string
	disabled: boolean
	emailVerified: boolean
	defaultRole: string
	createdAt: Date
	updatedAt: Date
}

interface TeamMember {
	id: string
	userId: string
	teamId: string
	teamLocationId: string
	role: string
	status: string
	disabled: boolean
	createdAt: Date
	updatedAt: Date
}

/**
 * EXAMPLE 1: Create a new user (no RBAC checks)
 */
export async function createUser(userData: Partial<User>) {
	const result = await createRecord<User>('member', userData)

	if (result.success) {
		console.log(`User created successfully: ${result.data?.email}`)
	} else {
		console.error(`Failed to create user: ${result.error}`)
	}

	return result
}

/**
 * EXAMPLE 2: Update user information (no RBAC checks)
 */
export async function updateUser(updates: Partial<User> & { id: string }) {
	const result = await updateRecord<User>('member', updates)

	if (result.success) {
		console.log(`User updated successfully: ${updates.id}`)
	}

	return result
}

/**
 * EXAMPLE 3: Delete a user (no RBAC checks)
 */
export async function deleteUser(userId: string) {
	const result = await deleteRecord<User>('member', { id: userId })

	if (result.success) {
		console.log(`User deleted successfully: ${userId}`)
	}

	return result
}

/**
 * EXAMPLE 4: Get all users (no filtering, no RBAC)
 */
export async function getAllUsers() {
	return await getRecords<User>('member')
}

/**
 * EXAMPLE 5: Get user by ID (no RBAC checks)
 */
export async function getUserById(userId: string) {
	return await getRecordById<User>('member', userId)
}

/**
 * EXAMPLE 6: Get users by team ID (no RBAC checks)
 */
export async function getUsersByTeam(teamId: string) {
	return await getRecordsByTeamId<User>('member', teamId)
}

/**
 * EXAMPLE 7: Get active users with verified emails
 */
export async function getActiveVerifiedUsers() {
	const whereClause = createWhereClause([
		createCondition('disabled', 'eq', false),
		createCondition('emailVerified', 'eq', true)
	], 'AND')

	return await getRecordsWithWhere<User>('member', whereClause)
}

/**
 * EXAMPLE 8: Get users with specific roles
 */
export async function getUsersByRoles(roles: string[]) {
	const whereClause = createWhereClause([
		createCondition('defaultRole', 'in', undefined, roles)
	])

	return await getRecordsWithWhere<User>('member', whereClause)
}

/**
 * EXAMPLE 9: Search users by display name (case-insensitive)
 */
export async function searchUsersByName(searchTerm: string) {
	const whereClause = createWhereClause([
		createCondition('displayName', 'ilike', `%${searchTerm}%`)
	])

	return await getRecordsWithWhere<User>('member', whereClause)
}

/**
 * EXAMPLE 10: Get users created in the last 30 days
 */
export async function getRecentUsers() {
	const thirtyDaysAgo = new Date()
	thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

	const whereClause = createWhereClause([
		createCondition('createdAt', 'gte', thirtyDaysAgo)
	])

	return await getRecordsWithWhere<User>('member', whereClause)
}

/**
 * EXAMPLE 11: Complex query - Active team admins with verified emails
 */
export async function getActiveTeamAdmins(teamId: string) {
	const whereClause = createWhereClause([
		createCondition('teamId', 'eq', teamId),
		createCondition('disabled', 'eq', false),
		createCondition('emailVerified', 'eq', true),
		createCondition('defaultRole', 'eq', 'team-admin')
	], 'AND')

	return await getRecordsWithWhere<User>('member', whereClause)
}

/**
 * EXAMPLE 12: OR condition - Users with either verified email OR phone
 */
export async function getVerifiedUsers() {
	const whereClause = createWhereClause([
		createCondition('emailVerified', 'eq', true),
		createCondition('phoneNumberVerified', 'eq', true)
	], 'OR')

	return await getRecordsWithWhere<User>('member', whereClause)
}

/**
 * EXAMPLE 13: Bulk create multiple users
 */
export async function createMultipleUsers(usersData: Partial<User>[]) {
	const result = await createMultipleRecords<User>('member', usersData)

	if (result.success) {
		console.log(`Created ${result.data?.length} users successfully`)
	}

	return result
}

/**
 * EXAMPLE 14: Bulk update users with where clause
 */
export async function deactivateUsersInTeam(teamId: string) {
	const whereClause = createWhereClause([
		createCondition('teamId', 'eq', teamId)
	])

	const result = await updateRecordsWhere<User>('member', 
		{ disabled: true }, 
		whereClause
	)

	if (result.success) {
		console.log(`Deactivated ${result.data?.length} users in team ${teamId}`)
	}

	return result
}

/**
 * EXAMPLE 15: Bulk delete inactive users
 */
export async function deleteInactiveUsers() {
	const whereClause = createWhereClause([
		createCondition('disabled', 'eq', true)
	])

	const result = await deleteRecordsWhere<User>('member', whereClause)

	if (result.success) {
		console.log(`Deleted ${result.data?.length} inactive users`)
	}

	return result
}

/**
 * EXAMPLE 16: Count records with filtering
 */
export async function countActiveUsers() {
	const whereClause = createWhereClause([
		createCondition('disabled', 'eq', false)
	])

	const result = await countRecords('member', whereClause)

	if (result.success) {
		console.log(`Found ${result.data} active users`)
	}

	return result
}

/**
 * EXAMPLE 17: Check if user exists
 */
export async function checkUserExists(userId: string) {
	const result = await recordExists('member', userId)

	if (result.success) {
		console.log(`User ${userId} ${result.data ? 'exists' : 'does not exist'}`)
	}

	return result
}

/**
 * EXAMPLE 18: Upsert (insert or update)
 */
export async function upsertUser(userData: Partial<User>) {
	const result = await upsertRecord<User>('member', userData)

	if (result.success) {
		console.log(`User upserted successfully: ${result.data?.email}`)
	}

	return result
}

/**
 * EXAMPLE 19: Dynamic filtering based on parameters
 */
export async function filterUsers(filters: {
	teamId?: string
	isActive?: boolean
	role?: string
	searchTerm?: string
	emailVerified?: boolean
}) {
	const conditions = []

	// Add filters dynamically
	if (filters.teamId) {
		conditions.push(createCondition('teamId', 'eq', filters.teamId))
	}

	if (filters.isActive !== undefined) {
		conditions.push(createCondition('disabled', 'eq', !filters.isActive))
	}

	if (filters.role) {
		conditions.push(createCondition('defaultRole', 'eq', filters.role))
	}

	if (filters.searchTerm) {
		conditions.push(createCondition('displayName', 'ilike', `%${filters.searchTerm}%`))
	}

	if (filters.emailVerified !== undefined) {
		conditions.push(createCondition('emailVerified', 'eq', filters.emailVerified))
	}

	// If no conditions, return all users
	if (conditions.length === 0) {
		return await getRecords<User>('member')
	}

	const whereClause = createWhereClause(conditions, 'AND')
	return await getRecordsWithWhere<User>('member', whereClause)
}

/**
 * EXAMPLE 20: Working with team members
 */
export async function manageTeamMember() {
	// Create a team member
	const newMember = await createRecord<TeamMember>('member', {
		userId: 'user-123',
		teamId: 'team-456',
		teamLocationId: 'location-789',
		role: 'team-member',
		status: 'Active',
		disabled: false
	})

	if (newMember.success) {
		// Update the member's role
		const updatedMember = await updateRecord<TeamMember>('member', {
			id: newMember.data!.id,
			role: 'team-admin'
		})

		// Get all active members in the team
		const activeMembers = await getRecordsWithWhere<TeamMember>('member', 
			createWhereClause([
				createCondition('teamId', 'eq', 'team-456'),
				createCondition('disabled', 'eq', false)
			])
		)

		return { newMember, updatedMember, activeMembers }
	}

	return { error: newMember.error }
}
