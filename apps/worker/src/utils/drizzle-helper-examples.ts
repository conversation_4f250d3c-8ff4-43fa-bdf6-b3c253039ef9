/**
 * Examples demonstrating how to use the enhanced drizzle-helper functions
 * with dynamic where clauses
 */

import {
	getRecordsWithWhere,
	createWhereClause,
	createCondition,
	type WhereClause,
	type WhereCondition
} from './utils/drizzle-helper'
import type { AuthData } from './server.helpers'

// Mock auth data for examples
const mockAuthData: AuthData = {
	userId: 'user-123',
	teamId: 'team-456',
	defaultRole: 'team-admin',
	roles: ['team-admin']
}

/**
 * EXAMPLE 1: Simple equality condition
 * Find all users with a specific email
 */
export async function getUserByEmail(email: string) {
	const whereClause = createWhereClause([
		createCondition('email', 'eq', email)
	])

	return await getRecordsWithWhere(
		'user',
		'readUser',
		mockAuthData,
		whereClause
	)
}

/**
 * EXAMPLE 2: Multiple conditions with AND logic
 * Find active users in a specific team
 */
export async function getActiveUsersInTeam(teamId: string) {
	const whereClause = createWhereClause([
		createCondition('teamId', 'eq', teamId),
		createCondition('disabled', 'eq', false)
	], 'AND')

	return await getRecordsWithWhere(
		'user',
		'readUser',
		mockAuthData,
		whereClause
	)
}

/**
 * EXAMPLE 3: OR conditions
 * Find users with either verified email OR verified phone
 */
export async function getVerifiedUsers() {
	const whereClause = createWhereClause([
		createCondition('emailVerified', 'eq', true),
		createCondition('phoneNumberVerified', 'eq', true)
	], 'OR')

	return await getRecordsWithWhere(
		'user',
		'readUser',
		mockAuthData,
		whereClause
	)
}

/**
 * EXAMPLE 4: LIKE operator for text search
 * Find users whose display name contains a search term
 */
export async function searchUsersByName(searchTerm: string) {
	const whereClause = createWhereClause([
		createCondition('displayName', 'ilike', `%${searchTerm}%`)
	])

	return await getRecordsWithWhere(
		'user',
		'readUser',
		mockAuthData,
		whereClause
	)
}

/**
 * EXAMPLE 5: IN operator for multiple values
 * Find users with specific roles
 */
export async function getUsersByRoles(roles: string[]) {
	const whereClause = createWhereClause([
		createCondition('defaultRole', 'in', undefined, roles)
	])

	return await getRecordsWithWhere(
		'user',
		'readUser',
		mockAuthData,
		whereClause
	)
}

/**
 * EXAMPLE 6: Date range queries using BETWEEN
 * Find users created within a date range
 */
export async function getUsersCreatedBetween(startDate: Date, endDate: Date) {
	const whereClause = createWhereClause([
		createCondition('createdAt', 'between', undefined, [startDate, endDate])
	])

	return await getRecordsWithWhere(
		'user',
		'readUser',
		mockAuthData,
		whereClause
	)
}

/**
 * EXAMPLE 7: Greater than / Less than comparisons
 * Find recent appointments (created in last 7 days)
 */
export async function getRecentAppointments() {
	const sevenDaysAgo = new Date()
	sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

	const whereClause = createWhereClause([
		createCondition('createdAt', 'gte', sevenDaysAgo)
	])

	return await getRecordsWithWhere(
		'appointment',
		'readAppointment',
		mockAuthData,
		whereClause
	)
}

/**
 * EXAMPLE 8: NULL checks
 * Find users without profile photos
 */
export async function getUsersWithoutProfilePhoto() {
	const whereClause = createWhereClause([
		createCondition('profilePhotoUrl', 'isNull')
	])

	return await getRecordsWithWhere(
		'user',
		'readUser',
		mockAuthData,
		whereClause
	)
}

/**
 * EXAMPLE 9: Complex query with multiple conditions
 * Find active team members with verified emails in specific locations
 */
export async function getActiveVerifiedMembersInLocations(locationIds: string[]) {
	const whereClause = createWhereClause([
		createCondition('disabled', 'eq', false),
		createCondition('emailVerified', 'eq', true),
		createCondition('teamLocationId', 'in', undefined, locationIds)
	], 'AND')

	return await getRecordsWithWhere(
		'member',
		'readMember',
		mockAuthData,
		whereClause
	)
}

/**
 * EXAMPLE 10: NOT IN operator
 * Find users excluding specific IDs
 */
export async function getUsersExcluding(excludeIds: string[]) {
	const whereClause = createWhereClause([
		createCondition('id', 'notIn', undefined, excludeIds)
	])

	return await getRecordsWithWhere(
		'user',
		'readUser',
		mockAuthData,
		whereClause
	)
}

/**
 * EXAMPLE 11: Building conditions programmatically
 * Dynamic search with optional filters
 */
export async function searchUsersWithFilters(options: {
	searchTerm?: string
	teamId?: string
	isActive?: boolean
	roles?: string[]
	hasEmail?: boolean
}) {
	const conditions: WhereCondition[] = []

	// Add search term if provided
	if (options.searchTerm) {
		conditions.push(createCondition('displayName', 'ilike', `%${options.searchTerm}%`))
	}

	// Add team filter if provided
	if (options.teamId) {
		conditions.push(createCondition('teamId', 'eq', options.teamId))
	}

	// Add active status filter if provided
	if (options.isActive !== undefined) {
		conditions.push(createCondition('disabled', 'eq', !options.isActive))
	}

	// Add roles filter if provided
	if (options.roles && options.roles.length > 0) {
		conditions.push(createCondition('defaultRole', 'in', undefined, options.roles))
	}

	// Add email existence filter if provided
	if (options.hasEmail !== undefined) {
		conditions.push(createCondition('email', options.hasEmail ? 'isNotNull' : 'isNull'))
	}

	// If no conditions, return all users
	if (conditions.length === 0) {
		return await getRecordsWithWhere('user', 'readUser', mockAuthData)
	}

	const whereClause = createWhereClause(conditions, 'AND')
	return await getRecordsWithWhere('user', 'readUser', mockAuthData, whereClause)
}

/**
 * EXAMPLE 12: Raw where clause construction
 * For when you need maximum flexibility
 */
export async function getCustomQuery() {
	const whereClause: WhereClause = {
		conditions: [
			{ field: 'createdAt', operator: 'gte', value: new Date('2024-01-01') },
			{ field: 'disabled', operator: 'eq', value: false },
			{ field: 'defaultRole', operator: 'in', values: ['team-admin', 'team-member'] }
		],
		logic: 'AND'
	}

	return await getRecordsWithWhere('user', 'readUser', mockAuthData, whereClause)
}
