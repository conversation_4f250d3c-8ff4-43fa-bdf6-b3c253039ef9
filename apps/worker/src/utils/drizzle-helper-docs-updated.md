# Drizzle Helper - Simplified Database Operations Documentation

## Overview

The `drizzle-helper.ts` provides a comprehensive set of functions for database operations using Drizzle ORM with dynamic where clauses. **RBAC permission checks have been removed** - this helper focuses purely on database operations, allowing you to handle permissions at a higher level in your application.

## Key Features

- **CRUD Operations**: Create, Read, Update, Delete with type safety
- **Dynamic Filtering**: Complex where clauses with multiple operators
- **Bulk Operations**: Create, update, and delete multiple records
- **Utility Functions**: Count, exists checks, upsert operations
- **Type Safety**: Full TypeScript support with generic types
- **Linq Extensions**: Leverages linq-extensions for array operations
- **No RBAC**: Pure database operations without permission checks

## Core CRUD Functions

### `createRecord<T>(tableName, data)`
Creates a new record in the specified table.
- **Parameters**: `tableName` (string), `data` (Partial<T>)
- **Returns**: `{ success: boolean, data?: T, error?: string }`

### `getRecords<T>(tableName)`
Gets all records from the specified table.
- **Parameters**: `tableName` (string)
- **Returns**: `{ success: boolean, data?: T[], error?: string }`

### `getRecordsWithWhere<T>(tableName, whereClause?)`
Gets filtered records using dynamic where clauses.
- **Parameters**: `tableName` (string), `whereClause?` (WhereClause)
- **Returns**: `{ success: boolean, data?: T[], error?: string }`

### `getRecordById<T>(tableName, id)`
Gets a single record by ID.
- **Parameters**: `tableName` (string), `id` (string)
- **Returns**: `{ success: boolean, data?: T, error?: string }`

### `getRecordsByTeamId<T>(tableName, teamId)`
Gets records filtered by teamId (common pattern).
- **Parameters**: `tableName` (string), `teamId` (string)
- **Returns**: `{ success: boolean, data?: T[], error?: string }`

### `updateRecord<T>(tableName, data)`
Updates an existing record by ID.
- **Parameters**: `tableName` (string), `data` (Partial<T> & { id: string })
- **Returns**: `{ success: boolean, data?: T, error?: string }`

### `deleteRecord<T>(tableName, data)`
Deletes a record by ID.
- **Parameters**: `tableName` (string), `data` ({ id: string })
- **Returns**: `{ success: boolean, data?: T, error?: string }`

## Bulk Operations

### `createMultipleRecords<T>(tableName, dataArray)`
Creates multiple records in a single operation.
- **Parameters**: `tableName` (string), `dataArray` (Partial<T>[])
- **Returns**: `{ success: boolean, data?: T[], error?: string }`

### `updateRecordsWhere<T>(tableName, data, whereClause)`
Updates multiple records matching the where clause.
- **Parameters**: `tableName` (string), `data` (Partial<T>), `whereClause` (WhereClause)
- **Returns**: `{ success: boolean, data?: T[], error?: string }`

### `deleteRecordsWhere<T>(tableName, whereClause)`
Deletes multiple records matching the where clause.
- **Parameters**: `tableName` (string), `whereClause` (WhereClause)
- **Returns**: `{ success: boolean, data?: T[], error?: string }`

## Utility Functions

### `countRecords(tableName, whereClause?)`
Counts records with optional filtering.
- **Parameters**: `tableName` (string), `whereClause?` (WhereClause)
- **Returns**: `{ success: boolean, data?: number, error?: string }`

### `recordExists(tableName, id)`
Checks if a record exists by ID.
- **Parameters**: `tableName` (string), `id` (string)
- **Returns**: `{ success: boolean, data?: boolean, error?: string }`

### `upsertRecord<T>(tableName, data)`
Inserts or updates a record (insert if no ID, update if ID exists).
- **Parameters**: `tableName` (string), `data` (Partial<T>)
- **Returns**: `{ success: boolean, data?: T, error?: string }`

## Where Clause Operators

| Operator | Description | Example |
|----------|-------------|---------|
| `eq` | Equal to | `createCondition('status', 'eq', 'active')` |
| `ne` | Not equal to | `createCondition('disabled', 'ne', true)` |
| `gt` | Greater than | `createCondition('age', 'gt', 18)` |
| `gte` | Greater than or equal | `createCondition('createdAt', 'gte', date)` |
| `lt` | Less than | `createCondition('score', 'lt', 100)` |
| `lte` | Less than or equal | `createCondition('updatedAt', 'lte', date)` |
| `like` | Contains (case-sensitive) | `createCondition('name', 'like', 'John')` |
| `ilike` | Contains (case-insensitive) | `createCondition('email', 'ilike', '@gmail.com')` |
| `in` | Value in array | `createCondition('role', 'in', undefined, ['admin', 'user'])` |
| `notIn` | Value not in array | `createCondition('id', 'notIn', undefined, excludeIds)` |
| `isNull` | Field is null/undefined | `createCondition('deletedAt', 'isNull')` |
| `isNotNull` | Field is not null/undefined | `createCondition('email', 'isNotNull')` |
| `between` | Value between two values | `createCondition('age', 'between', undefined, [18, 65])` |

## Usage Examples

### Basic CRUD Operations

```typescript
// Create
const result = await createRecord<User>('member', {
  email: '<EMAIL>',
  displayName: 'John Doe',
  teamId: 'team-123'
})

// Read all
const users = await getRecords<User>('member')

// Read by ID
const user = await getRecordById<User>('member', 'user-123')

// Update
await updateRecord<User>('member', {
  id: 'user-123',
  displayName: 'John Smith'
})

// Delete
await deleteRecord<User>('member', { id: 'user-123' })
```

### Dynamic Filtering

```typescript
// Single condition
const whereClause = createWhereClause([
  createCondition('disabled', 'eq', false)
])

// Multiple AND conditions
const whereClause = createWhereClause([
  createCondition('teamId', 'eq', 'team-123'),
  createCondition('emailVerified', 'eq', true),
  createCondition('disabled', 'eq', false)
], 'AND')

// Multiple OR conditions
const whereClause = createWhereClause([
  createCondition('role', 'eq', 'admin'),
  createCondition('role', 'eq', 'moderator')
], 'OR')

const users = await getRecordsWithWhere<User>('member', whereClause)
```

### Bulk Operations

```typescript
// Bulk create
const users = [
  { email: '<EMAIL>', displayName: 'User 1' },
  { email: '<EMAIL>', displayName: 'User 2' }
]
const result = await createMultipleRecords<User>('member', users)

// Bulk update
const whereClause = createWhereClause([
  createCondition('teamId', 'eq', 'team-123')
])
await updateRecordsWhere<User>('member', { disabled: true }, whereClause)

// Bulk delete
await deleteRecordsWhere<User>('member', whereClause)
```

### Utility Operations

```typescript
// Count records
const count = await countRecords('member', whereClause)

// Check existence
const exists = await recordExists('member', 'user-123')

// Upsert
const result = await upsertRecord<User>('member', {
  id: 'user-123', // Will update if exists, create if not
  email: '<EMAIL>',
  displayName: 'John Doe'
})
```

## Error Handling

All functions return a consistent result format:

```typescript
{
  success: boolean
  data?: T | T[] | number | boolean
  error?: string
}
```

Always check the `success` field before using `data`:

```typescript
const result = await getRecords<User>('member')
if (result.success) {
  console.log('Users:', result.data)
} else {
  console.error('Error:', result.error)
}
```

## Performance Considerations

- **Indexes**: Ensure proper database indexes for frequently queried fields
- **Bulk Operations**: Use bulk functions for multiple records instead of loops
- **Where Clauses**: Specific conditions perform better than broad searches
- **Pagination**: Consider implementing pagination for large datasets

## Migration from RBAC Version

If migrating from the RBAC version, remove the `rbacAction` and `authData` parameters:

```typescript
// Old (with RBAC)
await getRecords<User>('member', ActionTypes.readMembers, authData)

// New (without RBAC)
await getRecords<User>('member')
```

Handle permissions at a higher level in your application logic or middleware.
