/**
 * Examples demonstrating how to use the state-helper functions
 * with actor context and dynamic where clauses
 */

import {
	createStateRecord,
	updateStateRecord,
	deleteStateRecord,
	getStateRecords,
	getStateRecordsWithWhere,
	getStateRecordById,
	getStateRecordsByTeamId,
	searchStateRecords,
	createMultipleStateRecords,
	countStateRecords,
	stateRecordExists,
	getFirstStateRecord,
	getLastStateRecord,
	selectStateRecordFields,
	groupStateRecords,
	orderStateRecords,
	takeStateRecords,
	paginateStateRecords,
	getDistinctStateValues,
	aggregateStateRecords,
	createWhereClause,
	createCondition,
	type ActorContext,
	type WhereClause
} from './state-helper'
import { ActionTypes } from '@repo/common'

// Example interfaces
interface User {
	id: string
	email: string
	displayName: string
	teamId: string
	disabled: boolean
	emailVerified: boolean
	defaultRole: string
	createdAt: Date
	updatedAt: Date
}

interface TeamMember {
	id: string
	userId: string
	teamId: string
	teamLocationId: string
	role: string
	status: string
	disabled: boolean
	createdAt: Date
	updatedAt: Date
}

/**
 * EXAMPLE 1: Create a new user in state
 */
export async function createUser(c: ActorContext, userData: Partial<User>) {
	const result = await createStateRecord<User>(
		c,
		userData,
		ActionTypes.manageMembers
	)

	if (result.success) {
		c.log.info(`User created successfully: ${result.data?.email}`)
		// The function automatically broadcasts:
		// - ActorEvents.ItemCreated
		// - PostgresEvents.ItemCreated
	} else {
		c.log.error(`Failed to create user: ${result.error}`)
	}

	return result
}

/**
 * EXAMPLE 2: Update user information
 */
export async function updateUser(c: ActorContext, userId: string, updates: Partial<User>) {
	const result = await updateStateRecord<User>(
		c,
		{ ...updates, id: userId },
		ActionTypes.manageMembers
	)

	if (result.success) {
		c.log.info(`User updated successfully: ${userId}`)
		// The function automatically broadcasts:
		// - ActorEvents.ItemUpdated
		// - PostgresEvents.ItemUpdated
	}

	return result
}

/**
 * EXAMPLE 3: Delete a user
 */
export async function deleteUser(c: ActorContext, userId: string) {
	const result = await deleteStateRecord<User>(
		c,
		userId,
		ActionTypes.manageMembers
	)

	if (result.success) {
		c.log.info(`User deleted successfully: ${userId}`)
		// The function automatically broadcasts:
		// - ActorEvents.ItemDeleted
		// - PostgresEvents.ItemDeleted
	}

	return result
}

/**
 * EXAMPLE 4: Get all users (no filtering)
 */
export async function getAllUsers(c: ActorContext) {
	return await getStateRecords<User>(c, ActionTypes.readmembers)
}

/**
 * EXAMPLE 5: Get user by ID
 */
export async function getUserById(c: ActorContext, userId: string) {
	return await getStateRecordById<User>(c, userId, ActionTypes.readmembers)
}

/**
 * EXAMPLE 6: Get users by team ID
 */
export async function getUsersByTeam(c: ActorContext, teamId: string) {
	return await getStateRecordsByTeamId<User>(c, teamId, ActionTypes.readmembers)
}

/**
 * EXAMPLE 7: Search users by display name
 */
export async function searchUsersByName(c: ActorContext, searchTerm: string) {
	return await searchStateRecords<User>(
		c,
		'displayName',
		searchTerm,
		ActionTypes.readmembers
	)
}

/**
 * EXAMPLE 8: Get active users with verified emails
 */
export async function getActiveVerifiedUsers(c: ActorContext) {
	const whereClause = createWhereClause([
		createCondition('disabled', 'eq', false),
		createCondition('emailVerified', 'eq', true)
	], 'AND')

	return await getStateRecordsWithWhere<User>(
		c,
		ActionTypes.readmembers,
		whereClause
	)
}

/**
 * EXAMPLE 9: Get users with specific roles
 */
export async function getUsersByRoles(c: ActorContext, roles: string[]) {
	const whereClause = createWhereClause([
		createCondition('defaultRole', 'in', undefined, roles)
	])

	return await getStateRecordsWithWhere<User>(
		c,
		ActionTypes.readmembers,
		whereClause
	)
}

/**
 * EXAMPLE 10: Get users created in the last 30 days
 */
export async function getRecentUsers(c: ActorContext) {
	const thirtyDaysAgo = new Date()
	thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

	const whereClause = createWhereClause([
		createCondition('createdAt', 'gte', thirtyDaysAgo)
	])

	return await getStateRecordsWithWhere<User>(
		c,
		ActionTypes.readmembers,
		whereClause
	)
}

/**
 * EXAMPLE 11: Complex query - Active team admins with verified emails
 */
export async function getActiveTeamAdmins(c: ActorContext, teamId: string) {
	const whereClause = createWhereClause([
		createCondition('teamId', 'eq', teamId),
		createCondition('disabled', 'eq', false),
		createCondition('emailVerified', 'eq', true),
		createCondition('defaultRole', 'eq', 'team-admin')
	], 'AND')

	return await getStateRecordsWithWhere<User>(
		c,
		ActionTypes.readmembers,
		whereClause
	)
}

/**
 * EXAMPLE 12: OR condition - Users with either verified email OR phone
 */
export async function getVerifiedUsers(c: ActorContext) {
	const whereClause = createWhereClause([
		createCondition('emailVerified', 'eq', true),
		createCondition('phoneNumberVerified', 'eq', true)
	], 'OR')

	return await getStateRecordsWithWhere<User>(
		c,
		ActionTypes.readmembers,
		whereClause
	)
}

/**
 * EXAMPLE 13: Bulk create multiple users
 */
export async function createMultipleUsers(c: ActorContext, usersData: Partial<User>[]) {
	const result = await createMultipleStateRecords<User>(
		c,
		usersData,
		ActionTypes.manageMembers
	)

	if (result.success) {
		c.log.info(`Created ${result.data?.length} users successfully`)
		// The function automatically broadcasts:
		// - ActorEvents.ItemCreated for each user
		// - PostgresEvents.ItemCreated for each user
		// - PostgresEvents.CollectionUpdated for bulk operation
	}

	return result
}

/**
 * EXAMPLE 14: Dynamic filtering based on user input
 */
export async function filterUsers(c: ActorContext, filters: {
	teamId?: string
	isActive?: boolean
	role?: string
	searchTerm?: string
	emailVerified?: boolean
}) {
	const conditions: any[] = []

	// Add filters dynamically
	if (filters.teamId) {
		conditions.push(createCondition('teamId', 'eq', filters.teamId))
	}

	if (filters.isActive !== undefined) {
		conditions.push(createCondition('disabled', 'eq', !filters.isActive))
	}

	if (filters.role) {
		conditions.push(createCondition('defaultRole', 'eq', filters.role))
	}

	if (filters.searchTerm) {
		conditions.push(createCondition('displayName', 'ilike', `%${filters.searchTerm}%`))
	}

	if (filters.emailVerified !== undefined) {
		conditions.push(createCondition('emailVerified', 'eq', filters.emailVerified))
	}

	// If no conditions, return all users
	if (conditions.length === 0) {
		return await getStateRecords<User>(c, ActionTypes.readmembers)
	}

	const whereClause = createWhereClause(conditions, 'AND')
	return await getStateRecordsWithWhere<User>(c, ActionTypes.readmembers, whereClause)
}

/**
 * EXAMPLE 15: Working with team members
 */
export async function manageTeamMember(c: ActorContext) {
	// Create a team member
	const newMember = await createStateRecord<TeamMember>(c, {
		userId: 'user-123',
		teamId: 'team-456',
		teamLocationId: 'location-789',
		role: 'team-member',
		status: 'Active',
		disabled: false
	}, ActionTypes.manageMembers)

	if (newMember.success) {
		// Update the member's role
		const updatedMember = await updateStateRecord<TeamMember>(c, {
			id: newMember.data!.id,
			role: 'team-admin'
		}, ActionTypes.manageMembers)

		// Get all active members in the team
		const activeMembers = await getStateRecordsWithWhere<TeamMember>(c, ActionTypes.readmembers,
			createWhereClause([
				createCondition('teamId', 'eq', 'team-456'),
				createCondition('disabled', 'eq', false)
			])
		)

		return { newMember, updatedMember, activeMembers }
	}

	return { error: newMember.error }
}

/**
 * EXAMPLE 16: Pagination-like functionality using state
 */
export async function getPaginatedUsers(c: ActorContext, page: number = 1, limit: number = 10) {
	const allUsers = await getStateRecords<User>(c, ActionTypes.readmembers)

	if (!allUsers.success) {
		return allUsers
	}

	const startIndex = (page - 1) * limit
	const endIndex = startIndex + limit
	const paginatedData = allUsers.data!.slice(startIndex, endIndex)

	return {
		success: true,
		data: paginatedData,
		pagination: {
			page,
			limit,
			total: allUsers.data!.length,
			totalPages: Math.ceil(allUsers.data!.length / limit)
		}
	}
}

/**
 * LINQ-EXTENSIONS EXAMPLES
 * Demonstrating advanced linq-extensions functionality
 */

/**
 * EXAMPLE 17: Count records with filtering using linq-extensions
 */
export async function countActiveUsers(c: ActorContext) {
	const whereClause = createWhereClause([
		createCondition('disabled', 'eq', false)
	])

	return await countStateRecords<User>(c, ActionTypes.readmembers, whereClause)
}

/**
 * EXAMPLE 18: Check if records exist using linq-extensions
 */
export async function checkIfAdminExists(c: ActorContext, teamId: string) {
	const whereClause = createWhereClause([
		createCondition('teamId', 'eq', teamId),
		createCondition('defaultRole', 'eq', 'team-admin'),
		createCondition('disabled', 'eq', false)
	])

	return await stateRecordExists<User>(c, ActionTypes.readmembers, whereClause)
}

/**
 * EXAMPLE 19: Get first record matching criteria using linq-extensions
 */
export async function getFirstActiveUser(c: ActorContext, teamId: string) {
	const whereClause = createWhereClause([
		createCondition('teamId', 'eq', teamId),
		createCondition('disabled', 'eq', false)
	])

	return await getFirstStateRecord<User>(c, ActionTypes.readmembers, whereClause)
}

/**
 * EXAMPLE 20: Get last record using linq-extensions
 */
export async function getLastCreatedUser(c: ActorContext) {
	return await getLastStateRecord<User>(c, ActionTypes.readmembers)
}

/**
 * EXAMPLE 21: Select specific fields using linq-extensions
 */
export async function getUserEmailsAndNames(c: ActorContext, teamId: string) {
	const whereClause = createWhereClause([
		createCondition('teamId', 'eq', teamId),
		createCondition('disabled', 'eq', false)
	])

	return await selectStateRecordFields<User, { email: string; displayName: string }>(
		c,
		ActionTypes.readmembers,
		user => ({ email: user.email, displayName: user.displayName }),
		whereClause
	)
}

/**
 * EXAMPLE 22: Group records by role using linq-extensions
 */
export async function getUsersByRole(c: ActorContext, teamId: string) {
	const whereClause = createWhereClause([
		createCondition('teamId', 'eq', teamId),
		createCondition('disabled', 'eq', false)
	])

	return await groupStateRecords<User, string>(
		c,
		ActionTypes.readmembers,
		user => user.defaultRole,
		whereClause
	)
}

/**
 * EXAMPLE 23: Order records using linq-extensions
 */
export async function getUsersOrderedByName(c: ActorContext, teamId: string, descending: boolean = false) {
	const whereClause = createWhereClause([
		createCondition('teamId', 'eq', teamId),
		createCondition('disabled', 'eq', false)
	])

	return await orderStateRecords<User, string>(
		c,
		ActionTypes.readmembers,
		user => user.displayName,
		descending,
		whereClause
	)
}

/**
 * EXAMPLE 24: Take specific number of records using linq-extensions
 */
export async function getTopUsers(c: ActorContext, count: number) {
	const whereClause = createWhereClause([
		createCondition('disabled', 'eq', false)
	])

	return await takeStateRecords<User>(c, ActionTypes.readmembers, count, whereClause)
}

/**
 * EXAMPLE 25: Paginate records using linq-extensions
 */
export async function getPaginatedActiveUsers(c: ActorContext, page: number, pageSize: number) {
	const whereClause = createWhereClause([
		createCondition('disabled', 'eq', false)
	])

	const skip = (page - 1) * pageSize
	return await paginateStateRecords<User>(c, ActionTypes.readmembers, skip, pageSize, whereClause)
}

/**
 * EXAMPLE 26: Get distinct values using linq-extensions
 */
export async function getDistinctRoles(c: ActorContext, teamId: string) {
	const whereClause = createWhereClause([
		createCondition('teamId', 'eq', teamId),
		createCondition('disabled', 'eq', false)
	])

	return await getDistinctStateValues<User, string>(
		c,
		ActionTypes.readmembers,
		user => user.defaultRole,
		whereClause
	)
}

/**
 * EXAMPLE 27: Aggregate records using linq-extensions
 */
export async function getUserStatistics(c: ActorContext, teamId: string) {
	const whereClause = createWhereClause([
		createCondition('teamId', 'eq', teamId)
	])

	return await aggregateStateRecords<User>(c, ActionTypes.readmembers, whereClause)
}
