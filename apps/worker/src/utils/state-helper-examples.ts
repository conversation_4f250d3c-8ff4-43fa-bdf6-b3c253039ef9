/**
 * Examples demonstrating how to use the state-helper functions
 * with actor context, dynamic where clauses, and uniqueness validation
 *
 * Updated to use the new stateHelper object export and c.vars for uniqueness configuration
 */

import { stateHelper } from './state-helper'
import { ActionTypes } from '@repo/common'
import { actor } from 'actor-core'
import type { ActorActionContext } from './server.helpers'

// Example interfaces
interface User {
	id: string
	email: string
	displayName: string
	teamId: string
	disabled: boolean
	emailVerified: boolean
	defaultRole: string
	createdAt: Date
	updatedAt: Date
}

interface TeamMember {
	id: string
	userId: string
	teamId: string
	teamLocationId: string
	role: string
	status: string
	disabled: boolean
	createdAt: Date
	updatedAt: Date
}

/**
 * EXAMPLE 1: Create a new user in state
 */
export const createUser = async (c: ActorActionContext, userData: Partial<User>) => {
	const result = await stateHelper.createStateRecord<User>(
		c,
		userData,
		ActionTypes.manageMembers
	)

	if (result.success) {
		c.log.info(`User created successfully: ${result.data?.email}`)
		// The function automatically broadcasts:
		// - ActorEvents.ItemCreated
		// - PostgresEvents.ItemCreated
	} else {
		c.log.error(`Failed to create user: ${result.error}`)
	}

	return result
}

/**
 * EXAMPLE 2: Update user information
 */
export const updateUser = async (c: ActorActionContext, userId: string, updates: Partial<User>) => {
	const result = await stateHelper.updateStateRecord<User>(
		c,
		{ ...updates, id: userId },
		ActionTypes.manageMembers
	)

	if (result.success) {
		c.log.info(`User updated successfully: ${userId}`)
		// The function automatically broadcasts:
		// - ActorEvents.ItemUpdated
		// - PostgresEvents.ItemUpdated
	}

	return result
}

/**
 * EXAMPLE 3: Delete a user
 */
export const deleteUser = async (c: ActorActionContext, userId: string) => {
	const result = await stateHelper.deleteStateRecord<User>(
		c,
		userId,
		ActionTypes.manageMembers
	)

	if (result.success) {
		c.log.info(`User deleted successfully: ${userId}`)
		// The function automatically broadcasts:
		// - ActorEvents.ItemDeleted
		// - PostgresEvents.ItemDeleted
	}

	return result
}

/**
 * EXAMPLE 4: Get all users (no filtering)
 */
export const getAllUsers = async (c: ActorActionContext) => {
	return await stateHelper.getStateRecords<User>(c, ActionTypes.readmembers)
}

/**
 * EXAMPLE 5: Get user by ID
 */
export const getUserById = async (c: ActorActionContext, userId: string) => {
	return await stateHelper.getStateRecordById<User>(c, userId, ActionTypes.readmembers)
}

/**
 * EXAMPLE 6: Get users by team ID
 */
export const getUsersByTeam = async (c: ActorActionContext, teamId: string) => {
	return await stateHelper.getStateRecordsByTeamId<User>(c, teamId, ActionTypes.readmembers)
}

/**
 * EXAMPLE 7: Search users by display name
 */
export const searchUsersByName = async (c: ActorActionContext, searchTerm: string) => {
	return await stateHelper.searchStateRecords<User>(
		c,
		'displayName',
		searchTerm,
		ActionTypes.readmembers
	)
}

/**
 * EXAMPLE 8: Get active users with verified emails
 */
export const getActiveVerifiedUsers = async (c: ActorActionContext) => {
	const whereClause = stateHelper.createWhereClause([
		stateHelper.createCondition('disabled', 'eq', false),
		stateHelper.createCondition('emailVerified', 'eq', true)
	], 'AND')

	return await stateHelper.getStateRecordsWithWhere<User>(
		c,
		ActionTypes.readmembers,
		whereClause
	)
}

/**
 * EXAMPLE 9: Get users with specific roles
 */
export const getUsersByRoles = async (c: ActorActionContext, roles: string[]) => {
	const whereClause = stateHelper.createWhereClause([
		stateHelper.createCondition('defaultRole', 'in', undefined, roles)
	])

	return await stateHelper.getStateRecordsWithWhere<User>(
		c,
		ActionTypes.readmembers,
		whereClause
	)
}

/**
 * EXAMPLE 10: Get users created in the last 30 days
 */
export const getRecentUsers = async (c: ActorActionContext) => {
	const thirtyDaysAgo = new Date()
	thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

	const whereClause = stateHelper.createWhereClause([
		stateHelper.createCondition('createdAt', 'gte', thirtyDaysAgo)
	])

	return await stateHelper.getStateRecordsWithWhere<User>(
		c,
		ActionTypes.readmembers,
		whereClause
	)
}

/**
 * EXAMPLE 11: Complex query - Active team admins with verified emails
 */
export const getActiveTeamAdmins = async (c: ActorActionContext, teamId: string) => {
	const whereClause = stateHelper.createWhereClause([
		stateHelper.createCondition('teamId', 'eq', teamId),
		stateHelper.createCondition('disabled', 'eq', false),
		stateHelper.createCondition('emailVerified', 'eq', true),
		stateHelper.createCondition('defaultRole', 'eq', 'team-admin')
	], 'AND')

	return await stateHelper.getStateRecordsWithWhere<User>(
		c,
		ActionTypes.readmembers,
		whereClause
	)
}

/**
 * EXAMPLE 12: OR condition - Users with either verified email OR phone
 */
export const getVerifiedUsers = async (c: ActorActionContext) => {
	const whereClause = stateHelper.createWhereClause([
		stateHelper.createCondition('emailVerified', 'eq', true),
		stateHelper.createCondition('phoneNumberVerified', 'eq', true)
	], 'OR')

	return await stateHelper.getStateRecordsWithWhere<User>(
		c,
		ActionTypes.readmembers,
		whereClause
	)
}

/**
 * EXAMPLE 13: Bulk create multiple users
 */
export const createMultipleUsers = async (c: ActorActionContext, usersData: Partial<User>[]) => {
	const result = await stateHelper.createMultipleStateRecords<User>(
		c,
		usersData,
		ActionTypes.manageMembers
	)

	if (result.success) {
		c.log.info(`Created ${result.data?.length} users successfully`)
		// The function automatically broadcasts:
		// - ActorEvents.ItemCreated for each user
		// - PostgresEvents.ItemCreated for each user
		// - PostgresEvents.CollectionUpdated for bulk operation
	}

	return result
}

/**
 * EXAMPLE 14: Dynamic filtering based on user input
 */
export const filterUsers = async (c: ActorActionContext, filters: {
	teamId?: string
	isActive?: boolean
	role?: string
	searchTerm?: string
	emailVerified?: boolean
}) => {
	const conditions: any[] = []

	// Add filters dynamically
	if (filters.teamId) {
		conditions.push(stateHelper.createCondition('teamId', 'eq', filters.teamId))
	}

	if (filters.isActive !== undefined) {
		conditions.push(stateHelper.createCondition('disabled', 'eq', !filters.isActive))
	}

	if (filters.role) {
		conditions.push(stateHelper.createCondition('defaultRole', 'eq', filters.role))
	}

	if (filters.searchTerm) {
		conditions.push(stateHelper.createCondition('displayName', 'ilike', `%${filters.searchTerm}%`))
	}

	if (filters.emailVerified !== undefined) {
		conditions.push(stateHelper.createCondition('emailVerified', 'eq', filters.emailVerified))
	}

	// If no conditions, return all users
	if (conditions.length === 0) {
		return await stateHelper.getStateRecords<User>(c, ActionTypes.readmembers)
	}

	const whereClause = stateHelper.createWhereClause(conditions, 'AND')
	return await stateHelper.getStateRecordsWithWhere<User>(c, ActionTypes.readmembers, whereClause)
}

/**
 * EXAMPLE 15: Working with team members
 */
export const manageTeamMember = async (c: ActorActionContext) => {
	// Create a team member
	const newMember = await stateHelper.createStateRecord<TeamMember>(c, {
		userId: 'user-123',
		teamId: 'team-456',
		teamLocationId: 'location-789',
		role: 'team-member',
		status: 'Active',
		disabled: false
	}, ActionTypes.manageMembers)

	if (newMember.success) {
		// Update the member's role
		const updatedMember = await stateHelper.updateStateRecord<TeamMember>(c, {
			id: newMember.data!.id,
			role: 'team-admin'
		}, ActionTypes.manageMembers)

		// Get all active members in the team
		const activeMembers = await stateHelper.getStateRecordsWithWhere<TeamMember>(c, ActionTypes.readmembers,
			stateHelper.createWhereClause([
				stateHelper.createCondition('teamId', 'eq', 'team-456'),
				stateHelper.createCondition('disabled', 'eq', false)
			])
		)

		return { newMember, updatedMember, activeMembers }
	}

	return { error: newMember.error }
}

/**
 * EXAMPLE 16: Pagination-like functionality using state
 */
export const getPaginatedUsers = async (c: ActorActionContext, page: number = 1, limit: number = 10) => {
	const allUsers = await stateHelper.getStateRecords<User>(c, ActionTypes.readmembers)

	if (!allUsers.success) {
		return allUsers
	}

	const startIndex = (page - 1) * limit
	const endIndex = startIndex + limit
	const paginatedData = allUsers.data!.slice(startIndex, endIndex)

	return {
		success: true,
		data: paginatedData,
		pagination: {
			page,
			limit,
			total: allUsers.data!.length,
			totalPages: Math.ceil(allUsers.data!.length / limit)
		}
	}
}

/**
 * LINQ-EXTENSIONS EXAMPLES
 * Demonstrating advanced linq-extensions functionality
 */

/**
 * EXAMPLE 17: Count records with filtering using linq-extensions
 */
export const countActiveUsers = async (c: ActorActionContext) => {
	const whereClause = stateHelper.createWhereClause([
		stateHelper.createCondition('disabled', 'eq', false)
	])

	return await stateHelper.countStateRecords<User>(c, ActionTypes.readmembers, whereClause)
}

/**
 * EXAMPLE 18: Check if records exist using linq-extensions
 */
export const checkIfAdminExists = async (c: ActorActionContext, teamId: string) => {
	const whereClause = stateHelper.createWhereClause([
		stateHelper.createCondition('teamId', 'eq', teamId),
		stateHelper.createCondition('defaultRole', 'eq', 'team-admin'),
		stateHelper.createCondition('disabled', 'eq', false)
	])

	return await stateHelper.stateRecordExists<User>(c, ActionTypes.readmembers, whereClause)
}

/**
 * EXAMPLE 19: Get first record matching criteria using linq-extensions
 */
export const getFirstActiveUser = async (c: ActorActionContext, teamId: string) => {
	const whereClause = stateHelper.createWhereClause([
		stateHelper.createCondition('teamId', 'eq', teamId),
		stateHelper.createCondition('disabled', 'eq', false)
	])

	return await stateHelper.getFirstStateRecord<User>(c, ActionTypes.readmembers, whereClause)
}

/**
 * EXAMPLE 20: Get last record using linq-extensions
 */
export const getLastCreatedUser = async (c: ActorActionContext) => {
	return await stateHelper.getLastStateRecord<User>(c, ActionTypes.readmembers)
}

/**
 * EXAMPLE 21: Select specific fields using linq-extensions
 */
export const getUserEmailsAndNames = async (c: ActorActionContext, teamId: string) => {
	const whereClause = stateHelper.createWhereClause([
		stateHelper.createCondition('teamId', 'eq', teamId),
		stateHelper.createCondition('disabled', 'eq', false)
	])

	return await stateHelper.selectStateRecordFields<User, { email: string; displayName: string }>(
		c,
		ActionTypes.readmembers,
		(user: User) => ({ email: user.email, displayName: user.displayName }),
		whereClause
	)
}

/**
 * EXAMPLE 22: Group records by role using linq-extensions
 */
export const getUsersByRole = async (c: ActorActionContext, teamId: string) => {
	const whereClause = stateHelper.createWhereClause([
		stateHelper.createCondition('teamId', 'eq', teamId),
		stateHelper.createCondition('disabled', 'eq', false)
	])

	return await stateHelper.groupStateRecords<User, string>(
		c,
		ActionTypes.readmembers,
		(user: User) => user.defaultRole,
		whereClause
	)
}

/**
 * EXAMPLE 23: Order records using linq-extensions
 */
export const getUsersOrderedByName = async (c: ActorActionContext, teamId: string, descending: boolean = false) => {
	const whereClause = stateHelper.createWhereClause([
		stateHelper.createCondition('teamId', 'eq', teamId),
		stateHelper.createCondition('disabled', 'eq', false)
	])

	return await stateHelper.orderStateRecords<User, string>(
		c,
		ActionTypes.readmembers,
		(user: User) => user.displayName,
		descending,
		whereClause
	)
}

/**
 * EXAMPLE 24: Take specific number of records using linq-extensions
 */
export const getTopUsers = async (c: ActorActionContext, count: number) => {
	const whereClause = stateHelper.createWhereClause([
		stateHelper.createCondition('disabled', 'eq', false)
	])

	return await stateHelper.takeStateRecords<User>(c, ActionTypes.readmembers, count, whereClause)
}

/**
 * EXAMPLE 25: Paginate records using linq-extensions
 */
export const getPaginatedActiveUsers = async (c: ActorActionContext, page: number, pageSize: number) => {
	const whereClause = stateHelper.createWhereClause([
		stateHelper.createCondition('disabled', 'eq', false)
	])

	const skip = (page - 1) * pageSize
	return await stateHelper.paginateStateRecords<User>(c, ActionTypes.readmembers, skip, pageSize, whereClause)
}

/**
 * EXAMPLE 26: Get distinct values using linq-extensions
 */
export const getDistinctRoles = async (c: ActorActionContext, teamId: string) => {
	const whereClause = stateHelper.createWhereClause([
		stateHelper.createCondition('teamId', 'eq', teamId),
		stateHelper.createCondition('disabled', 'eq', false)
	])

	return await stateHelper.getDistinctStateValues<User, string>(
		c,
		ActionTypes.readmembers,
		(user: User) => user.defaultRole,
		whereClause
	)
}

/**
 * EXAMPLE 27: Aggregate records using linq-extensions
 */
export const getUserStatistics = async (c: ActorActionContext, teamId: string) => {
	const whereClause = stateHelper.createWhereClause([
		stateHelper.createCondition('teamId', 'eq', teamId)
	])

	return await stateHelper.aggregateStateRecords<User>(c, ActionTypes.readmembers, whereClause)
}

/**
 * UNIQUENESS VALIDATION EXAMPLES
 * Demonstrating the new uniqueness validation features using c.vars
 */

/**
 * EXAMPLE 28: Actor with unique email validation
 */
export const userActorWithUniqueEmail = actor({
	state: [] as User[],
	createVars: (_c, driverCtx) => ({
		uniqueFields: ['email'], // Email must be unique across all users
		mastra: undefined, // Replace with actual mastra instance
		ctx: driverCtx
	}),
	actions: {
		create: async (c, userData: Partial<User>) => {
			// Uniqueness validation happens automatically based on c.vars.uniqueFields
			return await stateHelper.createStateRecord(c as any, userData, ActionTypes.manageMembers)
		},
		update: async (c, userData: Partial<User> & { id: string }) => {
			// Uniqueness validation happens automatically (excludes current record)
			return await stateHelper.updateStateRecord(c as any, userData, ActionTypes.manageMembers)
		}
	}
})

/**
 * EXAMPLE 29: Actor with multiple unique fields
 */
export const userActorWithMultipleUniqueFields = actor({
	state: [] as User[],
	createVars: (_c, driverCtx) => ({
		uniqueFields: ['email', 'displayName'], // Both email and displayName must be unique
		mastra: undefined, // Replace with actual mastra instance
		ctx: driverCtx
	}),
	actions: {
		create: async (c, userData: Partial<User>) => {
			return await stateHelper.createStateRecord(c as any, userData, ActionTypes.manageMembers)
		},
		createMultiple: async (c, usersData: Partial<User>[]) => {
			// Validates uniqueness across the batch and existing state
			return await stateHelper.createMultipleStateRecords(c as any, usersData, ActionTypes.manageMembers)
		}
	}
})

/**
 * EXAMPLE 30: Actor with composite unique constraints
 */
interface Project {
	id: string
	name: string
	slug: string
	teamId: string
	organizationId: string
	createdAt: Date
	updatedAt: Date
}

export const projectActorWithCompositeUnique = actor({
	state: [] as Project[],
	createVars: (_c, driverCtx) => ({
		compositeUniqueFields: [
			['teamId', 'slug'],      // teamId + slug must be unique
			['organizationId', 'name'] // organizationId + name must be unique
		],
		mastra: undefined, // Replace with actual mastra instance
		ctx: driverCtx
	}),
	actions: {
		create: async (c, projectData: Partial<Project>) => {
			// Validates both composite constraints automatically
			return await stateHelper.createStateRecord(c as any, projectData, ActionTypes.manageMembers)
		}
	}
})

/**
 * EXAMPLE 31: Actor with both single and composite unique constraints
 */
interface ComplexEntity {
	id: string
	email: string
	apiKey: string
	teamId: string
	slug: string
	organizationId: string
	name: string
	createdAt: Date
	updatedAt: Date
}

export const complexActorWithMixedUnique = actor({
	state: [] as ComplexEntity[],
	createVars: (_c, driverCtx) => ({
		uniqueFields: ['email', 'apiKey'], // Single field uniqueness
		compositeUniqueFields: [
			['teamId', 'slug'],        // Composite uniqueness
			['organizationId', 'name']  // Multiple composite constraints
		],
		mastra: undefined, // Replace with actual mastra instance
		ctx: driverCtx
	}),
	actions: {
		create: async (c, entityData: Partial<ComplexEntity>) => {
			// Validates all uniqueness constraints automatically
			return await stateHelper.createStateRecord(c as any, entityData, ActionTypes.manageMembers)
		},
		update: async (c, entityData: Partial<ComplexEntity> & { id: string }) => {
			// Validates all uniqueness constraints (excluding current record)
			return await stateHelper.updateStateRecord(c as any, entityData, ActionTypes.manageMembers)
		},
		createBatch: async (c, entitiesData: Partial<ComplexEntity>[]) => {
			// Validates uniqueness within batch and against existing state
			return await stateHelper.createMultipleStateRecords(c as any, entitiesData, ActionTypes.manageMembers)
		}
	}
})

/**
 * EXAMPLE 32: Usage examples with error handling
 */
export const demonstrateUniqueValidation = async () => {
	// This would be called within an actor context
	// const result = await userActorWithUniqueEmail.actions.create(c, {
	//   email: '<EMAIL>', // This would fail if email already exists
	//   displayName: 'John Doe'
	// })

	// Error handling example:
	// if (!result.success) {
	//   console.error('Validation failed:', result.error)
	//   // Error message would be: "Unique constraint violation: email '<EMAIL>' already exists"
	// }

	// Composite constraint example:
	// const projectResult = await projectActorWithCompositeUnique.actions.create(c, {
	//   teamId: 'team-1',
	//   slug: 'existing-slug', // This would fail if teamId + slug combination exists
	//   name: 'My Project'
	// })

	// if (!projectResult.success) {
	//   console.error('Composite validation failed:', projectResult.error)
	//   // Error message would be: "Composite unique constraint violation: combination of teamId, slug (team-1, existing-slug) already exists"
	// }
}
