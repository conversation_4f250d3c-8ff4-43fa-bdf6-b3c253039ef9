# Drizzle Helper - Dynamic Where Clauses Documentation

## Overview

The enhanced `drizzle-helper.ts` now supports dynamic where clauses, allowing you to build complex queries programmatically without writing raw SQL. This provides type safety, flexibility, and maintains the existing RBAC security model.

## Core Functions

### `getRecordsWithWhere<T>(tableName, rbacAction, authData, whereClause?)`

Enhanced version of `getRecords` that accepts optional where clauses.

**Parameters:**
- `tableName`: String name of the table (must exist in schema)
- `rbacAction`: RBAC action type for permission checking
- `authData`: Authentication data for the user
- `whereClause`: Optional WhereClause object defining conditions

### Helper Functions

- `createWhereClause(conditions, logic)`: Creates a WhereClause object
- `createCondition(field, operator, value?, values?)`: Creates a WhereCondition object

## Available Operators

| Operator | Description | Value Type | Example |
|----------|-------------|------------|---------|
| `eq` | Equal to | any | `{ field: 'id', operator: 'eq', value: '123' }` |
| `ne` | Not equal to | any | `{ field: 'status', operator: 'ne', value: 'deleted' }` |
| `gt` | Greater than | number/date | `{ field: 'age', operator: 'gt', value: 18 }` |
| `gte` | Greater than or equal | number/date | `{ field: 'createdAt', operator: 'gte', value: new Date() }` |
| `lt` | Less than | number/date | `{ field: 'price', operator: 'lt', value: 100 }` |
| `lte` | Less than or equal | number/date | `{ field: 'updatedAt', operator: 'lte', value: new Date() }` |
| `like` | Pattern match (case-sensitive) | string | `{ field: 'name', operator: 'like', value: 'John%' }` |
| `ilike` | Pattern match (case-insensitive) | string | `{ field: 'email', operator: 'ilike', value: '%@gmail.com' }` |
| `in` | Value in array | array | `{ field: 'role', operator: 'in', values: ['admin', 'user'] }` |
| `notIn` | Value not in array | array | `{ field: 'id', operator: 'notIn', values: ['1', '2'] }` |
| `isNull` | Field is NULL | none | `{ field: 'deletedAt', operator: 'isNull' }` |
| `isNotNull` | Field is not NULL | none | `{ field: 'email', operator: 'isNotNull' }` |
| `between` | Value between two values | array[2] | `{ field: 'age', operator: 'between', values: [18, 65] }` |

## Logic Operators

- `AND`: All conditions must be true (default)
- `OR`: At least one condition must be true

## Type Definitions

```typescript
export type WhereOperator = 
  | 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' 
  | 'like' | 'ilike' | 'in' | 'notIn' 
  | 'isNull' | 'isNotNull' | 'between'

export interface WhereCondition {
  field: string
  operator: WhereOperator
  value?: any
  values?: any[] // For 'in', 'notIn', 'between' operators
}

export interface WhereClause {
  conditions: WhereCondition[]
  logic?: 'AND' | 'OR' // Default: 'AND'
}
```

## Usage Examples

### Basic Equality
```typescript
const whereClause = createWhereClause([
  createCondition('email', 'eq', '<EMAIL>')
])

const result = await getRecordsWithWhere('user', 'readUser', authData, whereClause)
```

### Multiple AND Conditions
```typescript
const whereClause = createWhereClause([
  createCondition('teamId', 'eq', 'team-123'),
  createCondition('disabled', 'eq', false)
], 'AND')
```

### Multiple OR Conditions
```typescript
const whereClause = createWhereClause([
  createCondition('emailVerified', 'eq', true),
  createCondition('phoneNumberVerified', 'eq', true)
], 'OR')
```

### Text Search
```typescript
const whereClause = createWhereClause([
  createCondition('displayName', 'ilike', '%john%')
])
```

### Array Values
```typescript
const whereClause = createWhereClause([
  createCondition('role', 'in', undefined, ['admin', 'moderator'])
])
```

### Date Ranges
```typescript
const whereClause = createWhereClause([
  createCondition('createdAt', 'between', undefined, [startDate, endDate])
])
```

### NULL Checks
```typescript
const whereClause = createWhereClause([
  createCondition('deletedAt', 'isNull')
])
```

### Dynamic Query Building
```typescript
function buildUserQuery(filters: {
  teamId?: string
  isActive?: boolean
  searchTerm?: string
}) {
  const conditions: WhereCondition[] = []
  
  if (filters.teamId) {
    conditions.push(createCondition('teamId', 'eq', filters.teamId))
  }
  
  if (filters.isActive !== undefined) {
    conditions.push(createCondition('disabled', 'eq', !filters.isActive))
  }
  
  if (filters.searchTerm) {
    conditions.push(createCondition('displayName', 'ilike', `%${filters.searchTerm}%`))
  }
  
  return createWhereClause(conditions, 'AND')
}
```

## Error Handling

The functions will throw errors for:
- Invalid table names
- Invalid column names
- Missing required values for operators
- Invalid operator usage

All errors are caught and returned in the standard `{ success: false, error: string }` format.

## Security

- All queries maintain RBAC permission checking
- SQL injection protection through parameterized queries
- Type safety through TypeScript interfaces
- Schema validation ensures only valid tables/columns are accessed

## Backward Compatibility

The original `getRecords` function remains unchanged, ensuring existing code continues to work without modifications.
