import { db } from '../clients'
import * as schema from '@repo/drizzle'
import {
	eq,
	ne,
	gt,
	gte,
	lt,
	lte,
	like,
	ilike,
	inArray,
	notInArray,
	isNull,
	isNotNull,
	and,
	or,
	between,
	type SQL
} from 'drizzle-orm'
import 'linq-extensions'

// Types for dynamic where clauses
export type WhereOperator =
	| 'eq'
	| 'ne'
	| 'gt'
	| 'gte'
	| 'lt'
	| 'lte'
	| 'like'
	| 'ilike'
	| 'in'
	| 'notIn'
	| 'isNull'
	| 'isNotNull'
	| 'between'

export interface WhereCondition {
	field: string
	operator: WhereOperator
	value?: any
	values?: any[] // For 'in', 'notIn', 'between' operators
}

export interface WhereClause {
	conditions: WhereCondition[]
	logic?: 'AND' | 'OR' // How to combine multiple conditions
}

// Helper function to build where clauses dynamically
function buildWhereClause(tableName: string, whereClause: WhereClause): SQL | undefined {
	if (!whereClause.conditions || whereClause.conditions.length === 0) {
		return undefined
	}

	const table = schema[tableName]
	if (!table) {
		throw new Error(`Table ${tableName} not found in schema`)
	}

	const conditions = whereClause.conditions.map(condition => {
		const column = table[condition.field]
		if (!column) {
			throw new Error(`Column ${condition.field} not found in table ${tableName}`)
		}

		switch (condition.operator) {
			case 'eq':
				return eq(column, condition.value)
			case 'ne':
				return ne(column, condition.value)
			case 'gt':
				return gt(column, condition.value)
			case 'gte':
				return gte(column, condition.value)
			case 'lt':
				return lt(column, condition.value)
			case 'lte':
				return lte(column, condition.value)
			case 'like':
				return like(column, condition.value)
			case 'ilike':
				return ilike(column, condition.value)
			case 'in':
				if (!condition.values || !Array.isArray(condition.values)) {
					throw new Error(`'in' operator requires 'values' array`)
				}
				return inArray(column, condition.values)
			case 'notIn':
				if (!condition.values || !Array.isArray(condition.values)) {
					throw new Error(`'notIn' operator requires 'values' array`)
				}
				return notInArray(column, condition.values)
			case 'isNull':
				return isNull(column)
			case 'isNotNull':
				return isNotNull(column)
			case 'between':
				if (!condition.values || condition.values.length !== 2) {
					throw new Error(`'between' operator requires exactly 2 values`)
				}
				return between(column, condition.values[0], condition.values[1])
			default:
				throw new Error(`Unsupported operator: ${condition.operator}`)
		}
	})

	// Combine conditions with AND or OR
	if (conditions.length === 1) {
		return conditions[0]
	}

	return whereClause.logic === 'OR' ? or(...conditions) : and(...conditions)
}

// Convenience functions for common query patterns
function createWhereClause(conditions: WhereCondition[], logic: 'AND' | 'OR' = 'AND'): WhereClause {
	return { conditions, logic }
}

function createCondition(field: string, operator: WhereOperator, value?: any, values?: any[]): WhereCondition {
	return { field, operator, value, values }
}

async function createRecord<T>(tablename: string, data: Partial<T>) {
	try {
		const newRecord = await db
			.insert(schema[tablename])
			.values(data)
			.returning()
		return { success: true, data: newRecord as T }
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

// Read all Records (original function for backward compatibility)
async function getRecords<T>(tableName: string) {
	try {
		const allRecords = await db.select().from(schema[tableName])
		return { success: true, data: allRecords as T[] }
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

// Enhanced function with dynamic where clauses
async function getRecordsWithWhere<T>(
	tableName: string,
	whereClause?: WhereClause
) {
	try {
		// Apply where clause if provided
		let records: any[]
		if (whereClause) {
			const whereCondition = buildWhereClause(tableName, whereClause)
			if (whereCondition) {
				records = await db.select().from(schema[tableName]).where(whereCondition).execute()
			} else {
				records = await db.select().from(schema[tableName]).execute()
			}
		} else {
			records = await db.select().from(schema[tableName]).execute()
		}

		return { success: true, data: records as T[] }
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

async function getRecordsByTeamId<T>(tableName: string, teamId: string) {
	try {
		const allRecords = await db.select().from(schema[tableName])
			.where(eq(schema[tableName].teamId, teamId))
			.execute()
		return { success: true, data: allRecords as T[] }
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

// Read a single Record by ID
async function getRecordById<T>(tablename: string, id: string) {
	try {
		const record = await db
			.select()
			.from(schema[tablename])
			.where(eq(schema[tablename].id, id))
			.execute()
		return record.length
			? { success: true, data: record.firstOrNull() as T }
			: { success: false, error: "Record not found" }
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

// Update a record by ID
async function updateRecord<T>(tableName: string, data: Partial<T>) {
	try {
		const id = (data as any).id
		if (!id) {
			throw new Error('ID is required for update operation')
		}

		console.log("UPDATE operations", schema[tableName])
		const updatedData = await db
			.update(schema[tableName])
			.set({ ...data, updatedAt: new Date() })
			.where(eq(schema[tableName].id, id))
			.returning()
		console.log("Update server action data", id, tableName, updatedData)
		return updatedData.length > 0
			? { success: true, data: updatedData.firstOrNull() as T }
			: { success: false, error: "Record not found" }
	} catch (error) {
		console.log("ERROR:", error)
		return { success: false, error: error }
	}
}

// Delete a Record by ID
async function deleteRecord<T>(tablename: string, data: Record<string, any>) {
	try {
		const id = (data as any).id
		if (!id) {
			throw new Error('ID is required for delete operation')
		}

		const deletedRecord = await db
			.delete(schema[tablename])
			.where(eq(schema[tablename].id, id))
			.returning()
		return deletedRecord.length
			? { success: true, data: deletedRecord.firstOrNull() as T }
			: { success: false, error: "Record not found" }
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

// Additional helper functions for common patterns

// Create multiple records (bulk insert)
async function createMultipleRecords<T>(tablename: string, dataArray: Partial<T>[]) {
	try {
		const newRecords = await db
			.insert(schema[tablename])
			.values(dataArray)
			.returning()
		return { success: true, data: newRecords as T[] }
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

// Update multiple records with where clause
async function updateRecordsWhere<T>(
	tableName: string,
	data: Partial<T>,
	whereClause: WhereClause
) {
	try {
		const whereCondition = buildWhereClause(tableName, whereClause)
		if (!whereCondition) {
			throw new Error('Where clause is required for bulk update')
		}

		const updatedRecords = await db
			.update(schema[tableName])
			.set({ ...data, updatedAt: new Date() })
			.where(whereCondition)
			.returning()

		return { success: true, data: updatedRecords as T[] }
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

// Delete multiple records with where clause
async function deleteRecordsWhere<T>(tableName: string, whereClause: WhereClause) {
	try {
		const whereCondition = buildWhereClause(tableName, whereClause)
		if (!whereCondition) {
			throw new Error('Where clause is required for bulk delete')
		}

		const deletedRecords = await db
			.delete(schema[tableName])
			.where(whereCondition)
			.returning()

		return { success: true, data: deletedRecords as T[] }
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

// Count records with optional where clause
async function countRecords(tableName: string, whereClause?: WhereClause) {
	try {
		// Get all records and count them (simpler approach)
		const recordsResult = await getRecordsWithWhere(tableName, whereClause)
		if (!recordsResult.success) {
			return recordsResult
		}

		return { success: true, data: recordsResult.data?.length || 0 }
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

// Check if record exists
async function recordExists(tableName: string, id: string) {
	try {
		const record = await db
			.select({ id: schema[tableName].id })
			.from(schema[tableName])
			.where(eq(schema[tableName].id, id))
			.limit(1)
			.execute()

		return { success: true, data: record.length > 0 }
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

// Upsert (insert or update if exists)
async function upsertRecord<T>(tableName: string, data: Partial<T>) {
	try {
		const id = (data as any).id
		if (!id) {
			// No ID provided, create new record
			return await createRecord<T>(tableName, data)
		}

		// Check if record exists
		const existsResult = await recordExists(tableName, id)
		if (!existsResult.success) {
			return existsResult
		}

		if (existsResult.data) {
			// Record exists, update it
			return await updateRecord<T>(tableName, data)
		} else {
			// Record doesn't exist, create it
			return await createRecord<T>(tableName, data)
		}
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

export const dbHelper = {
	createRecord,
	updateRecord,
	deleteRecord,
	getRecords,
	getRecordsWithWhere,
	getRecordById,
	getRecordsByTeamId,
	createMultipleRecords,
	updateRecordsWhere,
	deleteRecordsWhere,
	countRecords,
	recordExists,
	upsertRecord,
	createWhereClause,
	createCondition,
	buildWhereClause
}