import { db } from './clients'
import * as schema from '@repo/drizzle'
import {
	eq,
	ne,
	gt,
	gte,
	lt,
	lte,
	like,
	ilike,
	inArray,
	notInArray,
	isNull,
	isNotNull,
	and,
	or,
	between,
	type SQL
} from 'drizzle-orm'
import 'linq-extensions'
import type { AuthData } from './server.helpers'
import { rbac, type ActionType, type paramsType } from '@repo/common'

// Types for dynamic where clauses
export type WhereOperator =
	| 'eq'
	| 'ne'
	| 'gt'
	| 'gte'
	| 'lt'
	| 'lte'
	| 'like'
	| 'ilike'
	| 'in'
	| 'notIn'
	| 'isNull'
	| 'isNotNull'
	| 'between'

export interface WhereCondition {
	field: string
	operator: WhereOperator
	value?: any
	values?: any[] // For 'in', 'notIn', 'between' operators
}

export interface WhereClause {
	conditions: WhereCondition[]
	logic?: 'AND' | 'OR' // How to combine multiple conditions
}

// Helper function to build where clauses dynamically
function buildWhereClause(tableName: string, whereClause: WhereClause): SQL | undefined {
	if (!whereClause.conditions || whereClause.conditions.length === 0) {
		return undefined
	}

	const table = schema[tableName]
	if (!table) {
		throw new Error(`Table ${tableName} not found in schema`)
	}

	const conditions = whereClause.conditions.map(condition => {
		const column = table[condition.field]
		if (!column) {
			throw new Error(`Column ${condition.field} not found in table ${tableName}`)
		}

		switch (condition.operator) {
			case 'eq':
				return eq(column, condition.value)
			case 'ne':
				return ne(column, condition.value)
			case 'gt':
				return gt(column, condition.value)
			case 'gte':
				return gte(column, condition.value)
			case 'lt':
				return lt(column, condition.value)
			case 'lte':
				return lte(column, condition.value)
			case 'like':
				return like(column, condition.value)
			case 'ilike':
				return ilike(column, condition.value)
			case 'in':
				if (!condition.values || !Array.isArray(condition.values)) {
					throw new Error(`'in' operator requires 'values' array`)
				}
				return inArray(column, condition.values)
			case 'notIn':
				if (!condition.values || !Array.isArray(condition.values)) {
					throw new Error(`'notIn' operator requires 'values' array`)
				}
				return notInArray(column, condition.values)
			case 'isNull':
				return isNull(column)
			case 'isNotNull':
				return isNotNull(column)
			case 'between':
				if (!condition.values || condition.values.length !== 2) {
					throw new Error(`'between' operator requires exactly 2 values`)
				}
				return between(column, condition.values[0], condition.values[1])
			default:
				throw new Error(`Unsupported operator: ${condition.operator}`)
		}
	})

	// Combine conditions with AND or OR
	if (conditions.length === 1) {
		return conditions[0]
	}

	return whereClause.logic === 'OR' ? or(...conditions) : and(...conditions)
}

// Convenience functions for common query patterns
export function createWhereClause(conditions: WhereCondition[], logic: 'AND' | 'OR' = 'AND'): WhereClause {
	return { conditions, logic }
}

export function createCondition(field: string, operator: WhereOperator, value?: any, values?: any[]): WhereCondition {
	return { field, operator, value, values }
}

export async function createRecord<T>(tablename: string, data: Partial<T>, rbacAction: ActionType, authData: AuthData) {
	try {
		const hasPermission = await checkPermission(rbacAction, data, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}
		const newRecord = await db
			.insert(schema[tablename])
			.values(data)
			.returning()
		return { success: true, data: newRecord as T }
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

// Read all Records (original function for backward compatibility)
export async function getRecords<T>(tableName: string, rbacAction: ActionType, authData: AuthData) {
	try {
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}
		const allRecords = await db.select().from(schema[tableName])
		return { success: true, data: allRecords as T[] }
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

// Enhanced function with dynamic where clauses
export async function getRecordsWithWhere<T>(
	tableName: string,
	rbacAction: ActionType,
	authData: AuthData,
	whereClause?: WhereClause
) {
	try {
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		// Apply where clause if provided
		let records: any[]
		if (whereClause) {
			const whereCondition = buildWhereClause(tableName, whereClause)
			if (whereCondition) {
				records = await db.select().from(schema[tableName]).where(whereCondition).execute()
			} else {
				records = await db.select().from(schema[tableName]).execute()
			}
		} else {
			records = await db.select().from(schema[tableName]).execute()
		}

		return { success: true, data: records as T[] }
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

export async function getRecordsByTeamId<T>(tableName: string, rbacAction: ActionType, authData: AuthData, teamId: string) {
	try {
		const hasPermission = await checkPermission(rbacAction, { teamId }, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}
		const allRecords = await db.select().from(schema[tableName])
			.where(eq(schema[tableName].teamId, teamId))
			.execute()
		return { success: true, data: allRecords as T[] }
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

// Read a single Record by ID
export async function getRecordById<T>(tablename: string, rbacAction: ActionType, authData: AuthData, id: string) {
	try {
		const hasPermission = await checkPermission(rbacAction, { id }, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}
		const record = await db
			.select()
			.from(schema[tablename])
			.where(eq(schema[tablename].id, id))
			.execute()
		return record.length
			? { success: true, data: record.firstOrNull() as T }
			: { success: false, error: "Record not found" }
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

// Update a record by ID
export async function updateRecord<T>(tableName: string, rbacAction: ActionType, authData: AuthData, data: Partial<T>) {
	try {
		const hasPermission = await checkPermission(rbacAction, data, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}
		const id = (data as any).id
		console.log("UPDATE operations", schema[tableName])
		const updatedData = await db
			.update(schema[tableName])
			.set({ ...data, updatedAt: new Date() })
			.where(eq(schema[tableName].id, id))
			.returning()
		console.log("Update server action data", id, tableName, updatedData)
		return updatedData.length > 0
			? { success: true, data: updatedData.firstOrNull() as T }
			: { success: false, error: "Record not found" }
	} catch (error) {
		console.log("ERROR:", error)
		return { success: false, error: error }
	}
}

// Delete a Record by ID
export async function deleteRecord<T>(tablename: string, rbacAction: ActionType, authData: AuthData, data: Record<string, any>) {

	try {
		const id = (data as any).id
		const hasPermission = await checkPermission(rbacAction, data, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}
		const deletedRecord = await db
			.delete(schema[tablename])
			.where(eq(schema[tablename].id, id))
			.returning()
		return deletedRecord.length
			? { success: true, data: deletedRecord.firstOrNull() as T }
			: { success: false, error: "Record not found" }
	} catch (error) {
		return { success: false, error: (error as Error).message }
	}
}

export const checkPermission = async (action: ActionType,
	payload: Record<string, any>,
	authData: AuthData): Promise<boolean> => {
	try {
		const params: paramsType = {
			payload,
			authData
		}

		// Use the user's role to check permission
		const role = authData.defaultRole
		const hasPermission = await rbac.can(role, action, params)
		return hasPermission
	} catch (error) {
		console.error('Permission check failed:', error)
		return false
	}
}