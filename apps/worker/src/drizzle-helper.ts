import { eq, SQL } from 'drizzle-orm'
import type { AnyPgTable } from 'drizzle-orm/pg-core'
import { rbac, type paramsType } from '@repo/common'
import type { AuthData } from './server.helpers'
import type { DatabaseType } from './clients'

/**
 * Extends Drizzle tables with RBAC-protected CRUD operations that also update actor state
 */
export function extendDrizzleTable<T extends AnyPgTable>(db: DatabaseType) {
	// Get a table prototype to extend
	const tablePrototype = Object.getPrototypeOf(db.query.user || db.query[Object.keys(db.query)[0]])

	if (!tablePrototype) {
		console.error('Could not find table prototype to extend')
		return
	}

	// Define properties on the actual table prototype
	Object.defineProperties(tablePrototype, {
		create: {
			value: async function (data: any, authData?: AuthData, actorState?: any) {
				const result = await createRecord(db, this, data, authData)

				// Update actor state if provided
				if (actorState && Array.isArray(actorState)) {
					actorState.push(result)
				} else if (actorState && typeof actorState === 'object') {
					// If the state is an object with a collection property
					const tableName = getTableName(this)
					const collectionName = `${tableName}s` // Simple pluralization

					if (Array.isArray(actorState[collectionName])) {
						actorState[collectionName].push(result)
					}
				}

				return result
			},
			configurable: true
		},
		update: {
			value: async function (
				id: string | number,
				data: any,
				authData?: AuthData,
				actorState?: any
			) {
				const result = await updateRecord(db, this, id, data, authData)

				// Update actor state if provided and result exists
				if (result && actorState) {
					if (Array.isArray(actorState)) {
						// Find and update the item in the array
						const index = actorState.findIndex((item: any) => item.id === id)
						if (index !== -1) {
							actorState[index] = { ...actorState[index], ...result }
						}
					} else if (typeof actorState === 'object') {
						// If the state is an object with a collection property
						const tableName = getTableName(this)
						const collectionName = `${tableName}s` // Simple pluralization

						if (Array.isArray(actorState[collectionName])) {
							const index = actorState[collectionName].findIndex(
								(item: any) => item.id === id
							)
							if (index !== -1) {
								actorState[collectionName][index] = {
									...actorState[collectionName][index],
									...result
								}
							}
						}
					}
				}

				return result
			},
			configurable: true
		},
		delete: {
			value: async function (id: string | number, authData?: AuthData, actorState?: any) {
				const result = await deleteRecord(db, this, id, authData)

				// Update actor state if provided and result exists
				if (result && actorState) {
					if (Array.isArray(actorState)) {
						// Remove the item from the array
						const index = actorState.findIndex((item: any) => item.id === id)
						if (index !== -1) {
							actorState.splice(index, 1)
						}
					} else if (typeof actorState === 'object') {
						// If the state is an object with a collection property
						const tableName = getTableName(this)
						const collectionName = `${tableName}s` // Simple pluralization

						if (Array.isArray(actorState[collectionName])) {
							const index = actorState[collectionName].findIndex(
								(item: any) => item.id === id
							)
							if (index !== -1) {
								actorState[collectionName].splice(index, 1)
							}
						}
					}
				}

				return result
			},
			configurable: true
		},
		findById: {
			value: async function (
				id: string | number,
				authData?: AuthData,
				primaryKey: string = 'id'
			) {
				return await findRecordById(db, this, id, authData, primaryKey)
			},
			configurable: true
		},
		findByParams: {
			value: async function (params: SQL | undefined, authData?: AuthData) {
				return await findRecordsByParams(db, this, params, authData)
			},
			configurable: true
		},
		findOneByParams: {
			value: async function (params: SQL | undefined, authData?: AuthData) {
				return await findOneByParams(db, this, params, authData)
			},
			configurable: true
		}
	})
}

/**
 * Example usage:
 *
 * // Initialize the extension
 * import { db } from 'drizzle/db';
 * import { extendDrizzleTable } from '@repo/drizzle-helper';
 * import { team } from 'drizzle/schema';
 * import type { AuthData } from '../server.helpers';
 *
 * // Extend the tables with CRUD methods
 * extendDrizzleTable(db);
 *
 * // Now you can use the extension methods directly on table objects
 * async function createTeam(teamData, authData: AuthData) {
 *   // Create a new team with RBAC check
 *   const newTeam = await team.create(teamData, authData);
 *   return newTeam;
 * }
 *
 * async function updateTeam(id: string, teamData, authData: AuthData) {
 *   // Update an existing team with RBAC check
 *   const updatedTeam = await team.update(id, teamData, authData);
 *   return updatedTeam;
 * }
 *
 * async function deleteTeam(id: string, authData: AuthData) {
 *   // Delete a team with RBAC check
 *   const deletedTeam = await team.delete(id, authData);
 *   return deletedTeam;
 * }
 */

/**
 * Example usage with actor state:
 *
 * // In your appointment-actor.ts
 * import { actor } from 'actor-core'
 * import { db } from 'drizzle/db'
 * import { appointment } from 'drizzle/schema'
 * import { extendDrizzleTable } from '@repo/drizzle-helper'
 *
 * // Extend tables with CRUD methods
 * extendDrizzleTable(db)
 *
 * export const appointmentActor = actor({
 *   state: {
 *     appointments: [] // This will be automatically updated
 *   },
 *   actions: {
 *     create: async (c, appointmentData, authData) => {
 *       // This will update both the database and the actor state
 *       const result = await appointment.create(
 *         appointmentData,
 *         authData,
 *         c.state.appointments
 *       )
 *       return result
 *     },
 *     update: async (c, id, appointmentData, authData) => {
 *       // This will update both the database and the actor state
 *       const result = await appointment.update(
 *         id,
 *         appointmentData,
 *         authData,
 *         c.state.appointments
 *       )
 *       return result
 *     },
 *     delete: async (c, id, authData) => {
 *       // This will update both the database and the actor state
 *       const result = await appointment.delete(
 *         id,
 *         authData,
 *         c.state.appointments
 *       )
 *       return result
 *     },
 *     getById: async (c, id, authData) => {
 *       // Get a single appointment by ID with permission check
 *       const result = await appointment.findById(id, authData)
 *       return result
 *     },
 *     getByTeamId: async (c, teamId, authData) => {
 *       // Get appointments by team ID with permission check
 *       const result = await appointment.findByParams({ teamId }, authData)
 *       return result
 *     }
 *   }
 * })
 */

/**
 * Get the table name from a table object
 */
function getTableName(table: AnyPgTable): string {
	// Extract table name from the table object
	const tableName = table._.name.split('.').pop() || ''
	// Convert to camelCase if it's snake_case
	return tableName.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * Check if the user has permission to perform an action
 */
async function checkPermission(
	action: string,
	payload: Record<string, any>,
	authData: AuthData
): Promise<boolean> {
	try {
		const params: paramsType = {
			payload,
			authData
		}

		// Use the user's role to check permission
		const role = authData.defaultRole
		const hasPermission = await rbac.can(role, action, params)
		return hasPermission
	} catch (error) {
		console.error('Permission check failed:', error)
		return false
	}
}

/**
 * Create a new record with RBAC permission check
 */
async function createRecord<T extends Record<string, any>>(
	db: DatabaseType,
	table: AnyPgTable,
	data: T,
	authData?: AuthData
): Promise<T> {
	// If authData is provided, check permissions
	if (authData) {
		const tableName = getTableName(table)
		const rbacAction = `create${tableName.charAt(0).toUpperCase() + tableName.slice(1)}`

		const hasPermission = await checkPermission(rbacAction, data, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}
	}

	// Insert the record
	const result = await db.insert(table).values(data).returning().execute()
	return (Array.isArray(result) && result.length > 0 ? result[0] : result) as T
}

/**
 * Update an existing record with RBAC permission check
 */
async function updateRecord<T extends Record<string, any>>(
	db: DatabaseType,
	table: AnyPgTable,
	id: string | number,
	data: Partial<T>,
	authData?: AuthData,
	primaryKey: string = 'id'
): Promise<T | null> {
	// Get the existing record first to check permissions
	const existingRecord = await db.select().from(table).where(eq(table[primaryKey], id)).execute()

	if (!existingRecord || existingRecord.length === 0) {
		return null // Record not found
	}

	// If authData is provided, check permissions
	if (authData) {
		const tableName = getTableName(table)
		const rbacAction = `update${tableName.charAt(0).toUpperCase() + tableName.slice(1)}`

		const hasPermission = await checkPermission(
			rbacAction,
			{ ...existingRecord[0], ...data },
			authData
		)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}
	}

	// Update the record
	const result = await db
		.update(table)
		.set(data)
		.where(eq(table[primaryKey], id))
		.returning()
		.execute()

	return (Array.isArray(result) && result.length > 0 ? result[0] : result) as T
}

/**
 * Delete a record with RBAC permission check
 */
async function deleteRecord<T extends Record<string, any>>(
	db: DatabaseType,
	table: AnyPgTable,
	id: string | number,
	authData?: AuthData,
	primaryKey: string = 'id'
): Promise<T | null> {
	// Get the record before deletion to check permissions
	const recordToDelete = await db.select().from(table).where(eq(table[primaryKey], id)).execute()

	if (!recordToDelete || recordToDelete.length === 0) {
		return null // Record not found
	}

	// If authData is provided, check permissions
	if (authData) {
		const tableName = getTableName(table)
		const rbacAction = `delete${tableName.charAt(0).toUpperCase() + tableName.slice(1)}`

		const hasPermission = await checkPermission(rbacAction, recordToDelete[0], authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}
	}

	// Delete the record
	const result = await db.delete(table).where(eq(table[primaryKey], id)).returning().execute()

	return (Array.isArray(result) && result.length > 0 ? result[0] : result) as T
}

/**
 * Find a record by ID with RBAC permission check
 */
async function findRecordById<T extends Record<string, any>>(
	db: DatabaseType,
	table: AnyPgTable,
	id: string | number,
	authData?: AuthData,
	primaryKey: string = 'id'
): Promise<T | null> {
	// Get the record
	const record = await db.select().from(table).where(eq(table[primaryKey], id)).execute()

	if (!record || record.length === 0) {
		return null // Record not found
	}

	// If authData is provided, check permissions
	if (authData) {
		const tableName = getTableName(table)
		const rbacAction = `read${tableName.charAt(0).toUpperCase() + tableName.slice(1)}`

		const hasPermission = await checkPermission(rbacAction, record[0], authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}
	}

	return record[0] as T
}

/**
 * Find records by parameters with RBAC permission check
 */
async function findRecordsByParams<T extends Record<string, any>>(
	db: DatabaseType,
	table: AnyPgTable,
	params: SQL | undefined,
	authData?: AuthData
): Promise<T[]> {
	let query: any = db.select().from(table)

	// Apply SQL condition if provided
	if (params) {
		query = query.where(params)
	}

	// Execute the query
	const records = await query.execute()

	// If authData is provided, filter records based on permissions
	if (authData) {
		const tableName = getTableName(table)
		const rbacAction = `read${tableName.charAt(0).toUpperCase() + tableName.slice(1)}`

		// Filter records based on permissions
		const authorizedRecords: T[] = []

		for (const record of records) {
			const hasPermission = await checkPermission(rbacAction, record, authData)

			if (hasPermission) {
				authorizedRecords.push(record as T)
			}
		}

		return authorizedRecords
	}

	return records as T[]
}

/**
 * Find the first record matching parameters with RBAC permission check
 */
async function findOneByParams<T extends Record<string, any>>(
	db: DatabaseType,
	table: AnyPgTable,
	params: SQL | undefined,
	authData?: AuthData
): Promise<T | null> {
	// Get records matching params
	const records = await findRecordsByParams<T>(db, table, params, authData)

	// Return the first record or null if none found
	return records.length > 0 ? records[0] : null
}
