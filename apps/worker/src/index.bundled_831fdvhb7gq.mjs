// src/index.ts
import { setup } from "actor-core";
import "@dotenvx/dotenvx/config";
import { initSuper<PERSON>son } from "@repo/common";

// src/clients/db.client.ts
import { drizzle } from "drizzle-orm/node-postgres";
import * as schema from "@repo/drizzle";
var db = drizzle(process.env.DATABASE_URL, {
  casing: "snake_case",
  schema
});

// src/drizzle-helper.ts
import { eq } from "drizzle-orm";
import { rbac } from "@repo/common";
function extendDrizzleTable(db2) {
  const tablePrototype = Object.getPrototypeOf(db2.query.user || db2.query[Object.keys(db2.query)[0]]);
  if (!tablePrototype) {
    console.error("Could not find table prototype to extend");
    return;
  }
  Object.defineProperties(tablePrototype, {
    create: {
      value: async function(data, authData, actorState) {
        const result = await createRecord(db2, this, data, authData);
        if (actorState && Array.isArray(actorState)) {
          actorState.push(result);
        } else if (actorState && typeof actorState === "object") {
          const tableName = getTableName(this);
          const collectionName = `${tableName}s`;
          if (Array.isArray(actorState[collectionName])) {
            actorState[collectionName].push(result);
          }
        }
        return result;
      },
      configurable: true
    },
    update: {
      value: async function(id, data, authData, actorState) {
        const result = await updateRecord(db2, this, id, data, authData);
        if (result && actorState) {
          if (Array.isArray(actorState)) {
            const index = actorState.findIndex((item) => item.id === id);
            if (index !== -1) {
              actorState[index] = { ...actorState[index], ...result };
            }
          } else if (typeof actorState === "object") {
            const tableName = getTableName(this);
            const collectionName = `${tableName}s`;
            if (Array.isArray(actorState[collectionName])) {
              const index = actorState[collectionName].findIndex(
                (item) => item.id === id
              );
              if (index !== -1) {
                actorState[collectionName][index] = {
                  ...actorState[collectionName][index],
                  ...result
                };
              }
            }
          }
        }
        return result;
      },
      configurable: true
    },
    delete: {
      value: async function(id, authData, actorState) {
        const result = await deleteRecord(db2, this, id, authData);
        if (result && actorState) {
          if (Array.isArray(actorState)) {
            const index = actorState.findIndex((item) => item.id === id);
            if (index !== -1) {
              actorState.splice(index, 1);
            }
          } else if (typeof actorState === "object") {
            const tableName = getTableName(this);
            const collectionName = `${tableName}s`;
            if (Array.isArray(actorState[collectionName])) {
              const index = actorState[collectionName].findIndex(
                (item) => item.id === id
              );
              if (index !== -1) {
                actorState[collectionName].splice(index, 1);
              }
            }
          }
        }
        return result;
      },
      configurable: true
    },
    findById: {
      value: async function(id, authData, primaryKey = "id") {
        return await findRecordById(db2, this, id, authData, primaryKey);
      },
      configurable: true
    },
    findByParams: {
      value: async function(params, authData) {
        return await findRecordsByParams(db2, this, params, authData);
      },
      configurable: true
    },
    findOneByParams: {
      value: async function(params, authData) {
        return await findOneByParams(db2, this, params, authData);
      },
      configurable: true
    }
  });
}
function getTableName(table) {
  const tableName = table._.name.split(".").pop() || "";
  return tableName.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}
async function checkPermission(action, payload, authData) {
  try {
    const params = {
      payload,
      authData
    };
    const role2 = authData.defaultRole;
    const hasPermission = await rbac.can(role2, action, params);
    return hasPermission;
  } catch (error) {
    console.error("Permission check failed:", error);
    return false;
  }
}
async function createRecord(db2, table, data, authData) {
  if (authData) {
    const tableName = getTableName(table);
    const rbacAction = `create${tableName.charAt(0).toUpperCase() + tableName.slice(1)}`;
    const hasPermission = await checkPermission(rbacAction, data, authData);
    if (!hasPermission) {
      throw new Error(`Permission denied: User does not have ${rbacAction} permission`);
    }
  }
  const result = await db2.insert(table).values(data).returning().execute();
  return Array.isArray(result) && result.length > 0 ? result[0] : result;
}
async function updateRecord(db2, table, id, data, authData, primaryKey = "id") {
  const existingRecord = await db2.select().from(table).where(eq(table[primaryKey], id)).execute();
  if (!existingRecord || existingRecord.length === 0) {
    return null;
  }
  if (authData) {
    const tableName = getTableName(table);
    const rbacAction = `update${tableName.charAt(0).toUpperCase() + tableName.slice(1)}`;
    const hasPermission = await checkPermission(
      rbacAction,
      { ...existingRecord[0], ...data },
      authData
    );
    if (!hasPermission) {
      throw new Error(`Permission denied: User does not have ${rbacAction} permission`);
    }
  }
  const result = await db2.update(table).set(data).where(eq(table[primaryKey], id)).returning().execute();
  return Array.isArray(result) && result.length > 0 ? result[0] : result;
}
async function deleteRecord(db2, table, id, authData, primaryKey = "id") {
  const recordToDelete = await db2.select().from(table).where(eq(table[primaryKey], id)).execute();
  if (!recordToDelete || recordToDelete.length === 0) {
    return null;
  }
  if (authData) {
    const tableName = getTableName(table);
    const rbacAction = `delete${tableName.charAt(0).toUpperCase() + tableName.slice(1)}`;
    const hasPermission = await checkPermission(rbacAction, recordToDelete[0], authData);
    if (!hasPermission) {
      throw new Error(`Permission denied: User does not have ${rbacAction} permission`);
    }
  }
  const result = await db2.delete(table).where(eq(table[primaryKey], id)).returning().execute();
  return Array.isArray(result) && result.length > 0 ? result[0] : result;
}
async function findRecordById(db2, table, id, authData, primaryKey = "id") {
  const record = await db2.select().from(table).where(eq(table[primaryKey], id)).execute();
  if (!record || record.length === 0) {
    return null;
  }
  if (authData) {
    const tableName = getTableName(table);
    const rbacAction = `read${tableName.charAt(0).toUpperCase() + tableName.slice(1)}`;
    const hasPermission = await checkPermission(rbacAction, record[0], authData);
    if (!hasPermission) {
      throw new Error(`Permission denied: User does not have ${rbacAction} permission`);
    }
  }
  return record[0];
}
async function findRecordsByParams(db2, table, params, authData) {
  let query = db2.select().from(table);
  if (params) {
    query = query.where(params);
  }
  const records = await query.execute();
  if (authData) {
    const tableName = getTableName(table);
    const rbacAction = `read${tableName.charAt(0).toUpperCase() + tableName.slice(1)}`;
    const authorizedRecords = [];
    for (const record of records) {
      const hasPermission = await checkPermission(rbacAction, record, authData);
      if (hasPermission) {
        authorizedRecords.push(record);
      }
    }
    return authorizedRecords;
  }
  return records;
}
async function findOneByParams(db2, table, params, authData) {
  const records = await findRecordsByParams(db2, table, params, authData);
  return records.length > 0 ? records[0] : null;
}

// src/index.ts
import "linq-extensions";

// src/actors/appointment-type-actor.ts
import { actor } from "actor-core";

// src/mastra/index.ts
import { Mastra } from "@mastra/core/mastra";

// src/mastra/workflows/index.ts
import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core/agent";
import { createStep, createWorkflow } from "@mastra/core";
import { z } from "zod";
var llm = openai("gpt-4o");
var agent = new Agent({
  name: "Weather Agent",
  model: llm,
  instructions: `
        You are a local activities and travel expert who excels at weather-based planning. Analyze the weather data and provide practical activity recommendations.

        For each day in the forecast, structure your response exactly as follows:

        \u{1F4C5} [Day, Month Date, Year]
        \u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550

        \u{1F321}\uFE0F WEATHER SUMMARY
        \u2022 Conditions: [brief description]
        \u2022 Temperature: [X\xB0C/Y\xB0F to A\xB0C/B\xB0F]
        \u2022 Precipitation: [X% chance]

        \u{1F305} MORNING ACTIVITIES
        Outdoor:
        \u2022 [Activity Name] - [Brief description including specific location/route]
          Best timing: [specific time range]
          Note: [relevant weather consideration]

        \u{1F31E} AFTERNOON ACTIVITIES
        Outdoor:
        \u2022 [Activity Name] - [Brief description including specific location/route]
          Best timing: [specific time range]
          Note: [relevant weather consideration]

        \u{1F3E0} INDOOR ALTERNATIVES
        \u2022 [Activity Name] - [Brief description including specific venue]
          Ideal for: [weather condition that would trigger this alternative]

        \u26A0\uFE0F SPECIAL CONSIDERATIONS
        \u2022 [Any relevant weather warnings, UV index, wind conditions, etc.]

        Guidelines:
        - Suggest 2-3 time-specific outdoor activities per day
        - Include 1-2 indoor backup options
        - For precipitation >50%, lead with indoor activities
        - All activities must be specific to the location
        - Include specific venues, trails, or locations
        - Consider activity intensity based on temperature
        - Keep descriptions concise but informative

        Maintain this exact formatting for consistency, using the emoji and section headers as shown.
      `
});
var forecastSchema = z.array(
  z.object({
    date: z.string(),
    maxTemp: z.number(),
    minTemp: z.number(),
    precipitationChance: z.number(),
    condition: z.string(),
    location: z.string()
  })
);
var fetchWeather = createStep({
  id: "fetch-weather",
  description: "Fetches weather forecast for a given city",
  inputSchema: z.object({
    city: z.string().describe("The city to get the weather for")
  }),
  outputSchema: forecastSchema,
  execute: async ({ inputData }) => {
    const triggerData = inputData;
    if (!triggerData) {
      throw new Error("Trigger data not found");
    }
    const geocodingUrl = `https://geocoding-api.open-meteo.com/v1/search?name=${encodeURIComponent(triggerData.city)}&count=1`;
    const geocodingResponse = await fetch(geocodingUrl);
    const geocodingData = await geocodingResponse.json();
    if (!geocodingData.results?.[0]) {
      throw new Error(`Location '${triggerData.city}' not found`);
    }
    const { latitude, longitude, name } = geocodingData.results[0];
    const weatherUrl = `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&daily=temperature_2m_max,temperature_2m_min,precipitation_probability_mean,weathercode&timezone=auto`;
    const response = await fetch(weatherUrl);
    const data = await response.json();
    const forecast = data.daily.time.map((date, index) => ({
      date,
      maxTemp: data.daily.temperature_2m_max[index],
      minTemp: data.daily.temperature_2m_min[index],
      precipitationChance: data.daily.precipitation_probability_mean[index],
      condition: getWeatherCondition(data.daily.weathercode[index]),
      location: name
    }));
    return forecast;
  }
});
var planActivities = createStep({
  id: "plan-activities",
  description: "Suggests activities based on weather conditions",
  inputSchema: {},
  outputSchema: z.object({
    activities: z.string()
  }),
  execute: async ({ inputData, mastra: mastra2 }) => {
    const forecast = inputData;
    if (!forecast || forecast.length === 0) {
      throw new Error("Forecast data not found");
    }
    const prompt = `Based on the following weather forecast for ${forecast[0]?.location}, suggest appropriate activities:
      ${JSON.stringify(forecast, null, 2)}
      `;
    const response = await agent.stream([
      {
        role: "user",
        content: prompt
      }
    ]);
    let activitiesText = "";
    for await (const chunk of response.textStream) {
      process.stdout.write(chunk);
      activitiesText += chunk;
    }
    return {
      activities: activitiesText
    };
  }
});
function getWeatherCondition(code) {
  const conditions = {
    0: "Clear sky",
    1: "Mainly clear",
    2: "Partly cloudy",
    3: "Overcast",
    45: "Foggy",
    48: "Depositing rime fog",
    51: "Light drizzle",
    53: "Moderate drizzle",
    55: "Dense drizzle",
    61: "Slight rain",
    63: "Moderate rain",
    65: "Heavy rain",
    71: "Slight snow fall",
    73: "Moderate snow fall",
    75: "Heavy snow fall",
    95: "Thunderstorm"
  };
  return conditions[code] || "Unknown";
}
var weatherWorkflow = createWorkflow({
  id: "weather-workflow",
  inputSchema: z.object({
    city: z.string().describe("The city to get the weather for")
  }),
  outputSchema: z.object({
    activities: z.string()
  }),
  steps: [fetchWeather]
}).then(planActivities).commit();

// src/mastra/agents/index.ts
import { openai as openai2 } from "@ai-sdk/openai";
import { Agent as Agent2 } from "@mastra/core/agent";

// src/mastra/tools/index.ts
import { createTool } from "@mastra/core/tools";
import { z as z2 } from "zod";
var weatherTool = createTool({
  id: "get-weather",
  description: "Get current weather for a location",
  inputSchema: z2.object({
    location: z2.string().describe("City name")
  }),
  outputSchema: z2.object({
    temperature: z2.number(),
    feelsLike: z2.number(),
    humidity: z2.number(),
    windSpeed: z2.number(),
    windGust: z2.number(),
    conditions: z2.string(),
    location: z2.string()
  }),
  execute: async ({ context }) => {
    return await getWeather(context.location);
  }
});
var getWeather = async (location) => {
  const geocodingUrl = `https://geocoding-api.open-meteo.com/v1/search?name=${encodeURIComponent(location)}&count=1`;
  const geocodingResponse = await fetch(geocodingUrl);
  const geocodingData = await geocodingResponse.json();
  if (!geocodingData.results?.[0]) {
    throw new Error(`Location '${location}' not found`);
  }
  const { latitude, longitude, name } = geocodingData.results[0];
  const weatherUrl = `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&current=temperature_2m,apparent_temperature,relative_humidity_2m,wind_speed_10m,wind_gusts_10m,weather_code`;
  const response = await fetch(weatherUrl);
  const data = await response.json();
  return {
    temperature: data.current.temperature_2m,
    feelsLike: data.current.apparent_temperature,
    humidity: data.current.relative_humidity_2m,
    windSpeed: data.current.wind_speed_10m,
    windGust: data.current.wind_gusts_10m,
    conditions: getWeatherCondition2(data.current.weather_code),
    location: name
  };
};
function getWeatherCondition2(code) {
  const conditions = {
    0: "Clear sky",
    1: "Mainly clear",
    2: "Partly cloudy",
    3: "Overcast",
    45: "Foggy",
    48: "Depositing rime fog",
    51: "Light drizzle",
    53: "Moderate drizzle",
    55: "Dense drizzle",
    56: "Light freezing drizzle",
    57: "Dense freezing drizzle",
    61: "Slight rain",
    63: "Moderate rain",
    65: "Heavy rain",
    66: "Light freezing rain",
    67: "Heavy freezing rain",
    71: "Slight snow fall",
    73: "Moderate snow fall",
    75: "Heavy snow fall",
    77: "Snow grains",
    80: "Slight rain showers",
    81: "Moderate rain showers",
    82: "Violent rain showers",
    85: "Slight snow showers",
    86: "Heavy snow showers",
    95: "Thunderstorm",
    96: "Thunderstorm with slight hail",
    99: "Thunderstorm with heavy hail"
  };
  return conditions[code] || "Unknown";
}

// src/mastra/utils.ts
import { Memory } from "@mastra/memory";
import { PgVector, PostgresStore } from "@mastra/pg";
import { PinoLogger } from "@mastra/loggers";
import { fastembed } from "@mastra/fastembed";
var logger = new PinoLogger({
  name: "mastra",
  level: "debug"
});
var storage = new PostgresStore({
  connectionString: process.env.DATABASE_URL
});
var vector = new PgVector({
  connectionString: process.env.DATABASE_URL
});
var memory = new Memory({
  storage,
  vector,
  embedder: fastembed,
  options: {
    semanticRecall: true,
    // Number of recent messages to include
    lastMessages: 20,
    // Working memory configuration
    workingMemory: {
      enabled: true
    },
    // Thread options
    threads: {
      generateTitle: true
    }
  }
});

// src/mastra/agents/index.ts
var weatherAgent = new Agent2({
  name: "Weather Agent",
  instructions: `
      You are a helpful weather assistant that provides accurate weather information.

      Your primary function is to help users get weather details for specific locations. When responding:
      - Always ask for a location if none is provided
      - If the location name isn\u2019t in English, please translate it
      - If giving a location with multiple parts (e.g. "New York, NY"), use the most relevant part (e.g. "New York")
      - Include relevant details like humidity, wind conditions, and precipitation
      - Keep responses concise but informative

      Use the weatherTool to fetch current weather data.
`,
  model: openai2("gpt-4o"),
  tools: { weatherTool },
  memory
});

// src/mastra/index.ts
var mastra = new Mastra({
  workflows: { weatherWorkflow },
  agents: { weatherAgent },
  logger,
  storage,
  server: {
    apiRoutes: [
      {
        path: "/",
        method: "GET",
        handler: async (c) => {
          const mastra2 = c.get("mastra");
          const agent2 = await mastra2.getAgent("my-agent");
          return c.json({ message: "Hello, world!" });
        }
      }
    ],
    middleware: [
      async (c, next) => {
        c.set("mastra", mastra);
        await next();
      }
    ]
  }
});

// src/services/auth.services.ts
import { parse, stringify } from "superjson";
import { jwtVerify, SignJWT } from "jose";

// src/services/db.services.ts
import {
  teamLocation,
  team,
  user,
  member
} from "@repo/drizzle";
import { and, count, eq as eq2 } from "drizzle-orm";
import "linq-extensions";
var userWithEmailExists = async (email) => {
  const userCount = await db.select({ count: count() }).from(user).where(eq2(user.email, email));
  return userCount.first().count > 0;
};
var createTeamAdminUser = async ({
  userObject,
  teamObject,
  teamLocationObject,
  memberObject
}) => {
  const resp = await db.transaction(async (tx) => {
    const userInsert = await tx.insert(user).values(userObject).returning({ id: user.id });
    const teamInsert = await tx.insert(team).values(teamObject).returning({ id: team.id });
    const memberInsert = await tx.insert(member).values(memberObject).returning({ id: member.id });
    const tmInsert = await tx.insert(teamLocation).values(teamLocationObject).returning({ id: teamLocation.id });
    return !!userInsert.firstOrNull()?.id && !!teamInsert.firstOrNull()?.id && !!tmInsert.firstOrNull()?.id && !!memberInsert.firstOrNull()?.id;
  });
  console.log("team admin user ", resp);
  return resp;
};
var markUserEmailAsVerified = async (authKey, email) => {
  console.log("auth key", authKey, "email", email);
  const resp = await db.update(user).set({ emailVerified: true }).where(and(eq2(user.authKey, authKey), eq2(user.email, email))).returning();
  return resp.first();
};
var getCurrentUserData = async (sub, email) => {
  console.log("auth key", sub, "email", email);
  const currentUserData = await db.query.user.findFirst({
    where: and(eq2(user.authKey, sub), eq2(user.email, email)),
    with: {
      member: true
    }
  });
  return currentUserData;
};

// src/services/auth.services.ts
import { addDays } from "date-fns";
import { Passlock } from "@passlock/node";
import { isWithinExpirationDate, Roles, tryCatch } from "@repo/common";
var { AUTH_SECRET: authSecret, PUBLIC_PASSLOCK_API_KEY: passlockApiKey, PUBLIC_PASSLOCK_TENANCY_ID: passlockTenancyId } = process.env;
var setupAuthSession = async (jwtToken, user3, expires) => {
  if (!jwtToken || !user3) {
    throw new Error("No User nor token data");
  }
  const teamId = user3.teamId;
  const isTeamAdmin = user3.defaultRole === Roles.TeamAdmin;
  const teamConfigIsSetup = !!teamId;
  const sessionData = {
    jwtToken,
    user: user3,
    isAdmin: user3.defaultRole === Roles.Admin,
    teamConfigIsSetup,
    isTeamAdmin,
    expires
  };
  return sessionData;
};
var isAuthenticated = async (accessToken) => {
  if (!accessToken) {
    return { isValid: false, sessionData: null };
  }
  const result = await tryCatch(verifyJWT(accessToken));
  if (result.error) {
    console.error(`token verification failed, logging out : ${result.error}`);
    return { isValid: false, sessionData: null };
  }
  const { payload } = result.data;
  try {
    const tokenExpirationTime = payload?.exp;
    const isWithinExpiration = isWithinExpirationDate(new Date(tokenExpirationTime));
    const accessTokenExpired = !!accessToken && !!tokenExpirationTime && isWithinExpiration;
    if (accessTokenExpired) return { isValid: false, sessionData: null };
    const { userId, email } = payload;
    const userResp = await tryCatch(getAppUser(userId, email));
    if (userResp.error) return { isValid: false, sessionData: null };
    const user3 = userResp.data;
    if (!user3) return { isValid: false, sessionData: null };
    const sessionData = await setupAuthSession(accessToken, user3, new Date(tokenExpirationTime));
    return { isValid: true, sessionData };
  } catch (err) {
    console.error(`token expiration check error: ${stringify(err)}`);
    return { isValid: false, sessionData: null };
  }
};
var verifyJWT = async (token) => {
  try {
    const encoder = new TextEncoder();
    const secretKeyBytes = encoder.encode(authSecret);
    const { payload, protectedHeader } = await jwtVerify(token, secretKeyBytes, {
      algorithms: ["HS256"]
      // Restrict to HS256 algorithm
    });
    return { payload, protectedHeader };
  } catch (error) {
    if (error.toString()?.includes("JWTExpired")) {
      throw new Error("Token has expired");
    }
    console.error(`JWT verification failed: ${error}`);
    throw new Error(error.message);
  }
};
var createSessionToken = async (principal) => {
  const { data: currentUserData, error: currentUserError } = await tryCatch(
    getCurrentUserData(principal.sub, principal.email)
  );
  if (currentUserError) {
    throw new Error(currentUserError.message);
  }
  if (!currentUserData) {
    throw new Error("User not found");
  }
  const { member: member3, ...user3 } = currentUserData;
  if (!user3 || !member3) {
    throw new Error("User or member not found");
  }
  const currentUser = {
    id: user3.id,
    email: user3.email,
    familyName: user3.familyName,
    givenName: user3.givenName,
    defaultRole: user3.defaultRole,
    phoneNumber: user3.phoneNumber ?? void 0,
    idDocumentNumber: user3.idDocumentNumber ?? void 0,
    allowedRoles: parse(user3.roles ?? "[]") ?? [],
    gender: user3.gender,
    profilePhotoUrl: user3.avatarUrl ?? void 0,
    isEnabled: !user3.disabled,
    status: member3?.memberStatus,
    teamId: member3?.teamId,
    teamLocationId: member3?.teamLocationId,
    isPhoneNumberVerified: user3.phoneNumberVerified,
    isUserEmailVerified: user3.emailVerified
  };
  const scopes = ["openid", "email", "profile", "offline_access"];
  const customClaims = {
    loginMethod: principal.authType,
    admin: currentUser.defaultRole === Roles.Admin,
    iat: Math.floor(Date.now() / 1e3),
    scopes,
    email: currentUser.email,
    userId: currentUser.id,
    defaultRole: currentUser.defaultRole,
    allowedRoles: currentUser.allowedRoles,
    teamId: currentUser.teamId,
    teamLocationId: currentUser.teamLocationId
  };
  console.info(`zero auth secret: ${authSecret?.toString()}`);
  const encodedKey = new TextEncoder().encode(authSecret);
  console.info(`encoded key: ${encodedKey}`);
  const newJwt = await new SignJWT(customClaims).setProtectedHeader({ alg: "HS256" }).setIssuedAt().setIssuer("https://spendeed.com").setAudience("https://spendeed.com").setExpirationTime("7d").setSubject(currentUser.id).sign(encodedKey);
  const tokenExpiration = addDays(/* @__PURE__ */ new Date(), 1);
  return { jwtToken: newJwt, user: currentUser, expiration: tokenExpiration };
};
var getAppUser = async (sub, email) => {
  const { data: currentUserData, error: currentUserError } = await tryCatch(
    getCurrentUserData(sub, email)
  );
  if (currentUserError) {
    throw new Error(currentUserError.message);
  }
  if (!currentUserData) {
    throw new Error("User not found");
  }
  const { member: member3, ...user3 } = currentUserData;
  if (!user3 || !member3) {
    throw new Error("User or member not found");
  }
  const currentUser = {
    id: user3.id,
    email: user3.email,
    familyName: user3.familyName,
    givenName: user3.givenName,
    defaultRole: user3.defaultRole,
    phoneNumber: user3.phoneNumber ?? void 0,
    idDocumentNumber: user3.idDocumentNumber ?? void 0,
    allowedRoles: parse(user3.roles ?? "[]") ?? [],
    gender: user3.gender,
    profilePhotoUrl: user3.avatarUrl ?? void 0,
    isEnabled: !user3.disabled,
    status: member3?.memberStatus,
    teamId: member3?.teamId,
    teamLocationId: member3?.teamLocationId,
    isPhoneNumberVerified: user3.phoneNumberVerified,
    isUserEmailVerified: user3.emailVerified
  };
  return currentUser;
};
var passlock = new Passlock({
  tenancyId: passlockTenancyId || "",
  apiKey: passlockApiKey || ""
});

// src/actors/middleware.ts
import { tryCatch as tryCatch2 } from "@repo/common";
var checkConnState = async (c, params) => {
  try {
    const apiKey = params?.["spendeedApiKey"];
    const jwt = params?.["jwtToken"];
    let authData = {};
    if (apiKey) {
      if (apiKey !== process.env.SPENDEEED_API_KEY) {
        throw new Error("Invalid API key");
      }
      authData = {
        ...authData,
        userID: "spendeed-api-key",
        email: "<EMAIL>"
      };
      return { authData };
    }
    if (!jwt) {
      throw new Error("No JWT token provided");
    }
    const result = await tryCatch2(isAuthenticated(jwt));
    if (result.error) {
      throw new Error("invalid token", result.error);
    }
    const { isValid, sessionData } = result.data;
    if (!isValid) {
      throw new Error("invalid token");
    }
    authData = {
      ...authData,
      ...sessionData,
      userID: sessionData?.user?.id
    };
    return { authData };
  } catch (error) {
    c.log.error(error);
    throw new Error("Invalid or expired JWT token");
  }
};

// src/actors/appointment-type-actor.ts
import {
  appointmentType
} from "@repo/drizzle";
var appointmentTypesActor = actor({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create appointment type: ${item.appointmentDetail}`);
      const { authData } = c.conn.state;
      const newAppointmentType = await appointmentType.create(
        item,
        authData,
        c.state
      );
      return newAppointmentType;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update appointment type: ${item.appointmentDetail}`);
      return await appointmentType.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete appointment type: ${item.appointmentDetail}`);
      return await appointmentType.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await appointmentType.getCollection(authData, c.state);
    }
  }
});

// src/actors/appointments-actor.ts
import { actor as actor2 } from "actor-core";
import { appointment } from "@repo/drizzle";
var appointmentsActor = actor2({
  state: {
    appointments: []
  },
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    if (!params["spendeedApiKey"]) params["spendeedApiKey"] = process.env.SPENDED_API_KEY;
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      const { authData } = c.conn.state;
      const newAppointment = await appointment.create(
        item,
        authData,
        c.state
      );
      return newAppointment;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      return await appointment.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      return await appointment.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await appointment.getCollection(authData, c.state);
    }
  }
});

// src/server.helpers.ts
import { TimeSpan } from "@repo/common";
import { safeParse } from "valibot";
import { parse as parse2 } from "superjson";
var validateParams = (schema2, data) => {
  const result = safeParse(schema2, data);
  if (result.success) {
    return { isValid: true, output: result.output };
  }
  return { isValid: false, issues: result.issues };
};

// src/actors/auth-actor.ts
import {
  loginFormSchema,
  Roles as Roles2,
  signUpCodeFormSchema,
  signUpFormSchema,
  TeamMemberStatuses,
  TeamPlans,
  tryCatch as tryCatch3
} from "@repo/common";
import { PasslockError } from "@passlock/node";
import { actor as actor3 } from "actor-core";
import { uuidv7 } from "@kripod/uuidv7";
import { image } from "gravatar-gen";
import { parse as parse3, stringify as stringify2 } from "superjson";
var authActor = actor3({
  state: {
    sessionData: void 0
  },
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  actions: {
    signUp: async (c, params) => {
      try {
        const { isValid, output, issues } = validateParams(signUpFormSchema, params);
        const formData = output;
        if (!isValid) {
          return {
            status: "error",
            text: "Invalid parameters",
            issues: issues?.map((issue) => issue.message).join("; ")
          };
        }
        if (!formData?.token) {
          return { status: "error", text: "Token is required" };
        }
        const principal = await passlock.fetchPrincipal({
          token: formData.token
        });
        if (PasslockError.isError(principal)) {
          c.log.error(`principal error: ${principal.message}`);
          return { status: "error", text: principal.message };
        }
        c.log.info(`principal sub: ${principal.sub}`);
        const {
          authType,
          phoneNumber,
          businessName,
          verificationMethod,
          email,
          familyName,
          givenName
        } = formData;
        c.log.info(`type and Code: ${authType}-${verificationMethod}`);
        const teamId = uuidv7();
        const teamLocationId = uuidv7();
        const avatarUrl = await image(email, {
          size: 200,
          defaultImage: "blank",
          includeExtention: true,
          rating: "g"
        });
        const { data, error } = await tryCatch3(userWithEmailExists(email));
        if (error) {
          c.log.error(`existing userId error: ${error}`);
          return {
            status: "error",
            text: `An error occured while signing up. Please try again`
          };
        }
        if (data) {
          return {
            status: "error",
            text: `An account with this email already exists`
          };
        }
        const userId = uuidv7();
        const userObject = {
          id: userId,
          email,
          familyName,
          givenName,
          displayName: `${givenName} ${familyName}`,
          defaultRole: Roles2.TeamAdmin,
          authKey: principal.sub,
          phoneNumber: phoneNumber ?? null,
          emailVerified: principal.emailVerified,
          locale: "en",
          avatarUrl,
          phoneNumberVerified: false,
          roles: stringify2([`${Roles2.TeamAdmin}`, `${Roles2.TeamMember}`, `${Roles2.User}`])
        };
        const teamObject = {
          id: teamId,
          businessEmail: email,
          teamName: businessName,
          phoneNumber,
          isActive: true,
          currentPlan: TeamPlans.Free
        };
        const memberObject = {
          id: uuidv7(),
          teamId,
          userId,
          teamLocationId,
          memberStatus: TeamMemberStatuses.Active,
          createdAt: /* @__PURE__ */ new Date(),
          updatedAt: /* @__PURE__ */ new Date()
        };
        const teamLocationObject = {
          id: teamLocationId,
          teamId,
          createdBy: userId,
          teamLocationAdmin: userId,
          isActive: true,
          locationName: `HQ`,
          isDefault: true,
          locationEmail: email,
          locationPhoneNumber: phoneNumber
        };
        const { data: createTeamAdminData, error: createTeamAdminError } = await tryCatch3(
          createTeamAdminUser({
            userObject,
            teamObject,
            teamLocationObject,
            memberObject
          })
        );
        if (createTeamAdminError || !createTeamAdminData) {
          c.log.error(`create team admin error: ${createTeamAdminError}`);
          return { status: "error", text: "Failed to create user during sign up" };
        }
        switch (`${authType}-${verificationMethod}`) {
          case "passkey-code":
            return {
              status: "success",
              text: "Sign Up email sent. Please check your email for Code",
              data: { authType: { type: "code" }, userId }
            };
          case "passkey-link":
            return {
              status: "success",
              text: "Sign Up email sent. Please check your mail and click on the verification link",
              data: { authType: { type: "link" }, userId }
            };
          default:
            return {
              status: "error",
              text: "Invalid auth type"
            };
        }
      } catch (error) {
        c.log.error(`signup error: ${error}`);
        return {
          status: "error",
          text: `And error occured while registering user: ${error.message}`
        };
      }
    },
    login: async (c, params) => {
      try {
        const {
          isValid,
          output: formData,
          issues
        } = validateParams(loginFormSchema, params);
        if (!isValid || !formData) {
          return {
            status: "error",
            text: "Invalid parameters",
            issues: issues?.map((issue) => issue.message).join("; ")
          };
        }
        const principal = await passlock.fetchPrincipal({
          token: formData.token
        });
        if (PasslockError.isError(principal)) {
          c.log.error(`principal verification error: ${principal.message}`);
          return { status: "error", text: principal.message };
        }
        const sessionTokenRequest = await tryCatch3(createSessionToken(principal));
        if (sessionTokenRequest.error) {
          c.log.error(`session token error: ${sessionTokenRequest.error?.message}`);
          return { status: "error", text: sessionTokenRequest.error?.message };
        }
        const { user: currentUser, expiration, jwtToken } = sessionTokenRequest.data;
        c.log.info("creating auth session");
        const { data: sessionData, error: setupAuthError } = await tryCatch3(
          setupAuthSession(jwtToken, currentUser, expiration)
        );
        if (setupAuthError) {
          c.log.error(`setup session error: ${setupAuthError}`);
          return { status: "error", text: "An error occured.Please try again" };
        }
        c.log.info("logged in");
        c.state.sessionData = sessionData;
        return {
          status: "success",
          text: "Login successful",
          data: { session: sessionData }
        };
      } catch (error) {
        c.log.error(`login error: ${error}`);
        return {
          status: "error",
          text: "An error occured during login. Please try again"
        };
      }
    },
    verifyEmail: async (c, params) => {
      try {
        const {
          isValid,
          output: formData,
          issues
        } = validateParams(signUpCodeFormSchema, params);
        if (!isValid || !formData) {
          return {
            status: "error",
            text: "Invalid parameters",
            issues: issues?.map((issue) => issue.message).join("; ")
          };
        }
        if (!formData.token) {
          return {
            text: "Email Verification Failed",
            status: "error"
          };
        }
        const principal = parse3(formData.principal ?? "{}");
        if (!principal.emailVerified) {
          c.log.error(`principal email verification failed: ${principal}`);
          return {
            text: "Email Verification Failed",
            status: "error"
          };
        }
        const { data, error } = await tryCatch3(
          markUserEmailAsVerified(principal.sub, principal.email)
        );
        if (error || !data?.id) {
          c.log.error(`mark user as verified error: ${error}`);
          return {
            status: "error",
            text: "An error occured during user verification. please try again"
          };
        }
        const sessionResp = await tryCatch3(createSessionToken(principal));
        if (sessionResp.error || !sessionResp.data?.jwtToken) {
          c.log.error(`create session token error: ${sessionResp.error}`);
          return {
            text: "An error occured while verifying email",
            status: "error"
          };
        }
        const { jwtToken, user: currentUser, expiration } = sessionResp.data;
        c.log.info("creating auth session");
        const { data: sessionData, error: authSessionError } = await tryCatch3(
          setupAuthSession(jwtToken, currentUser, expiration)
        );
        if (authSessionError) {
          c.log.error("setup session error", authSessionError);
          return {
            status: "error",
            text: "An error occured during Code Verification.Please try again"
          };
        }
        c.state.sessionData = sessionData;
        return {
          text: "Code verified successfully",
          status: "success",
          data: { session: sessionData }
        };
      } catch (exception) {
        c.log.error("code verification exception", exception);
        return {
          status: "error",
          text: "An error occured during Code Verification.Please try again"
        };
      }
    },
    logOut: async (c) => {
      try {
        c.state.sessionData = void 0;
        return {
          status: "success",
          text: "Logout successful"
        };
      } catch (error) {
        c.log.error(`logout error: ${error}`);
        return {
          status: "error",
          text: "An error occured during logout. Please try again"
        };
      }
    }
  }
});

// src/actors/calendar-blocks-actor.ts
import { actor as actor4 } from "actor-core";
import {
  calendarBlock
} from "@repo/drizzle";
var calendarBlocksActor = actor4({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create calendar block: ${item.id}`);
      const { authData } = c.conn.state;
      const newCalendarBlock = await calendarBlock.create(item, authData, c.state);
      return newCalendarBlock;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update calendar block: ${item.id}`);
      return await calendarBlock.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete calendar block: ${item.id}`);
      return await calendarBlock.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await calendarBlock.getCollection(authData, c.state);
    }
  }
});

// src/actors/gender-list-actor.ts
import { actor as actor5 } from "actor-core";
import { gender } from "@repo/drizzle";
var genderListActor = actor5({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await gender.getCollection(authData, c.state);
    }
  }
});

// src/actors/member-invite-actor.ts
import { actor as actor6 } from "actor-core";
import { memberInvite } from "@repo/drizzle";
var memberInviteActor = actor6({
  state: {},
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create member invite: ${item.inviteeEmail}`);
      const { authData } = c.conn.state;
      const newMemberInvite = await memberInvite.create(item, authData, c.state);
      return newMemberInvite;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update member invite: ${item.inviteeEmail}`);
      return await memberInvite.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete member invite: ${item.inviteeEmail}`);
      return await memberInvite.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await memberInvite.getCollection(authData, c.state);
    }
  }
});

// src/actors/members-actor.ts
import { actor as actor7 } from "actor-core";
import { member as member2 } from "@repo/drizzle";
var membersActor = actor7({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create member: ${item.id}`);
      const { authData } = c.conn.state;
      const newMember = await member2.create(item, authData, c.state);
      return newMember;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update member: ${item.id}`);
      return await member2.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete member: ${item.id}`);
      return await member2.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await member2.getCollection(authData, c.state);
    }
  }
});

// src/actors/roles-actor.ts
import { actor as actor8 } from "actor-core";
import { role } from "@repo/drizzle";
var rolesListActor = actor8({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await role.getCollection(authData, c.state);
    }
  }
});

// src/actors/storage-bucket-actor.ts
import { actor as actor9 } from "actor-core";
import {
  storageBucket
} from "@repo/drizzle";
var storageBucketActor = actor9({
  state: {},
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create storage bucket: ${item.id}`);
      const { authData } = c.conn.state;
      const newStorageBucket = await storageBucket.create(item, authData, c.state);
      return newStorageBucket;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update storage bucket: ${item.id}`);
      return await storageBucket.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete storage bucket: ${item.id}`);
      return await storageBucket.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await storageBucket.getCollection(authData, c.state);
    }
  }
});

// src/actors/storage-files-actor.ts
import { actor as actor10 } from "actor-core";
import { storageFile } from "@repo/drizzle";
var storageFilesActor = actor10({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create storage file: ${item.name}`);
      const { authData } = c.conn.state;
      const newStorageFile = await storageFile.create(item, authData, c.state);
      return newStorageFile;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update storage file: ${item.name}`);
      return await storageFile.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete storage file: ${item.name}`);
      return await storageFile.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await storageFile.getCollection(authData, c.state);
    }
  }
});

// src/actors/team-actor.ts
import { actor as actor11 } from "actor-core";
import { team as team2 } from "@repo/drizzle";
var teamActor = actor11({
  state: {},
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params, request }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create team: ${item.teamName}`);
      const { authData } = c.conn.state;
      const newTeam = await team2.create(item, authData, c.state);
      return newTeam;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update team: ${item.teamName}`);
      return await team2.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete team: ${item.teamName}`);
      return await team2.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      const teamId = authData?.teamId;
      const currentTeam = await team2.findById(teamId, authData);
      return [currentTeam];
    }
  }
});

// src/actors/team-bank-detail-actor.ts
import { actor as actor12 } from "actor-core";
import {
  teamBankDetail
} from "@repo/drizzle";
var teamBankDetailActor = actor12({
  state: {},
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create team bank detail: ${item.bankName}`);
      const { authData } = c.conn.state;
      const newTeamBankDetail = await teamBankDetail.create(item, authData, c.state);
      return newTeamBankDetail;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update team bank detail: ${item.bankName}`);
      return await teamBankDetail.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete team bank detail: ${item.bankName}`);
      return await teamBankDetail.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await teamBankDetail.getCollection(authData, c.state);
    }
  }
});

// src/actors/team-locations-actor.ts
import { actor as actor13 } from "actor-core";
import { teamLocation as teamLocation2 } from "@repo/drizzle";
var teamLocationsActor = actor13({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create team location: ${item.locationName}`);
      const { authData } = c.conn.state;
      const newTeamLocation = await teamLocation2.create(item, authData, c.state);
      return newTeamLocation;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update team location: ${item.locationName}`);
      return await teamLocation2.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete team location: ${item.locationName}`);
      return await teamLocation2.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await teamLocation2.getCollection(authData, c.state);
    }
  }
});

// src/actors/team-payment-provider-actor.ts
import { actor as actor14 } from "actor-core";
import { teamPaymentProvider } from "@repo/drizzle";
var teamPaymentProviderActor = actor14({
  state: {},
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create team payment provider: ${item.providerName}`);
      const { authData } = c.conn.state;
      const newTeamPaymentProvider = await teamPaymentProvider.create(
        item,
        authData,
        c.state
      );
      return newTeamPaymentProvider;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update team payment provider: ${item.providerName}`);
      return await teamPaymentProvider.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete team payment provider: ${item.providerName}`);
      return await teamPaymentProvider.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await teamPaymentProvider.getCollection(authData, c.state);
    }
  }
});

// src/actors/time-zone-actor.ts
import { actor as actor15 } from "actor-core";
import { timeZone } from "@repo/drizzle";
var timeZonesActor = actor15({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await timeZone.getCollection(authData, c.state);
    }
  }
});

// src/actors/user-actor.ts
import { actor as actor16 } from "actor-core";
import { user as user2 } from "@repo/drizzle";
var usersActor = actor16({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create user: ${item.displayName}`);
      const { authData } = c.conn.state;
      const newUser = await user2.create(item, authData, c.state);
      return newUser;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update user: ${item.displayName}`);
      return await user2.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete user: ${item.displayName}`);
      return await user2.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await user2.getCollection(authData, c.state);
    }
  }
});

// src/actors/user-role-actor.ts
import { actor as actor17 } from "actor-core";
import { userRole } from "@repo/drizzle";
var stateObj = {};
var userRoleActor = actor17({
  state: stateObj,
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create user role: ${item.role}`);
      const { authData } = c.conn.state;
      const newUserRole = await userRole.create(item, authData, c.state);
      return newUserRole;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update user role: ${item.role}`);
      return await userRole.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete user role: ${item.role}`);
      return await userRole.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await userRole.getCollection(authData, c.state);
    }
  }
});

// src/actors/workday-setting-actor.ts
import { actor as actor18 } from "actor-core";
import { workdaySetting } from "@repo/drizzle";
var stateObj2 = {};
var workdaySettingActor = actor18({
  state: stateObj2,
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create workday setting: ${item.weekday}`);
      const { authData } = c.conn.state;
      const newWorkdaySetting = await workdaySetting.create(item, authData, c.state);
      return newWorkdaySetting;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update workday setting: ${item.weekday}`);
      return await workdaySetting.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete workday setting: ${item.weekday}`);
      return await workdaySetting.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await workdaySetting.getCollection(authData, c.state);
    }
  }
});

// src/index.ts
initSuperJson();
extendDrizzleTable(db);
var app = setup({
  actors: {
    auth: authActor,
    appointmentsActor,
    teamActor,
    calendarBlocksActor,
    membersActor,
    genderListActor,
    appointmentTypesActor,
    storageBucketActor,
    storageFilesActor,
    userRoleActor,
    rolesListActor,
    usersActor,
    teamPaymentProviderActor,
    timeZonesActor,
    workdaySettingActor,
    teamLocationsActor,
    teamBankDetailActor,
    memberInviteActor
  },
  cors: { origin: "*" }
  // Configure CORS for your production domains in production
});
export {
  app
};
//# sourceMappingURL=data:application/json;base64,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
