import {
	eq,
	ne,
	gt,
	gte,
	lt,
	lte,
	like,
	ilike,
	inArray,
	notInArray,
	isNull,
	isNotNull,
	and,
	or,
	between,
	type SQL
} from 'drizzle-orm'
import 'linq-extensions'
import { ActorEvents, PostgresEvents, rbac, type ActionType, type paramsType } from '@repo/common'

// Types for dynamic where clauses (same as drizzle-helper)
export type WhereOperator =
	| 'eq'
	| 'ne'
	| 'gt'
	| 'gte'
	| 'lt'
	| 'lte'
	| 'like'
	| 'ilike'
	| 'in'
	| 'notIn'
	| 'isNull'
	| 'isNotNull'
	| 'between'

export interface WhereCondition {
	field: string
	operator: WhereOperator
	value?: any
	values?: any[] // For 'in', 'notIn', 'between' operators
}

export interface WhereClause {
	conditions: WhereCondition[]
	logic?: 'AND' | 'OR' // How to combine multiple conditions
}

// Actor context interface for type safety
export interface ActorContext {
	state: any[]
	broadcast: (event: string, data?: any) => void
	log: {
		info: (message: string) => void
		error: (message: string) => void
		warn: (message: string) => void
	}
	conn: {
		state: {
			authData: {
				userId: string
				teamId: string
				defaultRole: string
				roles: string[]
			}
		}
	}
}

// Helper function to build where clauses dynamically for state filtering
function buildStateFilter<T>(whereClause: WhereClause): (item: T) => boolean {
	if (!whereClause.conditions || whereClause.conditions.length === 0) {
		return () => true
	}

	return (item: T) => {
		const results = whereClause.conditions.map(condition => {
			const fieldValue = (item as any)[condition.field]

			switch (condition.operator) {
				case 'eq':
					return fieldValue === condition.value
				case 'ne':
					return fieldValue !== condition.value
				case 'gt':
					return fieldValue > condition.value
				case 'gte':
					return fieldValue >= condition.value
				case 'lt':
					return fieldValue < condition.value
				case 'lte':
					return fieldValue <= condition.value
				case 'like':
					return typeof fieldValue === 'string' && fieldValue.includes(condition.value)
				case 'ilike':
					return typeof fieldValue === 'string' &&
						fieldValue.toLowerCase().includes(condition.value.toLowerCase())
				case 'in':
					return condition.values && condition.values.includes(fieldValue)
				case 'notIn':
					return condition.values && !condition.values.includes(fieldValue)
				case 'isNull':
					return fieldValue == null
				case 'isNotNull':
					return fieldValue != null
				case 'between':
					return condition.values && condition.values.length === 2 &&
						fieldValue >= condition.values[0] && fieldValue <= condition.values[1]
				default:
					throw new Error(`Unsupported operator: ${condition.operator}`)
			}
		})

		// Combine results with AND or OR
		return whereClause.logic === 'OR'
			? results.some(result => result)
			: results.every(result => result)
	}
}

// Convenience functions for common query patterns
export function createWhereClause(conditions: WhereCondition[], logic: 'AND' | 'OR' = 'AND'): WhereClause {
	return { conditions, logic }
}

export function createCondition(field: string, operator: WhereOperator, value?: any, values?: any[]): WhereCondition {
	return { field, operator, value, values }
}

// Permission checking function
export const checkPermission = async (
	action: ActionType,
	payload: Record<string, any>,
	authData: any
): Promise<boolean> => {
	try {
		const params: paramsType = {
			payload,
			authData
		}

		// Use the user's role to check permission
		const role = authData.defaultRole
		const hasPermission = await rbac.can(role, action, params)
		return hasPermission
	} catch (error) {
		console.error('Permission check failed:', error)
		return false
	}
}

// Create a new record in state
export async function createStateRecord<T extends { id: string }>(
	c: ActorContext,
	data: Partial<T>,
	rbacAction: ActionType
): Promise<{ success: boolean; data?: T; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, data, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		// Generate ID if not provided
		const newRecord = {
			...data,
			id: data.id || crypto.randomUUID(),
			createdAt: new Date(),
			updatedAt: new Date()
		} as T

		// Add to state
		c.state.push(newRecord)

		// Broadcast events
		c.broadcast(ActorEvents.ItemCreated, newRecord)
		c.broadcast(PostgresEvents.ItemCreated, newRecord)

		c.log.info(`Created record with ID: ${newRecord.id}`)
		return { success: true, data: newRecord }
	} catch (error) {
		c.log.error(`Create record error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Get all records from state (original function for backward compatibility)
export async function getStateRecords<T>(
	c: ActorContext,
	rbacAction: ActionType
): Promise<{ success: boolean; data?: T[]; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		return { success: true, data: c.state as T[] }
	} catch (error) {
		c.log.error(`Get records error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Enhanced function with dynamic where clauses
export async function getStateRecordsWithWhere<T>(
	c: ActorContext,
	rbacAction: ActionType,
	whereClause?: WhereClause
): Promise<{ success: boolean; data?: T[]; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		let records = c.state as T[]

		// Apply where clause if provided
		if (whereClause) {
			const filter = buildStateFilter<T>(whereClause)
			records = records.filter(filter)
		}

		return { success: true, data: records }
	} catch (error) {
		c.log.error(`Get records with where error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Get a single record by ID
export async function getStateRecordById<T extends { id: string }>(
	c: ActorContext,
	id: string,
	rbacAction: ActionType
): Promise<{ success: boolean; data?: T; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, { id }, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		const record = (c.state as T[]).find(item => item.id === id)

		return record
			? { success: true, data: record }
			: { success: false, error: "Record not found" }
	} catch (error) {
		c.log.error(`Get record by ID error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Update a record by ID
export async function updateStateRecord<T extends { id: string }>(
	c: ActorContext,
	data: Partial<T> & { id: string },
	rbacAction: ActionType
): Promise<{ success: boolean; data?: T; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, data, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		const index = (c.state as T[]).findIndex(item => item.id === data.id)

		if (index === -1) {
			return { success: false, error: "Record not found" }
		}

		// Update the record
		const updatedRecord = {
			...c.state[index],
			...data,
			updatedAt: new Date()
		} as T

		c.state[index] = updatedRecord

		// Broadcast events
		c.broadcast(ActorEvents.ItemUpdated, updatedRecord)
		c.broadcast(PostgresEvents.ItemUpdated, updatedRecord)

		c.log.info(`Updated record with ID: ${data.id}`)
		return { success: true, data: updatedRecord }
	} catch (error) {
		c.log.error(`Update record error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Delete a record by ID
export async function deleteStateRecord<T extends { id: string }>(
	c: ActorContext,
	id: string,
	rbacAction: ActionType
): Promise<{ success: boolean; data?: T; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, { id }, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		const index = (c.state as T[]).findIndex(item => item.id === id)

		if (index === -1) {
			return { success: false, error: "Record not found" }
		}

		// Remove the record
		const deletedRecord = c.state.splice(index, 1)[0] as T

		// Broadcast events
		c.broadcast(ActorEvents.ItemDeleted, deletedRecord)
		c.broadcast(PostgresEvents.ItemDeleted, deletedRecord)

		c.log.info(`Deleted record with ID: ${id}`)
		return { success: true, data: deletedRecord }
	} catch (error) {
		c.log.error(`Delete record error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Bulk operations
export async function createMultipleStateRecords<T extends { id: string }>(
	c: ActorContext,
	dataArray: Partial<T>[],
	rbacAction: ActionType
): Promise<{ success: boolean; data?: T[]; error?: string }> {
	try {
		const { authData } = c.conn.state
		const hasPermission = await checkPermission(rbacAction, {}, authData)

		if (!hasPermission) {
			throw new Error(`Permission denied: User does not have ${rbacAction} permission`)
		}

		const newRecords: T[] = []

		for (const data of dataArray) {
			const newRecord = {
				...data,
				id: data.id || crypto.randomUUID(),
				createdAt: new Date(),
				updatedAt: new Date()
			} as T

			c.state.push(newRecord)
			newRecords.push(newRecord)

			// Broadcast individual item events
			c.broadcast(ActorEvents.ItemCreated, newRecord)
			c.broadcast(PostgresEvents.ItemCreated, newRecord)
		}

		// Broadcast collection updated event
		c.broadcast(PostgresEvents.CollectionUpdated, { action: 'bulk_create', count: newRecords.length })

		c.log.info(`Created ${newRecords.length} records`)
		return { success: true, data: newRecords }
	} catch (error) {
		c.log.error(`Bulk create error: ${(error as Error).message}`)
		return { success: false, error: (error as Error).message }
	}
}

// Get records by team ID (common pattern)
export async function getStateRecordsByTeamId<T extends { teamId: string }>(
	c: ActorContext,
	teamId: string,
	rbacAction: ActionType
): Promise<{ success: boolean; data?: T[]; error?: string }> {
	const whereClause = createWhereClause([
		createCondition('teamId', 'eq', teamId)
	])

	return await getStateRecordsWithWhere<T>(c, rbacAction, whereClause)
}

// Search records with text (common pattern)
export async function searchStateRecords<T>(
	c: ActorContext,
	searchField: string,
	searchTerm: string,
	rbacAction: ActionType
): Promise<{ success: boolean; data?: T[]; error?: string }> {
	const whereClause = createWhereClause([
		createCondition(searchField, 'ilike', `%${searchTerm}%`)
	])

	return await getStateRecordsWithWhere<T>(c, rbacAction, whereClause)
}
