import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type RoleDbType, role } from '@repo/drizzle'
import type { ConnState } from '../server.helpers'

export const rolesListActor = actor({
	state: [] as RoleDbType[],
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		getCollection: async (c): Promise<RoleDbType[]> => {
			const { authData } = c.conn.state as ConnState
			return await (role as any).getCollection(authData, c.state)
		}
	}
})
