import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { appointment, type AppointmentDbType, type AppointmentInsertDbType } from '@repo/drizzle'
import type { AuthData, ConnState } from '../utils/server.helpers'

export const appointmentsActor = actor({
	state: {
		appointments: [] as AppointmentDbType[]
	},
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params }: { params: any }): Promise<ConnState> => {
		if (!params['spendeedApiKey']) params['spendeedApiKey'] = process.env.SPENDED_API_KEY
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<AppointmentInsertDbType> => {
			const { authData } = c.conn.state as { authData: AuthData }
			const newAppointment: AppointmentInsertDbType = await (appointment as any).create(
				item,
				authData,
				c.state
			)
			return newAppointment
		},
		update: async (c, item): Promise<AppointmentInsertDbType> => {
			const { authData } = c.conn.state as ConnState
			return await (appointment as any).update(item.id, item, authData, c.state)
		},
		delete: async (c, item): Promise<boolean> => {
			const { authData } = c.conn.state as ConnState
			return await (appointment as any).delete(item.id, authData, c.state)
		},
		getCollection: async (c): Promise<AppointmentDbType[]> => {
			const { authData } = c.conn.state as ConnState
			return await (appointment as any).getCollection(authData, c.state)
		}
	}
})
