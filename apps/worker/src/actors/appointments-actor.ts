import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type AppointmentDbType, type AppointmentInsertDbType } from '@repo/drizzle'
import type { AuthData, ConnState } from '../utils/server.helpers'
import { stateHelper } from '../utils'
import { ActionTypes } from '@repo/common'

export const appointmentsActor = actor({
	state: [] as AppointmentDbType[],
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx,
		uniqueFields: ['id'],
		compositeUniqueFields: [['clientId', 'startTime', 'endTime']]
	}),
	createConnState: async (c, { params }: { params: any }): Promise<ConnState> => {
		if (!params['spendeedApiKey']) params['spendeedApiKey'] = process.env.SPENDED_API_KEY
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<AppointmentInsertDbType | undefined> => {
			c.log.info(`create appointment: ${item.id}`)
			// Note: RBAC permission checking should be handled at a higher level
			const { data: newAppointment, error: createAppointmentError } = await stateHelper.createStateRecord<AppointmentDbType>(c, item, ActionTypes.manageAppointments)
			if (createAppointmentError) {
				c.log.error(`create appointment error: ${createAppointmentError}`)
				return undefined
			}
			return newAppointment as AppointmentDbType
		},
		update: async (c, item): Promise<AppointmentInsertDbType | undefined> => {
			c.log.info(`update appointment: ${item.id}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.updateStateRecord<AppointmentDbType>(c, item, ActionTypes.manageAppointments)
			if (!resp.success) {
				c.log.error(`update appointment error: ${resp.error}`)
				return undefined
			}
			return resp.data as AppointmentDbType
		},
		delete: async (c, item): Promise<boolean> => {
			c.log.info(`delete appointment: ${item.id}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.deleteStateRecord<AppointmentDbType>(c, item.id, ActionTypes.manageAppointments)
			if (resp.error) {
				c.log.error(`delete appointment error: ${resp.error}`)
			}
			return resp.success
		},
		getCollection: async (c): Promise<AppointmentDbType[]> => {
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.getStateRecords<AppointmentDbType>(c, ActionTypes.readAppointments)
			if (!resp.success) {
				c.log.error(`get appointments error: ${resp.error}`)
				return []
			}
			return resp.data || []
		}
	}
})
