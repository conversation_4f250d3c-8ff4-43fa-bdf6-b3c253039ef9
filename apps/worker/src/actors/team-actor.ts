import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type TeamDbType, type TeamInsertDbType, team } from '@repo/drizzle'
import type { ConnState } from '../server.helpers'

export const teamActor = actor({
	state: {} as TeamDbType,
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params, request }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<TeamInsertDbType> => {
			c.log.info(`create team: ${item.teamName}`)
			const { authData } = c.conn.state as ConnState
			const newTeam = await (team as any).create(item, authData, c.state)
			return newTeam
		},
		update: async (c, item): Promise<TeamInsertDbType> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`update team: ${item.teamName}`)
			return await (team as any).update(item.id, item, authData, c.state)
		},
		delete: async (c, item): Promise<boolean> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`delete team: ${item.teamName}`)
			return await (team as any).delete(item.id, authData, c.state)
		},
		getCollection: async (c): Promise<TeamDbType[]> => {
			const { authData } = c.conn.state as ConnState
			const teamId = authData?.teamId
			const currentTeam = await (team as any).findById(teamId, authData)
			return [currentTeam]
		}
	}
})
