import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type TeamDbType, type TeamInsertDbType } from '@repo/drizzle'
import type { ConnState } from '../utils/server.helpers'
import { stateHelper } from '../utils'
import { ActionTypes } from '@repo/common'

export const teamActor = actor({
	state: [] as TeamDbType[],
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx,
		uniqueFields: ['teamName', 'businessEmail'],
		compositeUniqueFields: [['teamName', 'businessEmail']]
	}),
	createConnState: async (c, { params, request }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<TeamInsertDbType | undefined> => {

			c.log.info(`create team: ${item.teamName}`)
			// Note: RBAC permission checking should be handled at a higher level
			const { data: newTeam, error: createTeamError } = await stateHelper.createStateRecord<TeamDbType>(c, item, ActionTypes.manageTeam)
			if (createTeamError) {
				c.log.error(`create team error: ${createTeamError}`)
				return undefined
			}
			return newTeam as TeamDbType
		},
		update: async (c, item): Promise<TeamInsertDbType | undefined> => {
			c.log.info(`update team: ${item.teamName}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await updateRecord<TeamInsertDbType>('team', item)
			if (!resp.success) {
				c.log.error(`update team error: ${resp.error}`)
				return undefined
			}
			return resp.data as TeamDbType
		},
		delete: async (c, item): Promise<boolean> => {
			c.log.info(`delete team: ${item.teamName}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await deleteRecord<TeamInsertDbType>('team', item)
			if (resp.error) {
				c.log.error(`delete team error: ${resp.error}`)
			}
			return resp.success
		},
		getCollection: async (c): Promise<TeamDbType | undefined> => {
			const { authData } = c.conn.state as ConnState
			const teamId = authData?.teamId
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await getRecordById<TeamDbType>('team', teamId)
			if (!resp.success) {
				c.log.error(`get team error: ${resp.error}`)
				return undefined
			}
			return resp.data
		}
	}
})
