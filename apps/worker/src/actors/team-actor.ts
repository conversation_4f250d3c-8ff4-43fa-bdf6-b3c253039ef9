import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type TeamDbType, type TeamInsertDbType, team } from '@repo/drizzle'
import type { ConnState } from '../server.helpers'
import { createRecord, deleteRecord, getRecordById, getRecords, updateRecord } from '../drizzle-helper'
import { ActionTypes } from '@repo/common'

export const teamActor = actor({
	state: [] as TeamDbType[],
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params, request }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<TeamInsertDbType | undefined> => {
			c.log.info(`create team: ${item.teamName}`)
			const { authData } = c.conn.state as ConnState
			const { data: newTeam,
				error: createTeamError
			} = await createRecord<TeamInsertDbType>('team', item, ActionTypes.manageTeam, authData)
			if (createTeamError) {
				c.log.error(`create team error: ${createTeamError}`)
				return undefined
			}
			return newTeam as TeamDbType
		},
		update: async (c, item): Promise<TeamInsertDbType | undefined> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`update team: ${item.teamName}`)
			const resp = await updateRecord<TeamInsertDbType>('team', ActionTypes.manageTeam, authData, item)
			if (!resp.success) {
				c.log.error(`update team error: ${resp.error}`)
				return undefined
			}
			return resp.data as TeamDbType
		},
		delete: async (c, item): Promise<boolean> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`delete team: ${item.teamName}`)
			const resp = await deleteRecord<TeamInsertDbType>('team', ActionTypes.manageTeam, authData, item)
			if (resp.error) {
				c.log.error(`delete team error: ${resp.error}`)
			}
			return resp.success
		},
		getCollection: async (c): Promise<TeamDbType | undefined> => {
			const { authData } = c.conn.state as ConnState
			const teamId = authData?.teamId
			const resp = await getRecordById<TeamDbType>('team', ActionTypes.readTeam, authData, teamId)
			if (!resp.success) {
				c.log.error(`get team error: ${resp.error}`)
				return undefined
			}
			return resp.data
		}
	}
})
