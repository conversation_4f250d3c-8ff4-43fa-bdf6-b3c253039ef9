import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type TeamLocationInsertDbType, teamLocation } from '@repo/drizzle'
import type { ConnState } from '../utils/server.helpers'

export const teamLocationsActor = actor({
	state: [] as TeamLocationInsertDbType[],
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<TeamLocationInsertDbType> => {
			c.log.info(`create team location: ${item.locationName}`)
			const { authData } = c.conn.state as ConnState
			const newTeamLocation = await (teamLocation as any).create(item, authData, c.state)
			return newTeamLocation
		},
		update: async (c, item): Promise<TeamLocationInsertDbType> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`update team location: ${item.locationName}`)
			return await (teamLocation as any).update(item.id, item, authData, c.state)
		},
		delete: async (c, item): Promise<boolean> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`delete team location: ${item.locationName}`)
			return await (teamLocation as any).delete(item.id, authData, c.state)
		},
		getCollection: async (c): Promise<TeamLocationInsertDbType[]> => {
			const { authData } = c.conn.state as ConnState
			return await (teamLocation as any).getCollection(authData, c.state)
		}
	}
})
