import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type TeamLocationInsertDbType } from '@repo/drizzle'
import type { ConnState } from '../utils/server.helpers'
import { stateHelper } from '../utils'
import { ActionTypes } from '@repo/common'

export const teamLocationsActor = actor({
	state: [] as TeamLocationInsertDbType[],
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx,
		uniqueFields: ['id', 'locationName'],
		compositeUniqueFields: [['teamId', 'locationName']]
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<TeamLocationInsertDbType | undefined> => {
			c.log.info(`create team location: ${item.locationName}`)
			// Note: RBAC permission checking should be handled at a higher level
			const { data: newTeamLocation, error: createTeamLocationError } = await stateHelper.createStateRecord<TeamLocationInsertDbType>(c, item, ActionTypes.manageTeamLocations)
			if (createTeamLocationError) {
				c.log.error(`create team location error: ${createTeamLocationError}`)
				return undefined
			}
			return newTeamLocation as TeamLocationInsertDbType
		},
		update: async (c, item): Promise<TeamLocationInsertDbType | undefined> => {
			c.log.info(`update team location: ${item.locationName}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.updateStateRecord<TeamLocationInsertDbType>(c, item, ActionTypes.manageTeamLocations)
			if (!resp.success) {
				c.log.error(`update team location error: ${resp.error}`)
				return undefined
			}
			return resp.data as TeamLocationInsertDbType
		},
		delete: async (c, item): Promise<boolean> => {
			c.log.info(`delete team location: ${item.locationName}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.deleteStateRecord<TeamLocationInsertDbType>(c, item.id, ActionTypes.manageTeamLocations)
			if (resp.error) {
				c.log.error(`delete team location error: ${resp.error}`)
			}
			return resp.success
		},
		getCollection: async (c): Promise<TeamLocationInsertDbType[]> => {
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.getStateRecords<TeamLocationInsertDbType>(c, ActionTypes.readTeamLocations)
			if (!resp.success) {
				c.log.error(`get team locations error: ${resp.error}`)
				return []
			}
			return resp.data || []
		}
	}
})
