import { mastra } from '../mastra'
import { validateParams, type PasslockPrincipal } from '../utils/server.helpers'
import {
	createSessionToken,
	createTeamAdminUser,
	markUserEmailAsVerified,
	passlock,
	setupAuthSession,
	userWithEmailExists
} from '../services'
import {
	loginFormSchema,
	Roles,
	signUpCodeFormSchema,
	signUpFormSchema,
	TeamMemberStatuses,
	TeamPlans,
	tryCatch,
	type ISessionData,
	type LoginFormType,
	type SignUpCodeFormType,
	type SignUpFormType
} from '@repo/common'
import { PasslockError } from '@passlock/node'
import { actor, ActorDefinition, type ActionContextOf } from 'actor-core'
import { uuidv7 } from '@kripod/uuidv7'
import type {
	UserInsertDbType,
	TeamInsertDbType,
	TeamLocationInsertDbType,
	MemberInsertDbType
} from '@repo/drizzle'
import { image } from 'gravatar-gen'
import { parse, stringify } from 'superjson'

type AuthStateType = {
	sessionData: ISessionData | undefined
}

export const authActor = actor({
	state: {
		sessionData: undefined
	} as AuthStateType,
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	actions: {
		signUp: async (c, params: SignUpFormType) => {
			try {
				const { isValid, output, issues } = validateParams(signUpFormSchema, params)
				const formData = output as SignUpFormType
				if (!isValid) {
					return {
						status: 'error',
						text: 'Invalid parameters',
						issues: issues?.map((issue) => issue.message).join('; ')
					}
				}
				if (!formData?.token) {
					return { status: 'error', text: 'Token is required' }
				}
				const principal: PasslockPrincipal | PasslockError = await passlock.fetchPrincipal({
					token: formData.token
				})
				if (PasslockError.isError(principal)) {
					c.log.error(`principal error: ${principal.message}`)
					return { status: 'error', text: principal.message }
				}
				c.log.info(`principal sub: ${principal.sub}`)
				const {
					authType,
					phoneNumber,
					businessName,
					verificationMethod,
					email,
					familyName,
					givenName
				} = formData
				c.log.info(`type and Code: ${authType}-${verificationMethod}`)

				const teamId = uuidv7()
				const teamLocationId = uuidv7()
				const avatarUrl = await image(email!, {
					size: 200,
					defaultImage: 'blank',
					includeExtention: true,
					rating: 'g'
				})
				const { data, error } = await tryCatch(userWithEmailExists(email!))
				if (error) {
					c.log.error(`existing userId error: ${error}`)
					return {
						status: 'error',
						text: `An error occured while signing up. Please try again`
					}
				}
				if (data) {
					return {
						status: 'error',
						text: `An account with this email already exists`
					}
				}
				const userId = uuidv7()

				const userObject: UserInsertDbType = {
					id: userId,
					email,
					familyName: familyName!,
					givenName: givenName!,
					displayName: `${givenName} ${familyName}`,
					defaultRole: Roles.TeamAdmin,
					authKey: principal.sub,
					phoneNumber: phoneNumber ?? null,
					emailVerified: principal.emailVerified,
					locale: 'en',
					avatarUrl,
					phoneNumberVerified: false,
					roles: stringify([`${Roles.TeamAdmin}`, `${Roles.TeamMember}`, `${Roles.User}`])
				}
				const teamObject: TeamInsertDbType = {
					id: teamId,
					businessEmail: email!,
					teamName: businessName!,
					phoneNumber: phoneNumber,
					isActive: true,
					currentPlan: TeamPlans.Free
				}
				const memberObject: MemberInsertDbType = {
					id: uuidv7(),
					teamId,
					userId,
					teamLocationId,
					memberStatus: TeamMemberStatuses.Active,
					createdAt: new Date(),
					updatedAt: new Date()
				}
				const teamLocationObject: TeamLocationInsertDbType = {
					id: teamLocationId,
					teamId,
					createdBy: userId,
					teamLocationAdmin: userId,
					isActive: true,
					locationName: `HQ`,
					isDefault: true,
					locationEmail: email,
					locationPhoneNumber: phoneNumber
				}

				const { data: createTeamAdminData, error: createTeamAdminError } = await tryCatch(
					createTeamAdminUser({
						userObject,
						teamObject,
						teamLocationObject,
						memberObject
					})
				)
				if (createTeamAdminError || !createTeamAdminData) {
					c.log.error(`create team admin error: ${createTeamAdminError}`)
					return { status: 'error', text: 'Failed to create user during sign up' }
				}

				switch (`${authType}-${verificationMethod}`) {
					case 'passkey-code':
						return {
							status: 'success',
							text: 'Sign Up email sent. Please check your email for Code',
							data: { authType: { type: 'code' }, userId }
						}
					case 'passkey-link':
						return {
							status: 'success',
							text: 'Sign Up email sent. Please check your mail and click on the verification link',
							data: { authType: { type: 'link' }, userId }
						}
					default:
						return {
							status: 'error',
							text: 'Invalid auth type'
						}
				}
			} catch (error) {
				c.log.error(`signup error: ${error}`)
				return {
					status: 'error',
					text: `And error occured while registering user: ${(error as Error).message}`
				}
			}
		},
		login: async (c, params: LoginFormType) => {
			try {
				const {
					isValid,
					output: formData,
					issues
				} = validateParams(loginFormSchema, params)
				if (!isValid || !formData) {
					return {
						status: 'error',
						text: 'Invalid parameters',
						issues: issues?.map((issue) => issue.message).join('; ')
					}
				}
				const principal: PasslockPrincipal | PasslockError = await passlock.fetchPrincipal({
					token: formData.token
				})
				if (PasslockError.isError(principal)) {
					c.log.error(`principal verification error: ${principal.message}`)
					return { status: 'error', text: principal.message }
				}
				const sessionTokenRequest = await tryCatch(createSessionToken(principal))

				if (sessionTokenRequest.error) {
					c.log.error(`session token error: ${sessionTokenRequest.error?.message}`)
					return { status: 'error', text: sessionTokenRequest.error?.message }
				}

				const { user: currentUser, expiration, jwtToken } = sessionTokenRequest.data
				c.log.info('creating auth session')
				const { data: sessionData, error: setupAuthError } = await tryCatch(
					setupAuthSession(jwtToken, currentUser, expiration)
				)

				if (setupAuthError) {
					c.log.error(`setup session error: ${setupAuthError}`)
					return { status: 'error', text: 'An error occured.Please try again' }
				}
				c.log.info('logged in')
				c.state.sessionData = sessionData
				return {
					status: 'success',
					text: 'Login successful',
					data: { session: sessionData }
				}
			} catch (error) {
				c.log.error(`login error: ${error}`)
				return {
					status: 'error',
					text: 'An error occured during login. Please try again'
				}
			}
		},
		verifyEmail: async (c, params: SignUpCodeFormType) => {
			try {
				const {
					isValid,
					output: formData,
					issues
				} = validateParams(signUpCodeFormSchema, params)
				if (!isValid || !formData) {
					return {
						status: 'error',
						text: 'Invalid parameters',
						issues: issues?.map((issue) => issue.message).join('; ')
					}
				}
				if (!formData.token) {
					return {
						text: 'Email Verification Failed',
						status: 'error'
					}
				}

				const principal: PasslockPrincipal = parse(formData.principal ?? '{}')
				if (!principal.emailVerified) {
					c.log.error(`principal email verification failed: ${principal}`)
					return {
						text: 'Email Verification Failed',
						status: 'error'
					}
				}
				const { data, error } = await tryCatch(
					markUserEmailAsVerified(principal.sub, principal.email!)
				)
				if (error || !data?.id) {
					c.log.error(`mark user as verified error: ${error}`)
					return {
						status: 'error',
						text: 'An error occured during user verification. please try again'
					}
				}

				const sessionResp = await tryCatch(createSessionToken(principal))
				if (sessionResp.error || !sessionResp.data?.jwtToken) {
					c.log.error(`create session token error: ${sessionResp.error}`)
					return {
						text: 'An error occured while verifying email',
						status: 'error'
					}
				}
				const { jwtToken, user: currentUser, expiration } = sessionResp.data

				c.log.info('creating auth session')
				const { data: sessionData, error: authSessionError } = await tryCatch(
					setupAuthSession(jwtToken, currentUser, expiration)
				)

				if (authSessionError) {
					c.log.error('setup session error', authSessionError)
					return {
						status: 'error',
						text: 'An error occured during Code Verification.Please try again'
					}
				}
				//setSessionCookie(event, sessionData) // TODO handle at the UI
				c.state.sessionData = sessionData
				return {
					text: 'Code verified successfully',
					status: 'success',
					data: { session: sessionData }
				}
			} catch (exception) {
				c.log.error('code verification exception', exception)
				return {
					status: 'error',
					text: 'An error occured during Code Verification.Please try again'
				}
			}
		},
		logOut: async (c) => {
			try {
				c.state.sessionData = undefined
				return {
					status: 'success',
					text: 'Logout successful'
				}
			} catch (error) {
				c.log.error(`logout error: ${error}`)
				return {
					status: 'error',
					text: 'An error occured during logout. Please try again'
				}
			}
		}
	}
})

export type AuthActorActionContext = ActionContextOf<typeof authActor>
export type AuthActor = ActorDefinition<typeof authActor, any, any, any, any>
