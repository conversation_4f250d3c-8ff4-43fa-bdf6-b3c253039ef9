import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import {
	type CalendarBlockDbType,
	type CalendarBlockInsertType,
	calendarBlock
} from '@repo/drizzle'
import type { ConnState } from '../utils/server.helpers'

export const calendarBlocksActor = actor({
	state: [] as CalendarBlockDbType[],
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<CalendarBlockInsertType> => {
			c.log.info(`create calendar block: ${item.id}`)
			const { authData } = c.conn.state as ConnState
			const newCalendarBlock = await (calendarBlock as any).create(item, authData, c.state)
			return newCalendarBlock
		},
		update: async (c, item): Promise<CalendarBlockInsertType> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`update calendar block: ${item.id}`)
			return await (calendarBlock as any).update(item.id, item, authData, c.state)
		},
		delete: async (c, item): Promise<boolean> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`delete calendar block: ${item.id}`)
			return await (calendarBlock as any).delete(item.id, authData, c.state)
		},
		getCollection: async (c): Promise<CalendarBlockDbType[]> => {
			const { authData } = c.conn.state as ConnState
			return await (calendarBlock as any).getCollection(authData, c.state)
		}
	}
})
