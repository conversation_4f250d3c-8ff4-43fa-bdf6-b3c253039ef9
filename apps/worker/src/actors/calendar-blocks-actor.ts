import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import {
	type CalendarBlockDbType,
	type CalendarBlockInsertType
} from '@repo/drizzle'
import type { ConnState } from '../utils/server.helpers'
import { stateHelper } from '../utils'
import { ActionTypes } from '@repo/common'

export const calendarBlocksActor = actor({
	state: [] as CalendarBlockDbType[],
	createVars: (_c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx,
		uniqueFields: ['id'],
		compositeUniqueFields: [['teamLocationId', 'startTime', 'endTime']]
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<CalendarBlockInsertType | undefined> => {
			c.log.info(`create calendar block: ${item.id}`)
			// Note: RBAC permission checking should be handled at a higher level
			const { data: newCalendarBlock, error: createCalendarBlockError } = await stateHelper.createStateRecord<CalendarBlockDbType>(c, item, ActionTypes.manageCalendarBlocks)
			if (createCalendarBlockError) {
				c.log.error(`create calendar block error: ${createCalendarBlockError}`)
				return undefined
			}
			return newCalendarBlock as CalendarBlockDbType
		},
		update: async (c, item): Promise<CalendarBlockInsertType | undefined> => {
			c.log.info(`update calendar block: ${item.id}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.updateStateRecord<CalendarBlockDbType>(c, item, ActionTypes.manageCalendarBlocks)
			if (!resp.success) {
				c.log.error(`update calendar block error: ${resp.error}`)
				return undefined
			}
			return resp.data as CalendarBlockDbType
		},
		delete: async (c, item): Promise<boolean> => {
			c.log.info(`delete calendar block: ${item.id}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.deleteStateRecord<CalendarBlockDbType>(c, item.id, ActionTypes.manageCalendarBlocks)
			if (resp.error) {
				c.log.error(`delete calendar block error: ${resp.error}`)
			}
			return resp.success
		},
		getCollection: async (c): Promise<CalendarBlockDbType[]> => {
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.getStateRecords<CalendarBlockDbType>(c, ActionTypes.readCalendarBlocks)
			if (!resp.success) {
				c.log.error(`get calendar blocks error: ${resp.error}`)
				return []
			}
			return resp.data || []
		}
	}
})
