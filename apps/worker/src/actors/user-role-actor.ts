import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type UserRoleInsertDbType } from '@repo/drizzle'
import type { ConnState } from '../utils/server.helpers'
import { stateHelper } from '../utils'
import { ActionTypes } from '@repo/common'

export const userRoleActor = actor({
	state: [] as UserRoleInsertDbType[],
	createVars: (_c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx,
		uniqueFields: ['id', 'role'],
		compositeUniqueFields: [['userId', 'role']]
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<UserRoleInsertDbType | undefined> => {
			c.log.info(`create user role: ${item.role}`)
			// Note: RBAC permission checking should be handled at a higher level
			const { data: newUserRole, error: createUserRoleError } = await stateHelper.createStateRecord<UserRoleInsertDbType>(c, item, ActionTypes.manageUserRoles)
			if (createUserRoleError) {
				c.log.error(`create user role error: ${createUserRoleError}`)
				return undefined
			}
			return newUserRole as UserRoleInsertDbType
		},
		update: async (c, item): Promise<UserRoleInsertDbType | undefined> => {
			c.log.info(`update user role: ${item.role}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.updateStateRecord<UserRoleInsertDbType>(c, item, ActionTypes.manageUserRoles)
			if (!resp.success) {
				c.log.error(`update user role error: ${resp.error}`)
				return undefined
			}
			return resp.data as UserRoleInsertDbType
		},
		delete: async (c, item): Promise<boolean> => {
			c.log.info(`delete user role: ${item.role}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.deleteStateRecord<UserRoleInsertDbType>(c, item.id, ActionTypes.manageUserRoles)
			if (resp.error) {
				c.log.error(`delete user role error: ${resp.error}`)
			}
			return resp.success
		},
		getCollection: async (c): Promise<UserRoleInsertDbType[]> => {
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.getStateRecords<UserRoleInsertDbType>(c, ActionTypes.readUserRoles)
			if (!resp.success) {
				c.log.error(`get user roles error: ${resp.error}`)
				return []
			}
			return resp.data || []
		}
	}
})
