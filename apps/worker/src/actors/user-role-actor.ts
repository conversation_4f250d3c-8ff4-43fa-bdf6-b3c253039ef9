import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type UserRoleInsertDbType, userRole } from '@repo/drizzle'
import type { ConnState } from '../server.helpers'

const stateObj = {} as UserRoleInsertDbType
export const userRoleActor = actor({
	state: stateObj,
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<UserRoleInsertDbType> => {
			c.log.info(`create user role: ${item.role}`)
			const { authData } = c.conn.state as ConnState
			const newUserRole = await (userRole as any).create(item, authData, c.state)
			return newUserRole
		},
		update: async (c, item): Promise<UserRoleInsertDbType> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`update user role: ${item.role}`)
			return await (userRole as any).update(item.id, item, authData, c.state)
		},
		delete: async (c, item): Promise<boolean> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`delete user role: ${item.role}`)
			return await (userRole as any).delete(item.id, authData, c.state)
		},
		getCollection: async (c): Promise<UserRoleInsertDbType[]> => {
			const { authData } = c.conn.state as ConnState
			return await (userRole as any).getCollection(authData, c.state)
		}
	}
})
