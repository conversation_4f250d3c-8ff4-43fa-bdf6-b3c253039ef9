import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type MemberDbType, type MemberInsertDbType, member } from '@repo/drizzle'
import type { ConnState } from '../server.helpers'

export const membersActor = actor({
	state: [] as MemberDbType[],
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<MemberInsertDbType> => {
			c.log.info(`create member: ${item.id}`)
			const { authData } = c.conn.state as ConnState
			const newMember = await (member as any).create(item, authData, c.state)
			return newMember
		},
		update: async (c, item): Promise<MemberInsertDbType> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`update member: ${item.id}`)
			return await (member as any).update(item.id, item, authData, c.state)
		},
		delete: async (c, item): Promise<boolean> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`delete member: ${item.id}`)
			return await (member as any).delete(item.id, authData, c.state)
		},
		getCollection: async (c): Promise<MemberDbType[]> => {
			const { authData } = c.conn.state as ConnState
			return await (member as any).getCollection(authData, c.state)
		}
	}
})
