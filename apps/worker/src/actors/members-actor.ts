import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type MemberDbType, type MemberInsertDbType } from '@repo/drizzle'
import type { ConnState } from '../utils/server.helpers'
import { stateHelper } from '../utils'
import { ActionTypes } from '@repo/common'

export const membersActor = actor({
	state: [] as MemberDbType[],
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx,
		uniqueFields: ['id', 'userId'],
		compositeUniqueFields: [['userId', 'teamId']]
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<MemberInsertDbType | undefined> => {
			c.log.info(`create member: ${item.id}`)
			// Note: RBAC permission checking should be handled at a higher level
			const { data: newMember, error: createMemberError } = await stateHelper.createStateRecord<MemberDbType>(c, item, ActionTypes.manageMembers)
			if (createMemberError) {
				c.log.error(`create member error: ${createMemberError}`)
				return undefined
			}
			return newMember as MemberDbType
		},
		update: async (c, item): Promise<MemberInsertDbType | undefined> => {
			c.log.info(`update member: ${item.id}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.updateStateRecord<MemberDbType>(c, item, ActionTypes.manageMembers)
			if (!resp.success) {
				c.log.error(`update member error: ${resp.error}`)
				return undefined
			}
			return resp.data as MemberDbType
		},
		delete: async (c, item): Promise<boolean> => {
			c.log.info(`delete member: ${item.id}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.deleteStateRecord<MemberDbType>(c, item.id, ActionTypes.manageMembers)
			if (resp.error) {
				c.log.error(`delete member error: ${resp.error}`)
			}
			return resp.success
		},
		getCollection: async (c): Promise<MemberDbType[]> => {
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.getStateRecords<MemberDbType>(c, ActionTypes.readmembers)
			if (!resp.success) {
				c.log.error(`get members error: ${resp.error}`)
				return []
			}
			return resp.data || []
		}
	}
})
