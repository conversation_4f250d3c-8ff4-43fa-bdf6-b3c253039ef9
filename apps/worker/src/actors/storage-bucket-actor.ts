import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import {
	type StorageBucketDbType,
	type StorageBucketInsertDbType,
	storageBucket
} from '@repo/drizzle'
import type { AuthData, ConnState } from '../server.helpers'

export const storageBucketActor = actor({
	state: {} as StorageBucketDbType,
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params }): Promise<{ authData: AuthData | undefined }> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<StorageBucketInsertDbType> => {
			c.log.info(`create storage bucket: ${item.id}`)
			const { authData } = c.conn.state as ConnState
			const newStorageBucket = await (storageBucket as any).create(item, authData, c.state)
			return newStorageBucket
		},
		update: async (c, item): Promise<StorageBucketInsertDbType> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`update storage bucket: ${item.id}`)
			return await (storageBucket as any).update(item.id, item, authData, c.state)
		},
		delete: async (c, item): Promise<boolean> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`delete storage bucket: ${item.id}`)
			return await (storageBucket as any).delete(item.id, authData, c.state)
		},
		getCollection: async (c): Promise<StorageBucketInsertDbType[]> => {
			const { authData } = c.conn.state as ConnState
			return await (storageBucket as any).getCollection(authData, c.state)
		}
	}
})
