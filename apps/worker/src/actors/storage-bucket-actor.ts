import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import {
	type StorageBucketDbType,
	type StorageBucketInsertDbType
} from '@repo/drizzle'
import type { AuthData } from '../utils/server.helpers'
import { stateHelper } from '../utils'
import { ActionTypes } from '@repo/common'

export const storageBucketActor = actor({
	state: [] as StorageBucketDbType[],
	createVars: (_c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx,
		uniqueFields: ['id', 'name'],
		compositeUniqueFields: []
	}),
	createConnState: async (c, { params }): Promise<{ authData: AuthData | undefined }> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<StorageBucketInsertDbType | undefined> => {
			c.log.info(`create storage bucket: ${item.id}`)
			// Note: RBAC permission checking should be handled at a higher level
			const { data: newStorageBucket, error: createStorageBucketError } = await stateHelper.createStateRecord<StorageBucketDbType>(c as any, item, ActionTypes.manageStorageBuckets)
			if (createStorageBucketError) {
				c.log.error(`create storage bucket error: ${createStorageBucketError}`)
				return undefined
			}
			return newStorageBucket as StorageBucketDbType
		},
		update: async (c, item): Promise<StorageBucketInsertDbType | undefined> => {
			c.log.info(`update storage bucket: ${item.id}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.updateStateRecord<StorageBucketDbType>(c as any, item, ActionTypes.manageStorageBuckets)
			if (!resp.success) {
				c.log.error(`update storage bucket error: ${resp.error}`)
				return undefined
			}
			return resp.data as StorageBucketDbType
		},
		delete: async (c, item): Promise<boolean> => {
			c.log.info(`delete storage bucket: ${item.id}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.deleteStateRecord<StorageBucketDbType>(c as any, item.id, ActionTypes.manageStorageBuckets)
			if (resp.error) {
				c.log.error(`delete storage bucket error: ${resp.error}`)
			}
			return resp.success
		},
		getCollection: async (c): Promise<StorageBucketInsertDbType[]> => {
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.getStateRecords<StorageBucketDbType>(c as any, ActionTypes.readStorageBuckets)
			if (!resp.success) {
				c.log.error(`get storage buckets error: ${resp.error}`)
				return []
			}
			return resp.data || []
		}
	}
})
