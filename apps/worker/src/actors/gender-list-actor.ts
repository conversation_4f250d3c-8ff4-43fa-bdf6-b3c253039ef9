import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type GenderDbType, gender } from '@repo/drizzle'
import type { ConnState } from '../server.helpers'

export const genderListActor = actor({
	state: [] as GenderDbType[],
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		getCollection: async (c): Promise<GenderDbType[]> => {
			const { authData } = c.conn.state as ConnState
			return await (gender as any).getCollection(authData, c.state)
		}
	}
})
