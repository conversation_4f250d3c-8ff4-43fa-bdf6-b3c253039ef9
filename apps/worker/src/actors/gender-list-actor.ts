import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type GenderDbType } from '@repo/drizzle'
import type { ConnState } from '../utils/server.helpers'
import { stateHelper } from '../utils'
import { ActionTypes } from '@repo/common'

export const genderListActor = actor({
	state: [] as GenderDbType[],
	createVars: (_c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx,
		uniqueFields: ['id', 'name'],
		compositeUniqueFields: []
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		getCollection: async (c): Promise<GenderDbType[]> => {
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.getStateRecords<GenderDbType>(c, ActionTypes.readGenders)
			if (!resp.success) {
				c.log.error(`get genders error: ${resp.error}`)
				return []
			}
			return resp.data || []
		}
	}
})
