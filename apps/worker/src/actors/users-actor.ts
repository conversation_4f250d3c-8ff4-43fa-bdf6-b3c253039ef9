import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type UserDbType, type UserInsertDbType, user } from '@repo/drizzle'
import type { ConnState } from '../server.helpers'

export const usersActor = actor({
	state: [] as UserDbType[],
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<UserInsertDbType> => {
			c.log.info(`create user: ${item.displayName}`)
			const { authData } = c.conn.state as ConnState
			const newUser = await (user as any).create(item, authData, c.state)
			return newUser
		},
		update: async (c, item): Promise<UserInsertDbType> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`update user: ${item.displayName}`)
			return await (user as any).update(item.id, item, authData, c.state)
		},
		delete: async (c, item): Promise<boolean> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`delete user: ${item.displayName}`)
			return await (user as any).delete(item.id, authData, c.state)
		},
		getCollection: async (c): Promise<UserDbType[]> => {
			const { authData } = c.conn.state as ConnState
			return await (user as any).getCollection(authData, c.state)
		}
	}
})
