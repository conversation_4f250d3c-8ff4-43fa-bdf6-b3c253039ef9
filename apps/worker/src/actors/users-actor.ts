import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type UserDbType, type UserInsertDbType } from '@repo/drizzle'
import type { ConnState } from '../utils/server.helpers'
import { stateHelper } from '../utils'
import { ActionTypes } from '@repo/common'

export const usersActor = actor({
	state: [] as UserDbType[],
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx,
		uniqueFields: ['id', 'email', 'displayName'],
		compositeUniqueFields: []
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<UserInsertDbType | undefined> => {
			c.log.info(`create user: ${item.displayName}`)
			// Note: RBAC permission checking should be handled at a higher level
			const { data: newUser, error: createUserError } = await stateHelper.createStateRecord<UserDbType>(c, item, ActionTypes.manageMembers)
			if (createUserError) {
				c.log.error(`create user error: ${createUserError}`)
				return undefined
			}
			return newUser as UserDbType
		},
		update: async (c, item): Promise<UserInsertDbType | undefined> => {
			c.log.info(`update user: ${item.displayName}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.updateStateRecord<UserDbType>(c, item, ActionTypes.manageMembers)
			if (!resp.success) {
				c.log.error(`update user error: ${resp.error}`)
				return undefined
			}
			return resp.data as UserDbType
		},
		delete: async (c, item): Promise<boolean> => {
			c.log.info(`delete user: ${item.displayName}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.deleteStateRecord<UserDbType>(c, item.id, ActionTypes.manageMembers)
			if (resp.error) {
				c.log.error(`delete user error: ${resp.error}`)
			}
			return resp.success
		},
		getCollection: async (c): Promise<UserDbType[]> => {
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.getStateRecords<UserDbType>(c, ActionTypes.readmembers)
			if (!resp.success) {
				c.log.error(`get users error: ${resp.error}`)
				return []
			}
			return resp.data || []
		}
	}
})
