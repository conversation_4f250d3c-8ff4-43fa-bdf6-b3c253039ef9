import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type TeamPaymentProviderInsertDbType } from '@repo/drizzle'
import type { ConnState } from '../utils/server.helpers'
import { stateHelper } from '../utils'
import { ActionTypes } from '@repo/common'

export const teamPaymentProviderActor = actor({
	state: [] as TeamPaymentProviderInsertDbType[],
	createVars: (_c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx,
		uniqueFields: ['id', 'providerName'],
		compositeUniqueFields: [['teamId', 'providerName']]
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<TeamPaymentProviderInsertDbType | undefined> => {
			c.log.info(`create team payment provider: ${item.providerName}`)
			// Note: RBAC permission checking should be handled at a higher level
			const { data: newTeamPaymentProvider, error: createTeamPaymentProviderError } = await stateHelper.createStateRecord<TeamPaymentProviderInsertDbType>(c, item, ActionTypes.manageTeamPaymentProviders)
			if (createTeamPaymentProviderError) {
				c.log.error(`create team payment provider error: ${createTeamPaymentProviderError}`)
				return undefined
			}
			return newTeamPaymentProvider as TeamPaymentProviderInsertDbType
		},
		update: async (c, item): Promise<TeamPaymentProviderInsertDbType | undefined> => {
			c.log.info(`update team payment provider: ${item.providerName}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.updateStateRecord<TeamPaymentProviderInsertDbType>(c, item, ActionTypes.manageTeamPaymentProviders)
			if (!resp.success) {
				c.log.error(`update team payment provider error: ${resp.error}`)
				return undefined
			}
			return resp.data as TeamPaymentProviderInsertDbType
		},
		delete: async (c, item): Promise<boolean> => {
			c.log.info(`delete team payment provider: ${item.providerName}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.deleteStateRecord<TeamPaymentProviderInsertDbType>(c, item.id, ActionTypes.manageTeamPaymentProviders)
			if (resp.error) {
				c.log.error(`delete team payment provider error: ${resp.error}`)
			}
			return resp.success
		},
		getCollection: async (c): Promise<TeamPaymentProviderInsertDbType[]> => {
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.getStateRecords<TeamPaymentProviderInsertDbType>(c, ActionTypes.readTeamPaymentProviders)
			if (!resp.success) {
				c.log.error(`get team payment providers error: ${resp.error}`)
				return []
			}
			return resp.data || []
		}
	}
})
