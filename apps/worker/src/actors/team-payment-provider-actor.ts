import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type TeamPaymentProviderInsertDbType, teamPaymentProvider } from '@repo/drizzle'
import type { ConnState } from '../utils/server.helpers'

export const teamPaymentProviderActor = actor({
	state: {} as TeamPaymentProviderInsertDbType,
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<TeamPaymentProviderInsertDbType> => {
			c.log.info(`create team payment provider: ${item.providerName}`)
			const { authData } = c.conn.state as ConnState
			const newTeamPaymentProvider = await (teamPaymentProvider as any).create(
				item,
				authData,
				c.state
			)
			return newTeamPaymentProvider
		},
		update: async (c, item): Promise<TeamPaymentProviderInsertDbType> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`update team payment provider: ${item.providerName}`)
			return await (teamPaymentProvider as any).update(item.id, item, authData, c.state)
		},
		delete: async (c, item): Promise<boolean> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`delete team payment provider: ${item.providerName}`)
			return await (teamPaymentProvider as any).delete(item.id, authData, c.state)
		},
		getCollection: async (c): Promise<TeamPaymentProviderInsertDbType[]> => {
			const { authData } = c.conn.state as ConnState
			return await (teamPaymentProvider as any).getCollection(authData, c.state)
		}
	}
})
