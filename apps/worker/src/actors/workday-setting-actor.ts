import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type WorkdaySettingInsertDbType } from '@repo/drizzle'
import type { ConnState } from '../utils/server.helpers'
import { stateHelper } from '../utils'
import { ActionTypes } from '@repo/common'

export const workdaySettingActor = actor({
	state: [] as WorkdaySettingInsertDbType[],
	createVars: (_c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx,
		uniqueFields: ['id', 'weekday'],
		compositeUniqueFields: [['teamLocationId', 'weekday']]
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<WorkdaySettingInsertDbType | undefined> => {
			c.log.info(`create workday setting: ${item.weekday}`)
			// Note: RBAC permission checking should be handled at a higher level
			const { data: newWorkdaySetting, error: createWorkdaySettingError } = await stateHelper.createStateRecord<WorkdaySettingInsertDbType>(c, item, ActionTypes.manageWorkdaySettings)
			if (createWorkdaySettingError) {
				c.log.error(`create workday setting error: ${createWorkdaySettingError}`)
				return undefined
			}
			return newWorkdaySetting as WorkdaySettingInsertDbType
		},
		update: async (c, item): Promise<WorkdaySettingInsertDbType | undefined> => {
			c.log.info(`update workday setting: ${item.weekday}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.updateStateRecord<WorkdaySettingInsertDbType>(c, item, ActionTypes.manageWorkdaySettings)
			if (!resp.success) {
				c.log.error(`update workday setting error: ${resp.error}`)
				return undefined
			}
			return resp.data as WorkdaySettingInsertDbType
		},
		delete: async (c, item): Promise<boolean> => {
			c.log.info(`delete workday setting: ${item.weekday}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.deleteStateRecord<WorkdaySettingInsertDbType>(c, item.id, ActionTypes.manageWorkdaySettings)
			if (resp.error) {
				c.log.error(`delete workday setting error: ${resp.error}`)
			}
			return resp.success
		},
		getCollection: async (c): Promise<WorkdaySettingInsertDbType[]> => {
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.getStateRecords<WorkdaySettingInsertDbType>(c, ActionTypes.readWorkdaySettings)
			if (!resp.success) {
				c.log.error(`get workday settings error: ${resp.error}`)
				return []
			}
			return resp.data || []
		}
	}
})
