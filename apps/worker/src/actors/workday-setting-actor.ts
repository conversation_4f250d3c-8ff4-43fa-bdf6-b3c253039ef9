import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type WorkdaySettingInsertDbType, workdaySetting } from '@repo/drizzle'
import type { ConnState } from '../server.helpers'

const stateObj = {} as WorkdaySettingInsertDbType
export const workdaySettingActor = actor({
	state: stateObj,
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<WorkdaySettingInsertDbType> => {
			c.log.info(`create workday setting: ${item.weekday}`)
			const { authData } = c.conn.state as ConnState
			const newWorkdaySetting = await (workdaySetting as any).create(item, authData, c.state)
			return newWorkdaySetting
		},
		update: async (c, item): Promise<WorkdaySettingInsertDbType> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`update workday setting: ${item.weekday}`)
			return await (workdaySetting as any).update(item.id, item, authData, c.state)
		},
		delete: async (c, item): Promise<boolean> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`delete workday setting: ${item.weekday}`)
			return await (workdaySetting as any).delete(item.id, authData, c.state)
		},
		getCollection: async (c): Promise<WorkdaySettingInsertDbType[]> => {
			const { authData } = c.conn.state as ConnState
			return await (workdaySetting as any).getCollection(authData, c.state)
		}
	}
})
