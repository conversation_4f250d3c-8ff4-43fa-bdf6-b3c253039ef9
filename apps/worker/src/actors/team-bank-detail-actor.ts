import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import {
	type TeamBankDetailDbType,
	type TeamBankDetailInsertDbType,
	teamBankDetail
} from '@repo/drizzle'
import type { ConnState } from '../server.helpers'

export const teamBankDetailActor = actor({
	state: {} as TeamBankDetailDbType,
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<TeamBankDetailInsertDbType> => {
			c.log.info(`create team bank detail: ${item.bankName}`)
			const { authData } = c.conn.state as ConnState
			const newTeamBankDetail = await (teamBankDetail as any).create(item, authData, c.state)
			return newTeamBankDetail
		},
		update: async (c, item): Promise<TeamBankDetailInsertDbType> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`update team bank detail: ${item.bankName}`)
			return await (teamBankDetail as any).update(item.id, item, authData, c.state)
		},
		delete: async (c, item): Promise<boolean> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`delete team bank detail: ${item.bankName}`)
			return await (teamBankDetail as any).delete(item.id, authData, c.state)
		},
		getCollection: async (c): Promise<TeamBankDetailDbType[]> => {
			const { authData } = c.conn.state as ConnState
			return await (teamBankDetail as any).getCollection(authData, c.state)
		}
	}
})
