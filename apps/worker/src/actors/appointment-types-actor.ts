import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import {
	type AppointmentTypeDbType,
	type AppointmentTypeInsertDbType,
	appointmentType
} from '@repo/drizzle'
import type { ConnState } from '../server.helpers'

export const appointmentTypesActor = actor({
	state: [] as AppointmentTypeDbType[],
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<AppointmentTypeInsertDbType> => {
			c.log.info(`create appointment type: ${item.appointmentDetail}`)
			const { authData } = c.conn.state as ConnState
			const newAppointmentType = await (appointmentType as any).create(
				item,
				authData,
				c.state
			)
			return newAppointmentType
		},
		update: async (c, item): Promise<AppointmentTypeInsertDbType> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`update appointment type: ${item.appointmentDetail}`)
			return await (appointmentType as any).update(item.id, item, authData, c.state)
		},
		delete: async (c, item): Promise<boolean> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`delete appointment type: ${item.appointmentDetail}`)
			return await (appointmentType as any).delete(item.id, authData, c.state)
		},
		getCollection: async (c): Promise<AppointmentTypeDbType[]> => {
			const { authData } = c.conn.state as ConnState
			return await (appointmentType as any).getCollection(authData, c.state)
		}
	}
})
