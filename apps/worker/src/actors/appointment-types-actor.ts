import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import {
	type AppointmentTypeDbType,
	type AppointmentTypeInsertDbType
} from '@repo/drizzle'
import type { ConnState } from '../utils/server.helpers'
import { stateHelper } from '../utils'
import { ActionTypes } from '@repo/common'

export const appointmentTypesActor = actor({
	state: [] as AppointmentTypeDbType[],
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx,
		uniqueFields: ['id'],
		compositeUniqueFields: [['teamId', 'appointmentDetail']]
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<AppointmentTypeInsertDbType | undefined> => {
			c.log.info(`create appointment type: ${item.appointmentDetail}`)
			// Note: RBAC permission checking should be handled at a higher level
			const { data: newAppointmentType, error: createAppointmentTypeError } = await stateHelper.createStateRecord<AppointmentTypeDbType>(c, item, ActionTypes.manageAppointmentTypes)
			if (createAppointmentTypeError) {
				c.log.error(`create appointment type error: ${createAppointmentTypeError}`)
				return undefined
			}
			return newAppointmentType as AppointmentTypeDbType
		},
		update: async (c, item): Promise<AppointmentTypeInsertDbType | undefined> => {
			c.log.info(`update appointment type: ${item.appointmentDetail}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.updateStateRecord<AppointmentTypeDbType>(c, item, ActionTypes.manageAppointmentTypes)
			if (!resp.success) {
				c.log.error(`update appointment type error: ${resp.error}`)
				return undefined
			}
			return resp.data as AppointmentTypeDbType
		},
		delete: async (c, item): Promise<boolean> => {
			c.log.info(`delete appointment type: ${item.appointmentDetail}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.deleteStateRecord<AppointmentTypeDbType>(c, item.id, ActionTypes.manageAppointmentTypes)
			if (resp.error) {
				c.log.error(`delete appointment type error: ${resp.error}`)
			}
			return resp.success
		},
		getCollection: async (c): Promise<AppointmentTypeDbType[]> => {
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.getStateRecords<AppointmentTypeDbType>(c, ActionTypes.readAppointmentTypes)
			if (!resp.success) {
				c.log.error(`get appointment types error: ${resp.error}`)
				return []
			}
			return resp.data || []
		}
	}
})
