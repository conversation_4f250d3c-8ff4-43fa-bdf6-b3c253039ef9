import type { AuthData } from '../utils/server.helpers'
import { isAuthenticated } from '../services'
import { tryCatch } from '@repo/common'

export const checkConnState = async (c, params): Promise<{ authData: AuthData }> => {
	try {
		const apiKey = params?.['spendeedApiKey'] as string
		const jwt = params?.['jwtToken'] as string
		let authData = {} as AuthData
		if (apiKey) {
			if (apiKey !== process.env.SPENDEEED_API_KEY) {
				throw new Error('Invalid API key')
			}
			authData = {
				...authData,
				userID: 'spendeed-api-key',
				email: '<EMAIL>'
			} as AuthData
			return { authData }
		}
		if (!jwt) {
			throw new Error('No JWT token provided')
		}
		const result = await tryCatch(isAuthenticated(jwt))
		if (result.error) {
			throw new Error('invalid token', result.error)
		}
		const { isValid, sessionData } = result.data
		if (!isValid) {
			throw new Error('invalid token')
		}

		authData = {
			...authData,
			...sessionData,
			userID: sessionData?.user?.id
		} as AuthData
		return { authData }
		//we now have c.conn.state.authData
	} catch (error) {
		c.log.error(error)
		throw new Error('Invalid or expired JWT token')
	}
}
