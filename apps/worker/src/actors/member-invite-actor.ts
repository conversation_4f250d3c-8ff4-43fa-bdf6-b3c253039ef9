import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type MemberInviteDbType, type MemberInviteInsertDbType, memberInvite } from '@repo/drizzle'
import type { ConnState } from '../utils/server.helpers'

export const membersInviteActor = actor({
	state: {} as MemberInviteDbType,
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<MemberInviteInsertDbType> => {
			c.log.info(`create member invite: ${item.inviteeEmail}`)
			const { authData } = c.conn.state as ConnState
			const newMemberInvite = await (memberInvite as any).create(item, authData, c.state)
			return newMemberInvite
		},
		update: async (c, item): Promise<MemberInviteInsertDbType> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`update member invite: ${item.inviteeEmail}`)
			return await (memberInvite as any).update(item.id, item, authData, c.state)
		},
		delete: async (c, item): Promise<boolean> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`delete member invite: ${item.inviteeEmail}`)
			return await (memberInvite as any).delete(item.id, authData, c.state)
		},
		getCollection: async (c): Promise<MemberInviteDbType[]> => {
			const { authData } = c.conn.state as ConnState
			return await (memberInvite as any).getCollection(authData, c.state)
		}
	}
})
