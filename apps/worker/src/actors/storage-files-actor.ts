import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type StorageFileDbType, type StorageFileInsertDbType } from '@repo/drizzle'
import type { AuthData } from '../utils/server.helpers'
import { stateHelper } from '../utils'
import { ActionTypes } from '@repo/common'

export const storageFilesActor = actor({
	state: [] as StorageFileDbType[],
	createVars: (_c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx,
		uniqueFields: ['id', 'name'],
		compositeUniqueFields: [['bucketId', 'name']]
	}),
	createConnState: async (c, { params }): Promise<{ authData: AuthData | undefined }> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<StorageFileInsertDbType | undefined> => {
			c.log.info(`create storage file: ${item.name}`)
			// Note: RBAC permission checking should be handled at a higher level
			const { data: newStorageFile, error: createStorageFileError } = await stateHelper.createStateRecord<StorageFileDbType>(c as any, item, ActionTypes.manageStorageFiles)
			if (createStorageFileError) {
				c.log.error(`create storage file error: ${createStorageFileError}`)
				return undefined
			}
			return newStorageFile as StorageFileDbType
		},
		update: async (c, item): Promise<StorageFileInsertDbType | undefined> => {
			c.log.info(`update storage file: ${item.name}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.updateStateRecord<StorageFileDbType>(c as any, item, ActionTypes.manageStorageFiles)
			if (!resp.success) {
				c.log.error(`update storage file error: ${resp.error}`)
				return undefined
			}
			return resp.data as StorageFileDbType
		},
		delete: async (c, item): Promise<boolean> => {
			c.log.info(`delete storage file: ${item.name}`)
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.deleteStateRecord<StorageFileDbType>(c as any, item.id, ActionTypes.manageStorageFiles)
			if (resp.error) {
				c.log.error(`delete storage file error: ${resp.error}`)
			}
			return resp.success
		},
		getCollection: async (c): Promise<StorageFileInsertDbType[]> => {
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.getStateRecords<StorageFileDbType>(c as any, ActionTypes.readStorageFiles)
			if (!resp.success) {
				c.log.error(`get storage files error: ${resp.error}`)
				return []
			}
			return resp.data || []
		}
	}
})
