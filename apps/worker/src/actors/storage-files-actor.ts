import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type StorageFileDbType, type StorageFileInsertDbType, storageFile } from '@repo/drizzle'
import type { AuthData, ConnState } from '../server.helpers'

export const storageFilesActor = actor({
	state: [] as StorageFileDbType[],
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params }): Promise<{ authData: AuthData | undefined }> => {
		return await checkConnState(c, params)
	},
	actions: {
		create: async (c, item): Promise<StorageFileInsertDbType> => {
			c.log.info(`create storage file: ${item.name}`)
			const { authData } = c.conn.state as ConnState
			const newStorageFile = await (storageFile as any).create(item, authData, c.state)
			return newStorageFile
		},
		update: async (c, item): Promise<StorageFileInsertDbType> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`update storage file: ${item.name}`)
			return await (storageFile as any).update(item.id, item, authData, c.state)
		},
		delete: async (c, item): Promise<boolean> => {
			const { authData } = c.conn.state as ConnState
			c.log.info(`delete storage file: ${item.name}`)
			return await (storageFile as any).delete(item.id, authData, c.state)
		},
		getCollection: async (c): Promise<StorageFileInsertDbType[]> => {
			const { authData } = c.conn.state as ConnState
			return await (storageFile as any).getCollection(authData, c.state)
		}
	}
})
