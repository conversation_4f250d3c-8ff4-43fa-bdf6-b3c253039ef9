import { actor } from 'actor-core'
import { mastra } from '../mastra'
import type { ConnState } from '../server.helpers'
import { checkConnState } from './middleware'

export type CreateItem<T> = {
      collectionName: string
      data: T
      token: string
}
export type UpdateItem<T> = {
      collectionName: string
      data: Partial<T>
      id: string
      token: string
}
export type DeleteItem<T> = {
      collectionName: string
      id: string
      token: string
}
export const pgActor = actor({
      state: {} as any,
      createVars: (c, driverCtx) => ({
            mastra: mastra,
            ctx: driverCtx
      }),
      actions: {
            create: async (c, item): Promise<T> => {
                  c.log.info(`create team: ${item.teamName}`)
                  const { authData } = c.conn.state as ConnState
                  const { data: newTeam,
                        error: createTeamError
                  } = await createRecord<T>('team', item, ActionTypes.manageTeam, authData)
                  if (createTeamError) {
                        c.log.error(`create team error: ${createTeamError}`)
                        return undefined
                  }
                  return newTeam as TeamDbType
            },
            update: async (c, item): Promise<TeamInsertDbType | undefined> => {
                  const { authData } = c.conn.state as ConnState
                  c.log.info(`update team: ${item.teamName}`)
                  const resp = await updateRecord<TeamInsertDbType>('team', ActionTypes.manageTeam, authData, item)
                  if (!resp.success) {
                        c.log.error(`update team error: ${resp.error}`)
                        return undefined
                  }
                  return resp.data as TeamDbType
            },
            delete: async (c, item): Promise<boolean> => {
                  const { authData } = c.conn.state as ConnState
                  c.log.info(`delete team: ${item.teamName}`)
                  const resp = await deleteRecord<TeamInsertDbType>('team', ActionTypes.manageTeam, authData, item)
                  if (resp.error) {
                        c.log.error(`delete team error: ${resp.error}`)
                  }
                  return resp.success
            },
            getCollection: async (c): Promise<TeamDbType | undefined> => {
                  const { authData } = c.conn.state as ConnState
                  const teamId = authData?.teamId
                  const resp = await getRecordById<TeamDbType>('team', ActionTypes.readTeam, authData, teamId)
                  if (!resp.success) {
                        c.log.error(`get team error: ${resp.error}`)
                        return undefined
                  }
                  return resp.data
            }
      }
})
