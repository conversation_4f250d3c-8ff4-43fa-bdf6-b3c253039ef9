import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type TimeZoneDbType } from '@repo/drizzle'
import type { ConnState } from '../utils/server.helpers'
import { stateHelper } from '../utils'
import { ActionTypes } from '@repo/common'

export const timeZonesActor = actor({
	state: [] as TimeZoneDbType[],
	createVars: (_c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx,
		uniqueFields: ['id', 'name'],
		compositeUniqueFields: []
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		getCollection: async (c): Promise<TimeZoneDbType[]> => {
			// Note: RBAC permission checking should be handled at a higher level
			const resp = await stateHelper.getStateRecords<TimeZoneDbType>(c, ActionTypes.readTimeZones)
			if (!resp.success) {
				c.log.error(`get time zones error: ${resp.error}`)
				return []
			}
			return resp.data || []
		}
	}
})
