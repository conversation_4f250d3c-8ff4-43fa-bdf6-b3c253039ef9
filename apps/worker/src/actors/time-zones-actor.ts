import { actor } from 'actor-core'
import { mastra } from '../mastra'
import { checkConnState } from './middleware'
import { type TimeZoneDbType, timeZone } from '@repo/drizzle'
import type { ConnState } from '../utils/server.helpers'

export const timeZonesActor = actor({
	state: [] as TimeZoneDbType[],
	createVars: (c, driverCtx) => ({
		mastra: mastra,
		ctx: driverCtx
	}),
	createConnState: async (c, { params }): Promise<ConnState> => {
		return await checkConnState(c, params)
	},
	actions: {
		getCollection: async (c): Promise<TimeZoneDbType[]> => {
			const { authData } = c.conn.state as ConnState
			return await (timeZone as any).getCollection(authData, c.state)
		}
	}
})
