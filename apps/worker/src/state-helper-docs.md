# State Helper - Actor State Management Documentation

## Overview

The `state-helper.ts` provides a comprehensive set of functions for managing actor state with dynamic where clauses, RBAC permissions, and automatic event broadcasting. It follows the same principles as the drizzle-helper but operates on in-memory state using linq-extensions.

## Key Features

- **State Management**: CRUD operations on actor state arrays
- **Dynamic Filtering**: Complex where clauses with multiple operators
- **RBAC Integration**: Permission checking for all operations
- **Event Broadcasting**: Automatic broadcasting of ActorEvents and PostgresEvents
- **Type Safety**: Full TypeScript support with generic types
- **Linq Extensions**: Leverages linq-extensions for array operations

## Core Functions

### State Mutation Functions (with Broadcasting)

#### `createStateRecord<T>(c, data, rbacAction)`
Creates a new record in actor state.
- **Broadcasts**: `ActorEvents.ItemCreated`, `PostgresEvents.ItemCreated`
- **Auto-generates**: ID, createdAt, updatedAt timestamps

#### `updateStateRecord<T>(c, data, rbacAction)`
Updates an existing record by ID.
- **Broadcasts**: `ActorEvents.ItemUpdated`, `PostgresEvents.ItemUpdated`
- **Auto-updates**: updatedAt timestamp

#### `deleteStateRecord<T>(c, id, rbacAction)`
Deletes a record by ID.
- **Broadcasts**: `ActorEvents.ItemDeleted`, `PostgresEvents.ItemDeleted`

#### `createMultipleStateRecords<T>(c, dataArray, rbacAction)`
Bulk creates multiple records.
- **Broadcasts**: Individual item events + `PostgresEvents.CollectionUpdated`

### State Query Functions (no Broadcasting)

#### `getStateRecords<T>(c, rbacAction)`
Gets all records from state.

#### `getStateRecordsWithWhere<T>(c, rbacAction, whereClause?)`
Gets filtered records using dynamic where clauses.

#### `getStateRecordById<T>(c, id, rbacAction)`
Gets a single record by ID.

#### `getStateRecordsByTeamId<T>(c, teamId, rbacAction)`
Gets records filtered by teamId (common pattern).

#### `searchStateRecords<T>(c, searchField, searchTerm, rbacAction)`
Text search in specified field (case-insensitive).

## Event Broadcasting

### ActorEvents (from @repo/common)
- `ActorEvents.ItemCreated`: When a record is created
- `ActorEvents.ItemUpdated`: When a record is updated  
- `ActorEvents.ItemDeleted`: When a record is deleted

### PostgresEvents (new)
- `PostgresEvents.ItemCreated`: Database-level create event
- `PostgresEvents.ItemUpdated`: Database-level update event
- `PostgresEvents.ItemDeleted`: Database-level delete event
- `PostgresEvents.CollectionUpdated`: Bulk operation event

## Where Clause Operators

| Operator | Description | Example |
|----------|-------------|---------|
| `eq` | Equal to | `createCondition('status', 'eq', 'active')` |
| `ne` | Not equal to | `createCondition('disabled', 'ne', true)` |
| `gt` | Greater than | `createCondition('age', 'gt', 18)` |
| `gte` | Greater than or equal | `createCondition('createdAt', 'gte', date)` |
| `lt` | Less than | `createCondition('score', 'lt', 100)` |
| `lte` | Less than or equal | `createCondition('updatedAt', 'lte', date)` |
| `like` | Contains (case-sensitive) | `createCondition('name', 'like', 'John')` |
| `ilike` | Contains (case-insensitive) | `createCondition('email', 'ilike', '@gmail.com')` |
| `in` | Value in array | `createCondition('role', 'in', undefined, ['admin', 'user'])` |
| `notIn` | Value not in array | `createCondition('id', 'notIn', undefined, excludeIds)` |
| `isNull` | Field is null/undefined | `createCondition('deletedAt', 'isNull')` |
| `isNotNull` | Field is not null/undefined | `createCondition('email', 'isNotNull')` |
| `between` | Value between two values | `createCondition('age', 'between', undefined, [18, 65])` |

## Usage Examples

### Basic CRUD Operations

```typescript
// Create
const result = await createStateRecord<User>(c, {
  email: '<EMAIL>',
  displayName: 'John Doe',
  teamId: 'team-123'
}, ActionTypes.manageMembers)

// Update
await updateStateRecord<User>(c, {
  id: 'user-123',
  displayName: 'John Smith'
}, ActionTypes.manageMembers)

// Delete
await deleteStateRecord<User>(c, 'user-123', ActionTypes.manageMembers)

// Get by ID
const user = await getStateRecordById<User>(c, 'user-123', ActionTypes.readmembers)
```

### Dynamic Filtering

```typescript
// Single condition
const whereClause = createWhereClause([
  createCondition('disabled', 'eq', false)
])

// Multiple AND conditions
const whereClause = createWhereClause([
  createCondition('teamId', 'eq', 'team-123'),
  createCondition('emailVerified', 'eq', true),
  createCondition('disabled', 'eq', false)
], 'AND')

// Multiple OR conditions
const whereClause = createWhereClause([
  createCondition('role', 'eq', 'admin'),
  createCondition('role', 'eq', 'moderator')
], 'OR')

const users = await getStateRecordsWithWhere<User>(c, ActionTypes.readmembers, whereClause)
```

### Text Search

```typescript
// Search by display name
const users = await searchStateRecords<User>(
  c,
  'displayName',
  'john',
  ActionTypes.readmembers
)
```

### Complex Queries

```typescript
// Active team admins with verified emails
const whereClause = createWhereClause([
  createCondition('teamId', 'eq', teamId),
  createCondition('disabled', 'eq', false),
  createCondition('emailVerified', 'eq', true),
  createCondition('defaultRole', 'eq', 'team-admin')
], 'AND')

const admins = await getStateRecordsWithWhere<User>(c, ActionTypes.readmembers, whereClause)
```

### Bulk Operations

```typescript
const users = [
  { email: '<EMAIL>', displayName: 'User 1' },
  { email: '<EMAIL>', displayName: 'User 2' }
]

const result = await createMultipleStateRecords<User>(c, users, ActionTypes.manageMembers)
```

## Actor Context Interface

```typescript
interface ActorContext {
  state: any[]
  broadcast: (event: string, data?: any) => void
  log: {
    info: (message: string) => void
    error: (message: string) => void
    warn: (message: string) => void
  }
  conn: {
    state: {
      authData: {
        userId: string
        teamId: string
        defaultRole: string
        roles: string[]
      }
    }
  }
}
```

## Error Handling

All functions return a consistent result format:

```typescript
{
  success: boolean
  data?: T | T[]
  error?: string
}
```

## Security Features

- **RBAC Integration**: All operations check permissions using the rbac system
- **Auth Context**: Uses connection state for authentication data
- **Type Safety**: Generic types ensure compile-time safety
- **Validation**: Input validation and error handling

## Performance Considerations

- **In-Memory Operations**: All operations work on in-memory state arrays
- **Linq Extensions**: Efficient array operations using linq-extensions
- **Event Broadcasting**: Minimal overhead for event notifications
- **Permission Caching**: RBAC permissions are checked efficiently

## Integration with Actors

```typescript
export const myActor = actor({
  state: [] as MyType[],
  actions: {
    create: async (c, item) => {
      return await createStateRecord<MyType>(c, item, ActionTypes.manageItems)
    },
    
    getFiltered: async (c, filters) => {
      const whereClause = createWhereClause([
        createCondition('teamId', 'eq', filters.teamId)
      ])
      return await getStateRecordsWithWhere<MyType>(c, ActionTypes.readItems, whereClause)
    }
  }
})
```

## Best Practices

1. **Use Type Safety**: Always specify generic types for better IDE support
2. **Handle Permissions**: Use appropriate RBAC actions for each operation
3. **Log Operations**: The helper automatically logs operations, but add custom logs as needed
4. **Validate Input**: Validate data before passing to state helper functions
5. **Use Where Clauses**: Prefer dynamic where clauses over manual filtering
6. **Batch Operations**: Use bulk functions for multiple records when possible
