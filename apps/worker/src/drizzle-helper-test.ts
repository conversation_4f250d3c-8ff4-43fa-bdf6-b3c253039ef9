/**
 * Test file to verify the dynamic where clause functionality
 * This file demonstrates the API and can be used for testing
 */

import { 
	getRecordsWithWhere, 
	createWhereClause, 
	createCondition,
	type Where<PERSON><PERSON>e 
} from './drizzle-helper'
import type { AuthData } from './server.helpers'

// Mock auth data for testing
const testAuthData: AuthData = {
	userId: 'test-user-123',
	teamId: 'test-team-456',
	defaultRole: 'team-admin',
	roles: ['team-admin']
}

/**
 * Test function to verify basic functionality
 */
export async function testBasicQuery() {
	console.log('Testing basic equality query...')
	
	try {
		const whereClause = createWhereClause([
			createCondition('disabled', 'eq', false)
		])

		const result = await getRecordsWithWhere(
			'user',
			'readUser',
			testAuthData,
			whereClause
		)

		console.log('✅ Basic query successful:', result.success)
		if (result.success) {
			console.log(`Found ${result.data.length} active users`)
		} else {
			console.log('❌ Query failed:', result.error)
		}
	} catch (error) {
		console.error('❌ Test failed:', error)
	}
}

/**
 * Test function to verify complex query with multiple conditions
 */
export async function testComplexQuery() {
	console.log('Testing complex query with multiple conditions...')
	
	try {
		const whereClause = createWhereClause([
			createCondition('disabled', 'eq', false),
			createCondition('emailVerified', 'eq', true),
			createCondition('defaultRole', 'in', undefined, ['team-admin', 'team-member'])
		], 'AND')

		const result = await getRecordsWithWhere(
			'user',
			'readUser',
			testAuthData,
			whereClause
		)

		console.log('✅ Complex query successful:', result.success)
		if (result.success) {
			console.log(`Found ${result.data.length} active, verified users with specified roles`)
		} else {
			console.log('❌ Query failed:', result.error)
		}
	} catch (error) {
		console.error('❌ Test failed:', error)
	}
}

/**
 * Test function to verify text search functionality
 */
export async function testTextSearch() {
	console.log('Testing text search with ILIKE...')
	
	try {
		const whereClause = createWhereClause([
			createCondition('displayName', 'ilike', '%admin%')
		])

		const result = await getRecordsWithWhere(
			'user',
			'readUser',
			testAuthData,
			whereClause
		)

		console.log('✅ Text search successful:', result.success)
		if (result.success) {
			console.log(`Found ${result.data.length} users with 'admin' in display name`)
		} else {
			console.log('❌ Query failed:', result.error)
		}
	} catch (error) {
		console.error('❌ Test failed:', error)
	}
}

/**
 * Test function to verify date range queries
 */
export async function testDateRange() {
	console.log('Testing date range query...')
	
	try {
		const thirtyDaysAgo = new Date()
		thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
		
		const whereClause = createWhereClause([
			createCondition('createdAt', 'gte', thirtyDaysAgo)
		])

		const result = await getRecordsWithWhere(
			'user',
			'readUser',
			testAuthData,
			whereClause
		)

		console.log('✅ Date range query successful:', result.success)
		if (result.success) {
			console.log(`Found ${result.data.length} users created in the last 30 days`)
		} else {
			console.log('❌ Query failed:', result.error)
		}
	} catch (error) {
		console.error('❌ Test failed:', error)
	}
}

/**
 * Test function to verify NULL checks
 */
export async function testNullChecks() {
	console.log('Testing NULL checks...')
	
	try {
		const whereClause = createWhereClause([
			createCondition('email', 'isNotNull')
		])

		const result = await getRecordsWithWhere(
			'user',
			'readUser',
			testAuthData,
			whereClause
		)

		console.log('✅ NULL check query successful:', result.success)
		if (result.success) {
			console.log(`Found ${result.data.length} users with email addresses`)
		} else {
			console.log('❌ Query failed:', result.error)
		}
	} catch (error) {
		console.error('❌ Test failed:', error)
	}
}

/**
 * Test function to verify OR logic
 */
export async function testOrLogic() {
	console.log('Testing OR logic...')
	
	try {
		const whereClause = createWhereClause([
			createCondition('emailVerified', 'eq', true),
			createCondition('phoneNumberVerified', 'eq', true)
		], 'OR')

		const result = await getRecordsWithWhere(
			'user',
			'readUser',
			testAuthData,
			whereClause
		)

		console.log('✅ OR logic query successful:', result.success)
		if (result.success) {
			console.log(`Found ${result.data.length} users with either email or phone verified`)
		} else {
			console.log('❌ Query failed:', result.error)
		}
	} catch (error) {
		console.error('❌ Test failed:', error)
	}
}

/**
 * Run all tests
 */
export async function runAllTests() {
	console.log('🚀 Starting drizzle-helper dynamic where clause tests...\n')
	
	await testBasicQuery()
	console.log('')
	
	await testComplexQuery()
	console.log('')
	
	await testTextSearch()
	console.log('')
	
	await testDateRange()
	console.log('')
	
	await testNullChecks()
	console.log('')
	
	await testOrLogic()
	console.log('')
	
	console.log('✅ All tests completed!')
}

// Export individual test functions for selective testing
export {
	testBasicQuery,
	testComplexQuery,
	testTextSearch,
	testDateRange,
	testNullChecks,
	testOrLogic
}
