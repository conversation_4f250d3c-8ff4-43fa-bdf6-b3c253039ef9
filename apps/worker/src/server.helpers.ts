import { TimeSpan } from '@repo/common'
import { safeParse, type BaseSchema } from 'valibot'
import { parse } from 'superjson'

export const getSessionExpiryTimeSpan = () => {
	return new TimeSpan(2, 'd')
}

export const safeJsonParse = (text: string) => {
	try {
		return parse(text)
	} catch (e) {
		return text
	}
}

export type PasslockPrincipal = {
	readonly givenName?: string
	readonly familyName?: string
	readonly email?: string
	readonly emailVerified?: boolean
	readonly user?: {
		readonly id: string
		readonly email: string
		readonly givenName: string
		readonly familyName: string
		readonly emailVerified: boolean
	}
	readonly iss: string
	readonly aud: string
	readonly sub: string
	readonly iat: Date
	readonly nbf: Date
	readonly exp: Date
	readonly jti: string
	readonly token: string
	readonly userVerified: boolean
	readonly authType: 'email' | 'apple' | 'google' | 'passkey'
	readonly authId: string
	readonly authStatement: {
		readonly userVerified: boolean
		readonly authType: 'email' | 'apple' | 'google' | 'passkey'
		readonly authTimestamp: Date
	}
	readonly expireAt: Date
}

export const validateParams = <T extends BaseSchema<unknown, unknown, any>>(
	schema: T,
	data: unknown
) => {
	const result = safeParse(schema, data)
	if (result.success) {
		return { isValid: true, output: result.output }
	}
	return { isValid: false, issues: result.issues }
}
export interface ResponseObject {
	status: 'error' | 'success' | 'warning'
	text: string
	data?: any
}

export interface AuthData {
	userID: string
	loginMethod: 'email' | 'apple' | 'google' | 'passkey'
	admin: boolean
	iat: number
	email: string
	scopes: string[]
	userId: string
	defaultRole: string
	allowedRoles: string[]
	teamId: string
	teamLocationId: string
}

export type ConnState = {
	authData: AuthData
}

