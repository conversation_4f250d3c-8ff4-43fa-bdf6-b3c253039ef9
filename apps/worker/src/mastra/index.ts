import { <PERSON><PERSON> } from '@mastra/core/mastra'
import { weatherWorkflow } from './workflows'
import { weatherAgent } from './agents'
import { logger, storage } from './utils'



export const mastra = new Mastra({
  workflows: { weatherWorkflow },
  agents: { weatherAgent },
  logger,
  storage,
  server: {
    apiRoutes: [
      {
        path: '/',
        method: 'GET',
        handler: async (c) => {
          // Access the mastra instance
          const mastra = c.get('mastra')

          // Use mastra instance to access agents, workflows, etc.
          const agent = await mastra.getAgent('my-agent')

          return c.json({ message: 'Hello, world!' })
        }
      }
    ],
    middleware: [
      async (c, next) => {
        c.set('mastra', mastra)
        await next()
      }
    ]
  }
})
