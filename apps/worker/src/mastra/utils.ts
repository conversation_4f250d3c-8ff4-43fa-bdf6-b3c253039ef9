import { Memory } from '@mastra/memory'
import { PgVector, PostgresStore } from '@mastra/pg'
import { PinoLogger } from '@mastra/loggers'
import { fastembed } from '@mastra/fastembed'

export const logger = new PinoLogger({
	name: 'mastra',
	level: 'debug'
})
export const storage = new PostgresStore({
	connectionString: process.env.DATABASE_URL as string
})
export const vector = new PgVector({
	connectionString: process.env.DATABASE_URL as string
})

export const memory = new Memory({
	storage,
	vector,
	embedder: fastembed,
	options: {
		semanticRecall: true,
		// Number of recent messages to include
		lastMessages: 20,

		// Working memory configuration
		workingMemory: {
			enabled: true
		},

		// Thread options
		threads: {
			generateTitle: true
		}
	}
})
