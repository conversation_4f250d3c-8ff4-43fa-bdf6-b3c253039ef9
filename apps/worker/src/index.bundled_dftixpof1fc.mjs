// src/index.ts
import { setup } from "actor-core";
import "@dotenvx/dotenvx/config";
import { initSuper<PERSON>son } from "@repo/common";

// src/clients/db.client.ts
import { drizzle } from "drizzle-orm/node-postgres";
import * as schema from "@repo/drizzle";
var db = drizzle(process.env.DATABASE_URL, {
  casing: "snake_case",
  schema
});

// src/drizzle-helper.ts
import { eq } from "drizzle-orm";
import { rbac } from "@repo/common";
function extendDrizzleTable(db2) {
  const tablePrototype = Object.getPrototypeOf(db2.query.user || db2.query[Object.keys(db2.query)[0]]);
  if (!tablePrototype) {
    console.error("Could not find table prototype to extend");
    return;
  }
  Object.defineProperties(tablePrototype, {
    create: {
      value: async function(data, authData, actorState) {
        const result = await createRecord(db2, this, data, authData);
        if (actorState && Array.isArray(actorState)) {
          actorState.push(result);
        } else if (actorState && typeof actorState === "object") {
          const tableName = getTableName(this);
          const collectionName = `${tableName}s`;
          if (Array.isArray(actorState[collectionName])) {
            actorState[collectionName].push(result);
          }
        }
        return result;
      },
      configurable: true
    },
    update: {
      value: async function(id, data, authData, actorState) {
        const result = await updateRecord(db2, this, id, data, authData);
        if (result && actorState) {
          if (Array.isArray(actorState)) {
            const index = actorState.findIndex((item) => item.id === id);
            if (index !== -1) {
              actorState[index] = { ...actorState[index], ...result };
            }
          } else if (typeof actorState === "object") {
            const tableName = getTableName(this);
            const collectionName = `${tableName}s`;
            if (Array.isArray(actorState[collectionName])) {
              const index = actorState[collectionName].findIndex(
                (item) => item.id === id
              );
              if (index !== -1) {
                actorState[collectionName][index] = {
                  ...actorState[collectionName][index],
                  ...result
                };
              }
            }
          }
        }
        return result;
      },
      configurable: true
    },
    delete: {
      value: async function(id, authData, actorState) {
        const result = await deleteRecord(db2, this, id, authData);
        if (result && actorState) {
          if (Array.isArray(actorState)) {
            const index = actorState.findIndex((item) => item.id === id);
            if (index !== -1) {
              actorState.splice(index, 1);
            }
          } else if (typeof actorState === "object") {
            const tableName = getTableName(this);
            const collectionName = `${tableName}s`;
            if (Array.isArray(actorState[collectionName])) {
              const index = actorState[collectionName].findIndex(
                (item) => item.id === id
              );
              if (index !== -1) {
                actorState[collectionName].splice(index, 1);
              }
            }
          }
        }
        return result;
      },
      configurable: true
    },
    findById: {
      value: async function(id, authData, primaryKey = "id") {
        return await findRecordById(db2, this, id, authData, primaryKey);
      },
      configurable: true
    },
    findByParams: {
      value: async function(params, authData) {
        return await findRecordsByParams(db2, this, params, authData);
      },
      configurable: true
    },
    findOneByParams: {
      value: async function(params, authData) {
        return await findOneByParams(db2, this, params, authData);
      },
      configurable: true
    }
  });
}
function getTableName(table) {
  const tableName = table._.name.split(".").pop() || "";
  return tableName.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}
async function checkPermission(action, payload, authData) {
  try {
    const params = {
      payload,
      authData
    };
    const role2 = authData.defaultRole;
    const hasPermission = await rbac.can(role2, action, params);
    return hasPermission;
  } catch (error) {
    console.error("Permission check failed:", error);
    return false;
  }
}
async function createRecord(db2, table, data, authData) {
  if (authData) {
    const tableName = getTableName(table);
    const rbacAction = `create${tableName.charAt(0).toUpperCase() + tableName.slice(1)}`;
    const hasPermission = await checkPermission(rbacAction, data, authData);
    if (!hasPermission) {
      throw new Error(`Permission denied: User does not have ${rbacAction} permission`);
    }
  }
  const result = await db2.insert(table).values(data).returning().execute();
  return Array.isArray(result) && result.length > 0 ? result[0] : result;
}
async function updateRecord(db2, table, id, data, authData, primaryKey = "id") {
  const existingRecord = await db2.select().from(table).where(eq(table[primaryKey], id)).execute();
  if (!existingRecord || existingRecord.length === 0) {
    return null;
  }
  if (authData) {
    const tableName = getTableName(table);
    const rbacAction = `update${tableName.charAt(0).toUpperCase() + tableName.slice(1)}`;
    const hasPermission = await checkPermission(
      rbacAction,
      { ...existingRecord[0], ...data },
      authData
    );
    if (!hasPermission) {
      throw new Error(`Permission denied: User does not have ${rbacAction} permission`);
    }
  }
  const result = await db2.update(table).set(data).where(eq(table[primaryKey], id)).returning().execute();
  return Array.isArray(result) && result.length > 0 ? result[0] : result;
}
async function deleteRecord(db2, table, id, authData, primaryKey = "id") {
  const recordToDelete = await db2.select().from(table).where(eq(table[primaryKey], id)).execute();
  if (!recordToDelete || recordToDelete.length === 0) {
    return null;
  }
  if (authData) {
    const tableName = getTableName(table);
    const rbacAction = `delete${tableName.charAt(0).toUpperCase() + tableName.slice(1)}`;
    const hasPermission = await checkPermission(rbacAction, recordToDelete[0], authData);
    if (!hasPermission) {
      throw new Error(`Permission denied: User does not have ${rbacAction} permission`);
    }
  }
  const result = await db2.delete(table).where(eq(table[primaryKey], id)).returning().execute();
  return Array.isArray(result) && result.length > 0 ? result[0] : result;
}
async function findRecordById(db2, table, id, authData, primaryKey = "id") {
  const record = await db2.select().from(table).where(eq(table[primaryKey], id)).execute();
  if (!record || record.length === 0) {
    return null;
  }
  if (authData) {
    const tableName = getTableName(table);
    const rbacAction = `read${tableName.charAt(0).toUpperCase() + tableName.slice(1)}`;
    const hasPermission = await checkPermission(rbacAction, record[0], authData);
    if (!hasPermission) {
      throw new Error(`Permission denied: User does not have ${rbacAction} permission`);
    }
  }
  return record[0];
}
async function findRecordsByParams(db2, table, params, authData) {
  let query = db2.select().from(table);
  if (params) {
    query = query.where(params);
  }
  const records = await query.execute();
  if (authData) {
    const tableName = getTableName(table);
    const rbacAction = `read${tableName.charAt(0).toUpperCase() + tableName.slice(1)}`;
    const authorizedRecords = [];
    for (const record of records) {
      const hasPermission = await checkPermission(rbacAction, record, authData);
      if (hasPermission) {
        authorizedRecords.push(record);
      }
    }
    return authorizedRecords;
  }
  return records;
}
async function findOneByParams(db2, table, params, authData) {
  const records = await findRecordsByParams(db2, table, params, authData);
  return records.length > 0 ? records[0] : null;
}

// src/index.ts
import "linq-extensions";

// src/actors/appointment-type-actor.ts
import { actor } from "actor-core";

// src/mastra/index.ts
import { Mastra } from "@mastra/core/mastra";

// src/mastra/workflows/index.ts
import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core/agent";
import { createStep, createWorkflow } from "@mastra/core";
import { z } from "zod";
var llm = openai("gpt-4o");
var agent = new Agent({
  name: "Weather Agent",
  model: llm,
  instructions: `
        You are a local activities and travel expert who excels at weather-based planning. Analyze the weather data and provide practical activity recommendations.

        For each day in the forecast, structure your response exactly as follows:

        \u{1F4C5} [Day, Month Date, Year]
        \u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550

        \u{1F321}\uFE0F WEATHER SUMMARY
        \u2022 Conditions: [brief description]
        \u2022 Temperature: [X\xB0C/Y\xB0F to A\xB0C/B\xB0F]
        \u2022 Precipitation: [X% chance]

        \u{1F305} MORNING ACTIVITIES
        Outdoor:
        \u2022 [Activity Name] - [Brief description including specific location/route]
          Best timing: [specific time range]
          Note: [relevant weather consideration]

        \u{1F31E} AFTERNOON ACTIVITIES
        Outdoor:
        \u2022 [Activity Name] - [Brief description including specific location/route]
          Best timing: [specific time range]
          Note: [relevant weather consideration]

        \u{1F3E0} INDOOR ALTERNATIVES
        \u2022 [Activity Name] - [Brief description including specific venue]
          Ideal for: [weather condition that would trigger this alternative]

        \u26A0\uFE0F SPECIAL CONSIDERATIONS
        \u2022 [Any relevant weather warnings, UV index, wind conditions, etc.]

        Guidelines:
        - Suggest 2-3 time-specific outdoor activities per day
        - Include 1-2 indoor backup options
        - For precipitation >50%, lead with indoor activities
        - All activities must be specific to the location
        - Include specific venues, trails, or locations
        - Consider activity intensity based on temperature
        - Keep descriptions concise but informative

        Maintain this exact formatting for consistency, using the emoji and section headers as shown.
      `
});
var forecastSchema = z.array(
  z.object({
    date: z.string(),
    maxTemp: z.number(),
    minTemp: z.number(),
    precipitationChance: z.number(),
    condition: z.string(),
    location: z.string()
  })
);
var fetchWeather = createStep({
  id: "fetch-weather",
  description: "Fetches weather forecast for a given city",
  inputSchema: z.object({
    city: z.string().describe("The city to get the weather for")
  }),
  outputSchema: forecastSchema,
  execute: async ({ inputData }) => {
    const triggerData = inputData;
    if (!triggerData) {
      throw new Error("Trigger data not found");
    }
    const geocodingUrl = `https://geocoding-api.open-meteo.com/v1/search?name=${encodeURIComponent(triggerData.city)}&count=1`;
    const geocodingResponse = await fetch(geocodingUrl);
    const geocodingData = await geocodingResponse.json();
    if (!geocodingData.results?.[0]) {
      throw new Error(`Location '${triggerData.city}' not found`);
    }
    const { latitude, longitude, name } = geocodingData.results[0];
    const weatherUrl = `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&daily=temperature_2m_max,temperature_2m_min,precipitation_probability_mean,weathercode&timezone=auto`;
    const response = await fetch(weatherUrl);
    const data = await response.json();
    const forecast = data.daily.time.map((date, index) => ({
      date,
      maxTemp: data.daily.temperature_2m_max[index],
      minTemp: data.daily.temperature_2m_min[index],
      precipitationChance: data.daily.precipitation_probability_mean[index],
      condition: getWeatherCondition(data.daily.weathercode[index]),
      location: name
    }));
    return forecast;
  }
});
var planActivities = createStep({
  id: "plan-activities",
  description: "Suggests activities based on weather conditions",
  inputSchema: {},
  outputSchema: z.object({
    activities: z.string()
  }),
  execute: async ({ inputData, mastra: mastra2 }) => {
    const forecast = inputData;
    if (!forecast || forecast.length === 0) {
      throw new Error("Forecast data not found");
    }
    const prompt = `Based on the following weather forecast for ${forecast[0]?.location}, suggest appropriate activities:
      ${JSON.stringify(forecast, null, 2)}
      `;
    const response = await agent.stream([
      {
        role: "user",
        content: prompt
      }
    ]);
    let activitiesText = "";
    for await (const chunk of response.textStream) {
      process.stdout.write(chunk);
      activitiesText += chunk;
    }
    return {
      activities: activitiesText
    };
  }
});
function getWeatherCondition(code) {
  const conditions = {
    0: "Clear sky",
    1: "Mainly clear",
    2: "Partly cloudy",
    3: "Overcast",
    45: "Foggy",
    48: "Depositing rime fog",
    51: "Light drizzle",
    53: "Moderate drizzle",
    55: "Dense drizzle",
    61: "Slight rain",
    63: "Moderate rain",
    65: "Heavy rain",
    71: "Slight snow fall",
    73: "Moderate snow fall",
    75: "Heavy snow fall",
    95: "Thunderstorm"
  };
  return conditions[code] || "Unknown";
}
var weatherWorkflow = createWorkflow({
  id: "weather-workflow",
  inputSchema: z.object({
    city: z.string().describe("The city to get the weather for")
  }),
  outputSchema: z.object({
    activities: z.string()
  }),
  steps: [fetchWeather]
}).then(planActivities).commit();

// src/mastra/agents/index.ts
import { openai as openai2 } from "@ai-sdk/openai";
import { Agent as Agent2 } from "@mastra/core/agent";

// src/mastra/tools/index.ts
import { createTool } from "@mastra/core/tools";
import { z as z2 } from "zod";
var weatherTool = createTool({
  id: "get-weather",
  description: "Get current weather for a location",
  inputSchema: z2.object({
    location: z2.string().describe("City name")
  }),
  outputSchema: z2.object({
    temperature: z2.number(),
    feelsLike: z2.number(),
    humidity: z2.number(),
    windSpeed: z2.number(),
    windGust: z2.number(),
    conditions: z2.string(),
    location: z2.string()
  }),
  execute: async ({ context }) => {
    return await getWeather(context.location);
  }
});
var getWeather = async (location) => {
  const geocodingUrl = `https://geocoding-api.open-meteo.com/v1/search?name=${encodeURIComponent(location)}&count=1`;
  const geocodingResponse = await fetch(geocodingUrl);
  const geocodingData = await geocodingResponse.json();
  if (!geocodingData.results?.[0]) {
    throw new Error(`Location '${location}' not found`);
  }
  const { latitude, longitude, name } = geocodingData.results[0];
  const weatherUrl = `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&current=temperature_2m,apparent_temperature,relative_humidity_2m,wind_speed_10m,wind_gusts_10m,weather_code`;
  const response = await fetch(weatherUrl);
  const data = await response.json();
  return {
    temperature: data.current.temperature_2m,
    feelsLike: data.current.apparent_temperature,
    humidity: data.current.relative_humidity_2m,
    windSpeed: data.current.wind_speed_10m,
    windGust: data.current.wind_gusts_10m,
    conditions: getWeatherCondition2(data.current.weather_code),
    location: name
  };
};
function getWeatherCondition2(code) {
  const conditions = {
    0: "Clear sky",
    1: "Mainly clear",
    2: "Partly cloudy",
    3: "Overcast",
    45: "Foggy",
    48: "Depositing rime fog",
    51: "Light drizzle",
    53: "Moderate drizzle",
    55: "Dense drizzle",
    56: "Light freezing drizzle",
    57: "Dense freezing drizzle",
    61: "Slight rain",
    63: "Moderate rain",
    65: "Heavy rain",
    66: "Light freezing rain",
    67: "Heavy freezing rain",
    71: "Slight snow fall",
    73: "Moderate snow fall",
    75: "Heavy snow fall",
    77: "Snow grains",
    80: "Slight rain showers",
    81: "Moderate rain showers",
    82: "Violent rain showers",
    85: "Slight snow showers",
    86: "Heavy snow showers",
    95: "Thunderstorm",
    96: "Thunderstorm with slight hail",
    99: "Thunderstorm with heavy hail"
  };
  return conditions[code] || "Unknown";
}

// src/mastra/utils.ts
import { Memory } from "@mastra/memory";
import { PgVector, PostgresStore } from "@mastra/pg";
import { PinoLogger } from "@mastra/loggers";
import { fastembed } from "@mastra/fastembed";
var logger = new PinoLogger({
  name: "mastra",
  level: "debug"
});
var storage = new PostgresStore({
  connectionString: process.env.DATABASE_URL
});
var vector = new PgVector({
  connectionString: process.env.DATABASE_URL
});
var memory = new Memory({
  storage,
  vector,
  embedder: fastembed,
  options: {
    semanticRecall: true,
    // Number of recent messages to include
    lastMessages: 20,
    // Working memory configuration
    workingMemory: {
      enabled: true
    },
    // Thread options
    threads: {
      generateTitle: true
    }
  }
});

// src/mastra/agents/index.ts
var weatherAgent = new Agent2({
  name: "Weather Agent",
  instructions: `
      You are a helpful weather assistant that provides accurate weather information.

      Your primary function is to help users get weather details for specific locations. When responding:
      - Always ask for a location if none is provided
      - If the location name isn\u2019t in English, please translate it
      - If giving a location with multiple parts (e.g. "New York, NY"), use the most relevant part (e.g. "New York")
      - Include relevant details like humidity, wind conditions, and precipitation
      - Keep responses concise but informative

      Use the weatherTool to fetch current weather data.
`,
  model: openai2("gpt-4o"),
  tools: { weatherTool },
  memory
});

// src/mastra/index.ts
var mastra = new Mastra({
  workflows: { weatherWorkflow },
  agents: { weatherAgent },
  logger,
  storage,
  server: {
    apiRoutes: [
      {
        path: "/",
        method: "GET",
        handler: async (c) => {
          const mastra2 = c.get("mastra");
          const agent2 = await mastra2.getAgent("my-agent");
          return c.json({ message: "Hello, world!" });
        }
      }
    ],
    middleware: [
      async (c, next) => {
        c.set("mastra", mastra);
        await next();
      }
    ]
  }
});

// src/services/auth.services.ts
import { parse, stringify } from "superjson";
import { jwtVerify, SignJWT } from "jose";

// src/services/db.services.ts
import {
  teamLocation,
  team,
  user,
  member
} from "@repo/drizzle";
import { and, count, eq as eq2 } from "drizzle-orm";
import "linq-extensions";
var userWithEmailExists = async (email) => {
  const userCount = await db.select({ count: count() }).from(user).where(eq2(user.email, email));
  return userCount.first().count > 0;
};
var createTeamAdminUser = async ({
  userObject,
  teamObject,
  teamLocationObject,
  memberObject
}) => {
  const resp = await db.transaction(async (tx) => {
    const userInsert = await tx.insert(user).values(userObject).returning({ id: user.id });
    const teamInsert = await tx.insert(team).values(teamObject).returning({ id: team.id });
    const memberInsert = await tx.insert(member).values(memberObject).returning({ id: member.id });
    const tmInsert = await tx.insert(teamLocation).values(teamLocationObject).returning({ id: teamLocation.id });
    return !!userInsert.firstOrNull()?.id && !!teamInsert.firstOrNull()?.id && !!tmInsert.firstOrNull()?.id && !!memberInsert.firstOrNull()?.id;
  });
  console.log("team admin user ", resp);
  return resp;
};
var markUserEmailAsVerified = async (authKey, email) => {
  console.log("auth key", authKey, "email", email);
  const resp = await db.update(user).set({ emailVerified: true }).where(and(eq2(user.authKey, authKey), eq2(user.email, email))).returning();
  return resp.first();
};
var getCurrentUserData = async (sub, email) => {
  console.log("auth key", sub, "email", email);
  const currentUserData = await db.query.user.findFirst({
    where: and(eq2(user.authKey, sub), eq2(user.email, email)),
    with: {
      member: true
    }
  });
  return currentUserData;
};

// src/services/auth.services.ts
import { addDays } from "date-fns";
import { Passlock } from "@passlock/node";
import { isWithinExpirationDate, Roles, tryCatch } from "@repo/common";
var { AUTH_SECRET: authSecret, PUBLIC_PASSLOCK_API_KEY: passlockApiKey, PUBLIC_PASSLOCK_TENANCY_ID: passlockTenancyId } = process.env;
var setupAuthSession = async (jwtToken, user3, expires) => {
  if (!jwtToken || !user3) {
    throw new Error("No User nor token data");
  }
  const teamId = user3.teamId;
  const isTeamAdmin = user3.defaultRole === Roles.TeamAdmin;
  const teamConfigIsSetup = !!teamId;
  const sessionData = {
    jwtToken,
    user: user3,
    isAdmin: user3.defaultRole === Roles.Admin,
    teamConfigIsSetup,
    isTeamAdmin,
    expires
  };
  return sessionData;
};
var isAuthenticated = async (accessToken) => {
  if (!accessToken) {
    return { isValid: false, sessionData: null };
  }
  const result = await tryCatch(verifyJWT(accessToken));
  if (result.error) {
    console.error(`token verification failed, logging out : ${result.error}`);
    return { isValid: false, sessionData: null };
  }
  const { payload } = result.data;
  try {
    const tokenExpirationTime = payload?.exp;
    const isWithinExpiration = isWithinExpirationDate(new Date(tokenExpirationTime));
    const accessTokenExpired = !!accessToken && !!tokenExpirationTime && isWithinExpiration;
    if (accessTokenExpired) return { isValid: false, sessionData: null };
    const { userId, email } = payload;
    const userResp = await tryCatch(getAppUser(userId, email));
    if (userResp.error) return { isValid: false, sessionData: null };
    const user3 = userResp.data;
    if (!user3) return { isValid: false, sessionData: null };
    const sessionData = await setupAuthSession(accessToken, user3, new Date(tokenExpirationTime));
    return { isValid: true, sessionData };
  } catch (err) {
    console.error(`token expiration check error: ${stringify(err)}`);
    return { isValid: false, sessionData: null };
  }
};
var verifyJWT = async (token) => {
  try {
    const encoder = new TextEncoder();
    const secretKeyBytes = encoder.encode(authSecret);
    const { payload, protectedHeader } = await jwtVerify(token, secretKeyBytes, {
      algorithms: ["HS256"]
      // Restrict to HS256 algorithm
    });
    return { payload, protectedHeader };
  } catch (error) {
    if (error.toString()?.includes("JWTExpired")) {
      throw new Error("Token has expired");
    }
    console.error(`JWT verification failed: ${error}`);
    throw new Error(error.message);
  }
};
var createSessionToken = async (principal) => {
  const { data: currentUserData, error: currentUserError } = await tryCatch(
    getCurrentUserData(principal.sub, principal.email)
  );
  if (currentUserError) {
    throw new Error(currentUserError.message);
  }
  if (!currentUserData) {
    throw new Error("User not found");
  }
  const { member: member3, ...user3 } = currentUserData;
  if (!user3 || !member3) {
    throw new Error("User or member not found");
  }
  const currentUser = {
    id: user3.id,
    email: user3.email,
    familyName: user3.familyName,
    givenName: user3.givenName,
    defaultRole: user3.defaultRole,
    phoneNumber: user3.phoneNumber ?? void 0,
    idDocumentNumber: user3.idDocumentNumber ?? void 0,
    allowedRoles: parse(user3.roles ?? "[]") ?? [],
    gender: user3.gender,
    profilePhotoUrl: user3.avatarUrl ?? void 0,
    isEnabled: !user3.disabled,
    status: member3?.memberStatus,
    teamId: member3?.teamId,
    teamLocationId: member3?.teamLocationId,
    isPhoneNumberVerified: user3.phoneNumberVerified,
    isUserEmailVerified: user3.emailVerified
  };
  const scopes = ["openid", "email", "profile", "offline_access"];
  const customClaims = {
    loginMethod: principal.authType,
    admin: currentUser.defaultRole === Roles.Admin,
    iat: Math.floor(Date.now() / 1e3),
    scopes,
    email: currentUser.email,
    userId: currentUser.id,
    defaultRole: currentUser.defaultRole,
    allowedRoles: currentUser.allowedRoles,
    teamId: currentUser.teamId,
    teamLocationId: currentUser.teamLocationId
  };
  console.info(`zero auth secret: ${authSecret?.toString()}`);
  const encodedKey = new TextEncoder().encode(authSecret);
  console.info(`encoded key: ${encodedKey}`);
  const newJwt = await new SignJWT(customClaims).setProtectedHeader({ alg: "HS256" }).setIssuedAt().setIssuer("https://spendeed.com").setAudience("https://spendeed.com").setExpirationTime("7d").setSubject(currentUser.id).sign(encodedKey);
  const tokenExpiration = addDays(/* @__PURE__ */ new Date(), 1);
  return { jwtToken: newJwt, user: currentUser, expiration: tokenExpiration };
};
var getAppUser = async (sub, email) => {
  const { data: currentUserData, error: currentUserError } = await tryCatch(
    getCurrentUserData(sub, email)
  );
  if (currentUserError) {
    throw new Error(currentUserError.message);
  }
  if (!currentUserData) {
    throw new Error("User not found");
  }
  const { member: member3, ...user3 } = currentUserData;
  if (!user3 || !member3) {
    throw new Error("User or member not found");
  }
  const currentUser = {
    id: user3.id,
    email: user3.email,
    familyName: user3.familyName,
    givenName: user3.givenName,
    defaultRole: user3.defaultRole,
    phoneNumber: user3.phoneNumber ?? void 0,
    idDocumentNumber: user3.idDocumentNumber ?? void 0,
    allowedRoles: parse(user3.roles ?? "[]") ?? [],
    gender: user3.gender,
    profilePhotoUrl: user3.avatarUrl ?? void 0,
    isEnabled: !user3.disabled,
    status: member3?.memberStatus,
    teamId: member3?.teamId,
    teamLocationId: member3?.teamLocationId,
    isPhoneNumberVerified: user3.phoneNumberVerified,
    isUserEmailVerified: user3.emailVerified
  };
  return currentUser;
};
var passlock = new Passlock({
  tenancyId: passlockTenancyId || "",
  apiKey: passlockApiKey || ""
});

// src/actors/middleware.ts
import { tryCatch as tryCatch2 } from "@repo/common";
var checkConnState = async (c, params) => {
  try {
    const apiKey = params?.["spendeedApiKey"];
    const jwt = params?.["jwtToken"];
    let authData = {};
    if (apiKey) {
      if (apiKey !== process.env.SPENDEEED_API_KEY) {
        throw new Error("Invalid API key");
      }
      authData = {
        ...authData,
        userID: "spendeed-api-key",
        email: "<EMAIL>"
      };
      return { authData };
    }
    if (!jwt) {
      throw new Error("No JWT token provided");
    }
    const result = await tryCatch2(isAuthenticated(jwt));
    if (result.error) {
      throw new Error("invalid token", result.error);
    }
    const { isValid, sessionData } = result.data;
    if (!isValid) {
      throw new Error("invalid token");
    }
    authData = {
      ...authData,
      ...sessionData,
      userID: sessionData?.user?.id
    };
    return { authData };
  } catch (error) {
    c.log.error(error);
    throw new Error("Invalid or expired JWT token");
  }
};

// src/actors/appointment-type-actor.ts
import {
  appointmentType
} from "@repo/drizzle";
var appointmentTypesActor = actor({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create appointment type: ${item.appointmentDetail}`);
      const { authData } = c.conn.state;
      const newAppointmentType = await appointmentType.create(
        item,
        authData,
        c.state
      );
      return newAppointmentType;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update appointment type: ${item.appointmentDetail}`);
      return await appointmentType.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete appointment type: ${item.appointmentDetail}`);
      return await appointmentType.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await appointmentType.getCollection(authData, c.state);
    }
  }
});

// src/actors/appointments-actor.ts
import { actor as actor2 } from "actor-core";
import { appointment } from "@repo/drizzle";
var appointmentsActor = actor2({
  state: {
    appointments: []
  },
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    if (!params["spendeedApiKey"]) params["spendeedApiKey"] = process.env.SPENDED_API_KEY;
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      const { authData } = c.conn.state;
      const newAppointment = await appointment.create(
        item,
        authData,
        c.state
      );
      return newAppointment;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      return await appointment.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      return await appointment.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await appointment.getCollection(authData, c.state);
    }
  }
});

// src/server.helpers.ts
import { TimeSpan } from "@repo/common";
import { safeParse } from "valibot";
import { parse as parse2 } from "superjson";
var validateParams = (schema2, data) => {
  const result = safeParse(schema2, data);
  if (result.success) {
    return { isValid: true, output: result.output };
  }
  return { isValid: false, issues: result.issues };
};

// src/actors/auth-actor.ts
import {
  loginFormSchema,
  Roles as Roles2,
  signUpCodeFormSchema,
  signUpFormSchema,
  TeamMemberStatuses,
  TeamPlans,
  tryCatch as tryCatch3
} from "@repo/common";
import { PasslockError } from "@passlock/node";
import { actor as actor3 } from "actor-core";
import { uuidv7 } from "@kripod/uuidv7";
import { image } from "gravatar-gen";
import { parse as parse3, stringify as stringify2 } from "superjson";
var authActor = actor3({
  state: {
    sessionData: void 0
  },
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  actions: {
    signUp: async (c, params) => {
      try {
        const { isValid, output, issues } = validateParams(signUpFormSchema, params);
        const formData = output;
        if (!isValid) {
          return {
            status: "error",
            text: "Invalid parameters",
            issues: issues?.map((issue) => issue.message).join("; ")
          };
        }
        if (!formData?.token) {
          return { status: "error", text: "Token is required" };
        }
        const principal = await passlock.fetchPrincipal({
          token: formData.token
        });
        if (PasslockError.isError(principal)) {
          c.log.error(`principal error: ${principal.message}`);
          return { status: "error", text: principal.message };
        }
        c.log.info(`principal sub: ${principal.sub}`);
        const {
          authType,
          phoneNumber,
          businessName,
          verificationMethod,
          email,
          familyName,
          givenName
        } = formData;
        c.log.info(`type and Code: ${authType}-${verificationMethod}`);
        const teamId = uuidv7();
        const teamLocationId = uuidv7();
        const avatarUrl = await image(email, {
          size: 200,
          defaultImage: "blank",
          includeExtention: true,
          rating: "g"
        });
        const { data, error } = await tryCatch3(userWithEmailExists(email));
        if (error) {
          c.log.error(`existing userId error: ${error}`);
          return {
            status: "error",
            text: `An error occured while signing up. Please try again`
          };
        }
        if (data) {
          return {
            status: "error",
            text: `An account with this email already exists`
          };
        }
        const userId = uuidv7();
        const userObject = {
          id: userId,
          email,
          familyName,
          givenName,
          displayName: `${givenName} ${familyName}`,
          defaultRole: Roles2.TeamAdmin,
          authKey: principal.sub,
          phoneNumber: phoneNumber ?? null,
          emailVerified: principal.emailVerified,
          locale: "en",
          avatarUrl,
          phoneNumberVerified: false,
          roles: stringify2([`${Roles2.TeamAdmin}`, `${Roles2.TeamMember}`, `${Roles2.User}`])
        };
        const teamObject = {
          id: teamId,
          businessEmail: email,
          teamName: businessName,
          phoneNumber,
          isActive: true,
          currentPlan: TeamPlans.Free
        };
        const memberObject = {
          id: uuidv7(),
          teamId,
          userId,
          teamLocationId,
          memberStatus: TeamMemberStatuses.Active,
          createdAt: /* @__PURE__ */ new Date(),
          updatedAt: /* @__PURE__ */ new Date()
        };
        const teamLocationObject = {
          id: teamLocationId,
          teamId,
          createdBy: userId,
          teamLocationAdmin: userId,
          isActive: true,
          locationName: `HQ`,
          isDefault: true,
          locationEmail: email,
          locationPhoneNumber: phoneNumber
        };
        const { data: createTeamAdminData, error: createTeamAdminError } = await tryCatch3(
          createTeamAdminUser({
            userObject,
            teamObject,
            teamLocationObject,
            memberObject
          })
        );
        if (createTeamAdminError || !createTeamAdminData) {
          c.log.error(`create team admin error: ${createTeamAdminError}`);
          return { status: "error", text: "Failed to create user during sign up" };
        }
        switch (`${authType}-${verificationMethod}`) {
          case "passkey-code":
            return {
              status: "success",
              text: "Sign Up email sent. Please check your email for Code",
              data: { authType: { type: "code" }, userId }
            };
          case "passkey-link":
            return {
              status: "success",
              text: "Sign Up email sent. Please check your mail and click on the verification link",
              data: { authType: { type: "link" }, userId }
            };
          default:
            return {
              status: "error",
              text: "Invalid auth type"
            };
        }
      } catch (error) {
        c.log.error(`signup error: ${error}`);
        return {
          status: "error",
          text: `And error occured while registering user: ${error.message}`
        };
      }
    },
    login: async (c, params) => {
      try {
        const {
          isValid,
          output: formData,
          issues
        } = validateParams(loginFormSchema, params);
        if (!isValid || !formData) {
          return {
            status: "error",
            text: "Invalid parameters",
            issues: issues?.map((issue) => issue.message).join("; ")
          };
        }
        const principal = await passlock.fetchPrincipal({
          token: formData.token
        });
        if (PasslockError.isError(principal)) {
          c.log.error(`principal verification error: ${principal.message}`);
          return { status: "error", text: principal.message };
        }
        const sessionTokenRequest = await tryCatch3(createSessionToken(principal));
        if (sessionTokenRequest.error) {
          c.log.error(`session token error: ${sessionTokenRequest.error?.message}`);
          return { status: "error", text: sessionTokenRequest.error?.message };
        }
        const { user: currentUser, expiration, jwtToken } = sessionTokenRequest.data;
        c.log.info("creating auth session");
        const { data: sessionData, error: setupAuthError } = await tryCatch3(
          setupAuthSession(jwtToken, currentUser, expiration)
        );
        if (setupAuthError) {
          c.log.error(`setup session error: ${setupAuthError}`);
          return { status: "error", text: "An error occured.Please try again" };
        }
        c.log.info("logged in");
        c.state.sessionData = sessionData;
        return {
          status: "success",
          text: "Login successful",
          data: { session: sessionData }
        };
      } catch (error) {
        c.log.error(`login error: ${error}`);
        return {
          status: "error",
          text: "An error occured during login. Please try again"
        };
      }
    },
    verifyEmail: async (c, params) => {
      try {
        const {
          isValid,
          output: formData,
          issues
        } = validateParams(signUpCodeFormSchema, params);
        if (!isValid || !formData) {
          return {
            status: "error",
            text: "Invalid parameters",
            issues: issues?.map((issue) => issue.message).join("; ")
          };
        }
        if (!formData.token) {
          return {
            text: "Email Verification Failed",
            status: "error"
          };
        }
        const principal = parse3(formData.principal ?? "{}");
        if (!principal.emailVerified) {
          c.log.error(`principal email verification failed: ${principal}`);
          return {
            text: "Email Verification Failed",
            status: "error"
          };
        }
        const { data, error } = await tryCatch3(
          markUserEmailAsVerified(principal.sub, principal.email)
        );
        if (error || !data?.id) {
          c.log.error(`mark user as verified error: ${error}`);
          return {
            status: "error",
            text: "An error occured during user verification. please try again"
          };
        }
        const sessionResp = await tryCatch3(createSessionToken(principal));
        if (sessionResp.error || !sessionResp.data?.jwtToken) {
          c.log.error(`create session token error: ${sessionResp.error}`);
          return {
            text: "An error occured while verifying email",
            status: "error"
          };
        }
        const { jwtToken, user: currentUser, expiration } = sessionResp.data;
        c.log.info("creating auth session");
        const { data: sessionData, error: authSessionError } = await tryCatch3(
          setupAuthSession(jwtToken, currentUser, expiration)
        );
        if (authSessionError) {
          c.log.error("setup session error", authSessionError);
          return {
            status: "error",
            text: "An error occured during Code Verification.Please try again"
          };
        }
        c.state.sessionData = sessionData;
        return {
          text: "Code verified successfully",
          status: "success",
          data: { session: sessionData }
        };
      } catch (exception) {
        c.log.error("code verification exception", exception);
        return {
          status: "error",
          text: "An error occured during Code Verification.Please try again"
        };
      }
    },
    logOut: async (c) => {
      try {
        c.state.sessionData = void 0;
        return {
          status: "success",
          text: "Logout successful"
        };
      } catch (error) {
        c.log.error(`logout error: ${error}`);
        return {
          status: "error",
          text: "An error occured during logout. Please try again"
        };
      }
    }
  }
});

// src/actors/calendar-blocks-actor.ts
import { actor as actor4 } from "actor-core";
import {
  calendarBlock
} from "@repo/drizzle";
var calendarBlocksActor = actor4({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create calendar block: ${item.id}`);
      const { authData } = c.conn.state;
      const newCalendarBlock = await calendarBlock.create(item, authData, c.state);
      return newCalendarBlock;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update calendar block: ${item.id}`);
      return await calendarBlock.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete calendar block: ${item.id}`);
      return await calendarBlock.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await calendarBlock.getCollection(authData, c.state);
    }
  }
});

// src/actors/gender-list-actor.ts
import { actor as actor5 } from "actor-core";
import { gender } from "@repo/drizzle";
var genderListActor = actor5({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await gender.getCollection(authData, c.state);
    }
  }
});

// src/actors/member-invite-actor.ts
import { actor as actor6 } from "actor-core";
import { memberInvite } from "@repo/drizzle";
var membersInviteActor = actor6({
  state: {},
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create member invite: ${item.inviteeEmail}`);
      const { authData } = c.conn.state;
      const newMemberInvite = await memberInvite.create(item, authData, c.state);
      return newMemberInvite;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update member invite: ${item.inviteeEmail}`);
      return await memberInvite.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete member invite: ${item.inviteeEmail}`);
      return await memberInvite.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await memberInvite.getCollection(authData, c.state);
    }
  }
});

// src/actors/members-actor.ts
import { actor as actor7 } from "actor-core";
import { member as member2 } from "@repo/drizzle";
var membersActor = actor7({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create member: ${item.id}`);
      const { authData } = c.conn.state;
      const newMember = await member2.create(item, authData, c.state);
      return newMember;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update member: ${item.id}`);
      return await member2.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete member: ${item.id}`);
      return await member2.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await member2.getCollection(authData, c.state);
    }
  }
});

// src/actors/roles-actor.ts
import { actor as actor8 } from "actor-core";
import { role } from "@repo/drizzle";
var rolesListActor = actor8({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await role.getCollection(authData, c.state);
    }
  }
});

// src/actors/storage-bucket-actor.ts
import { actor as actor9 } from "actor-core";
import {
  storageBucket
} from "@repo/drizzle";
var storageBucketActor = actor9({
  state: {},
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create storage bucket: ${item.id}`);
      const { authData } = c.conn.state;
      const newStorageBucket = await storageBucket.create(item, authData, c.state);
      return newStorageBucket;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update storage bucket: ${item.id}`);
      return await storageBucket.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete storage bucket: ${item.id}`);
      return await storageBucket.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await storageBucket.getCollection(authData, c.state);
    }
  }
});

// src/actors/storage-files-actor.ts
import { actor as actor10 } from "actor-core";
import { storageFile } from "@repo/drizzle";
var storageFilesActor = actor10({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create storage file: ${item.name}`);
      const { authData } = c.conn.state;
      const newStorageFile = await storageFile.create(item, authData, c.state);
      return newStorageFile;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update storage file: ${item.name}`);
      return await storageFile.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete storage file: ${item.name}`);
      return await storageFile.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await storageFile.getCollection(authData, c.state);
    }
  }
});

// src/actors/team-actor.ts
import { actor as actor11 } from "actor-core";
import { team as team2 } from "@repo/drizzle";
var teamActor = actor11({
  state: {},
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params, request }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create team: ${item.teamName}`);
      const { authData } = c.conn.state;
      const newTeam = await team2.create(item, authData, c.state);
      return newTeam;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update team: ${item.teamName}`);
      return await team2.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete team: ${item.teamName}`);
      return await team2.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      const teamId = authData?.teamId;
      const currentTeam = await team2.findById(teamId, authData);
      return [currentTeam];
    }
  }
});

// src/actors/team-bank-detail-actor.ts
import { actor as actor12 } from "actor-core";
import {
  teamBankDetail
} from "@repo/drizzle";
var teamBankDetailActor = actor12({
  state: {},
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create team bank detail: ${item.bankName}`);
      const { authData } = c.conn.state;
      const newTeamBankDetail = await teamBankDetail.create(item, authData, c.state);
      return newTeamBankDetail;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update team bank detail: ${item.bankName}`);
      return await teamBankDetail.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete team bank detail: ${item.bankName}`);
      return await teamBankDetail.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await teamBankDetail.getCollection(authData, c.state);
    }
  }
});

// src/actors/team-locations-actor.ts
import { actor as actor13 } from "actor-core";
import { teamLocation as teamLocation2 } from "@repo/drizzle";
var teamLocationsActor = actor13({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create team location: ${item.locationName}`);
      const { authData } = c.conn.state;
      const newTeamLocation = await teamLocation2.create(item, authData, c.state);
      return newTeamLocation;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update team location: ${item.locationName}`);
      return await teamLocation2.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete team location: ${item.locationName}`);
      return await teamLocation2.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await teamLocation2.getCollection(authData, c.state);
    }
  }
});

// src/actors/team-payment-provider-actor.ts
import { actor as actor14 } from "actor-core";
import { teamPaymentProvider } from "@repo/drizzle";
var teamPaymentProviderActor = actor14({
  state: {},
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create team payment provider: ${item.providerName}`);
      const { authData } = c.conn.state;
      const newTeamPaymentProvider = await teamPaymentProvider.create(
        item,
        authData,
        c.state
      );
      return newTeamPaymentProvider;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update team payment provider: ${item.providerName}`);
      return await teamPaymentProvider.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete team payment provider: ${item.providerName}`);
      return await teamPaymentProvider.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await teamPaymentProvider.getCollection(authData, c.state);
    }
  }
});

// src/actors/time-zone-actor.ts
import { actor as actor15 } from "actor-core";
import { timeZone } from "@repo/drizzle";
var timeZonesActor = actor15({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await timeZone.getCollection(authData, c.state);
    }
  }
});

// src/actors/user-actor.ts
import { actor as actor16 } from "actor-core";
import { user as user2 } from "@repo/drizzle";
var usersActor = actor16({
  state: [],
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create user: ${item.displayName}`);
      const { authData } = c.conn.state;
      const newUser = await user2.create(item, authData, c.state);
      return newUser;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update user: ${item.displayName}`);
      return await user2.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete user: ${item.displayName}`);
      return await user2.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await user2.getCollection(authData, c.state);
    }
  }
});

// src/actors/user-role-actor.ts
import { actor as actor17 } from "actor-core";
import { userRole } from "@repo/drizzle";
var stateObj = {};
var userRoleActor = actor17({
  state: stateObj,
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create user role: ${item.role}`);
      const { authData } = c.conn.state;
      const newUserRole = await userRole.create(item, authData, c.state);
      return newUserRole;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update user role: ${item.role}`);
      return await userRole.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete user role: ${item.role}`);
      return await userRole.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await userRole.getCollection(authData, c.state);
    }
  }
});

// src/actors/workday-setting-actor.ts
import { actor as actor18 } from "actor-core";
import { workdaySetting } from "@repo/drizzle";
var stateObj2 = {};
var workdaySettingActor = actor18({
  state: stateObj2,
  createVars: (c, driverCtx) => ({
    mastra,
    ctx: driverCtx
  }),
  createConnState: async (c, { params }) => {
    return await checkConnState(c, params);
  },
  actions: {
    create: async (c, item) => {
      c.log.info(`create workday setting: ${item.weekday}`);
      const { authData } = c.conn.state;
      const newWorkdaySetting = await workdaySetting.create(item, authData, c.state);
      return newWorkdaySetting;
    },
    update: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`update workday setting: ${item.weekday}`);
      return await workdaySetting.update(item.id, item, authData, c.state);
    },
    delete: async (c, item) => {
      const { authData } = c.conn.state;
      c.log.info(`delete workday setting: ${item.weekday}`);
      return await workdaySetting.delete(item.id, authData, c.state);
    },
    getCollection: async (c) => {
      const { authData } = c.conn.state;
      return await workdaySetting.getCollection(authData, c.state);
    }
  }
});

// src/index.ts
initSuperJson();
extendDrizzleTable(db);
var app = setup({
  actors: {
    auth: authActor,
    appointmentsActor,
    teamActor,
    calendarBlocksActor,
    membersActor,
    genderListActor,
    appointmentTypesActor,
    storageBucketActor,
    storageFilesActor,
    userRoleActor,
    rolesListActor,
    usersActor,
    teamPaymentProviderActor,
    timeZonesActor,
    workdaySettingActor,
    teamLocationsActor,
    teamBankDetailActor,
    memberInviteActor: membersInviteActor
  },
  cors: { origin: "*" }
  // Configure CORS for your production domains in production
});
export {
  app
};
//# sourceMappingURL=data:application/json;base64,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
