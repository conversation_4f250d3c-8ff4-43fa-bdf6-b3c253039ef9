import {
	Container,
	Head,
	Heading,
	Html,
	render,
	Section,
	Tailwind,
	Text
} from '@react-email/components'

function GetInviteEmail({
	teamName,
	inviteLink,
	expires,
	inviterName
}: {
	teamName: string
	inviteLink: string
	expires: Date
	inviterName: string
}) {
	return (
		<Html>
			<Tailwind>
				<Head />
				<Container className="container px-20 font-sans">
					<Heading className="mb-4 text-xl font-bold">
						{/* TODO: Update with your app name */}
						Invitation to join Team {teamName} on {process.env.APP_NAME}
					</Heading>

					<Section className="text-center">
						<Text className="text-sm">
							{inviterName} has invited you to join {teamName} on SpenDeed.
						</Text>
						<Text className="text-sm">Please click on the link below to join</Text>
						<Text className="font-semibold">Invite Link:</Text>
						<Text className="text-4xl font-bold">{inviteLink}</Text>
						<Text>
							(This code is valid for{' '}
							{Math.floor((+expires - Date.now()) / (60 * 60 * 1000))} hours)
						</Text>
					</Section>
				</Container>
			</Tailwind>
		</Html>
	)
}

export const getInviteEmail = async (
	teamName: string,
	inviteLink: string,
	expires: Date,
	inviterName: string
) => {
	return await render(
		<GetInviteEmail
			teamName={teamName}
			inviteLink={inviteLink}
			expires={expires}
			inviterName={inviterName}
		/>
	)
}
