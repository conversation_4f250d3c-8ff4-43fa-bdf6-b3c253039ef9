import { Container, Head, Heading, Html, render, Section, Tailwind, Text, Button } from '@react-email/components'

function PasswordLinkEmail({ host, magicLink }: { host: string; magicLink: string }) {
	const escapedHost = host.replace(/\./g, '&#8203;.')
	return (
		<Html>
			<Tailwind>
				<Head />
				<Container className="container px-20 font-sans">
					<Heading className="mb-4 text-xl font-bold">
						{/* TODO: Update with your app name */}
						Sign in to {escapedHost}
					</Heading>
					<Text className="text-sm">Please click the link below to sign in to {escapedHost}.</Text>
					<Section className="text-center">
						<Text className="font-semibold">Verification Link</Text>
						<Button href={magicLink} className="text-xl font-bold">
							Sign In
						</Button>
					</Section>
					<Text className="text-sm">
						If you did not request a sign in link, please ignore this email.
					</Text>
				</Container>
			</Tailwind>
		</Html>
	)
}

export const getPasswordLinkEmail = async (host: string, magicLink: string) => {
	return await render(<PasswordLinkEmail host={host} magicLink={magicLink} />)
}
