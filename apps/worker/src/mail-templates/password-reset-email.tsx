import { Container, Head, Heading, Html, render, Section, Tailwind, Text } from '@react-email/components'

function Password_reset_email({ code, expires }: { code: string; expires: Date }) {
	return (
		<Html>
			<Tailwind>
				<Head />
				<Container className="container px-20 font-sans">
					<Heading className="mb-4 text-xl font-bold">
						{/* TODO: Update with your app name */}
						Reset your password in My App
					</Heading>
					<Text className="text-sm">
						Please enter the following code on the password reset page.
					</Text>
					<Section className="text-center">
						<Text className="font-semibold">Verification code</Text>
						<Text className="text-4xl font-bold">{code}</Text>
						<Text>
							(This code is valid for {Math.floor((+expires - Date.now()) / (60 * 60 * 1000))}{' '}
							hours)
						</Text>
					</Section>

				</Container>
			</Tailwind>
		</Html>
	)
}

export const getPasswordResetEmail = async (code: string, expires: Date) => {
	return await render(<Password_reset_email code={code} expires={expires} />)
}
