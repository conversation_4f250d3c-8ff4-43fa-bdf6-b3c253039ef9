
import { parse, stringify } from 'superjson'
import { type JWTPayload, jwtVerify, SignJWT } from 'jose'
import { getCurrentUserData } from './db.services'
import { addDays } from 'date-fns'
import { Passlock } from '@passlock/node'
import { isWithinExpirationDate, Roles, tryCatch, type AppUserInfo, type GenderType, type ISessionData, type JWTClaims, type Role, type TeamMemberStatus } from '@repo/common'
import type { PasslockPrincipal } from '../server.helpers'

const { AUTH_SECRET: authSecret, PUBLIC_PASSLOCK_API_KEY: passlockApiKey, PUBLIC_PASSLOCK_TENANCY_ID: passlockTenancyId } = process.env

export const setupAuthSession = async (
      jwtToken: string | undefined,
      user: AppUserInfo | undefined,
      expires: Date | undefined
) => {
      if (!jwtToken || !user) {
            throw new Error('No User nor token data')
      }
      const teamId = user.teamId
      const isTeamAdmin = user.defaultRole === Roles.TeamAdmin
      const teamConfigIsSetup = !!teamId
      const sessionData: ISessionData = {
            jwtToken,
            user,
            isAdmin: user.defaultRole === Roles.Admin,
            teamConfigIsSetup,
            isTeamAdmin,
            expires
      }

      return sessionData
}

export const isAuthenticated = async (
      accessToken: string
): Promise<{ isValid: boolean; sessionData: ISessionData | null }> => {
      if (!accessToken) {
            return { isValid: false, sessionData: null }
      }

      const result = await tryCatch(verifyJWT(accessToken))
      if (result.error) {
            console.error(`token verification failed, logging out : ${result.error}`)
            return { isValid: false, sessionData: null }
      }
      //console.info(result.data)
      const { payload } = result.data
      try {
            const tokenExpirationTime = payload?.exp
            const isWithinExpiration = isWithinExpirationDate(new Date(tokenExpirationTime!))
            const accessTokenExpired: boolean =
                  !!accessToken && !!tokenExpirationTime && isWithinExpiration
            if (accessTokenExpired) return { isValid: false, sessionData: null }
            const { userId, email } = payload as JWTClaims & JWTPayload
            const userResp = await tryCatch(getAppUser(userId, email))
            if (userResp.error) return { isValid: false, sessionData: null }
            const user = userResp.data
            if (!user) return { isValid: false, sessionData: null }
            const sessionData = await setupAuthSession(accessToken, user, new Date(tokenExpirationTime!))
            return { isValid: true, sessionData }
      } catch (err) {
            console.error(`token expiration check error: ${stringify(err)}`)
            return { isValid: false, sessionData: null }
      }
}

export const verifyJWT = async (token: string) => {
      try {
            const encoder = new TextEncoder()
            const secretKeyBytes = encoder.encode(authSecret)

            // Verify the JWT
            const { payload, protectedHeader } = await jwtVerify<JWTClaims>(token, secretKeyBytes, {
                  algorithms: ['HS256'] // Restrict to HS256 algorithm
            })

            return { payload, protectedHeader }
      } catch (error) {
            if ((error as Error).toString()?.includes('JWTExpired')) {
                  throw new Error('Token has expired')
            }
            console.error(`JWT verification failed: ${error}`)
            throw new Error((error as Error).message)
      }
}

export const createSessionToken = async (
      principal: PasslockPrincipal
): Promise<{ jwtToken: string; user: AppUserInfo; expiration: Date }> => {
      const { data: currentUserData, error: currentUserError } = await tryCatch(
            getCurrentUserData(principal.sub, principal.email!)
      )
      if (currentUserError) {
            throw new Error(currentUserError.message)
      }
      if (!currentUserData) {
            throw new Error('User not found')
      }

      const { member, ...user } = currentUserData
      if (!user || !member) {
            throw new Error('User or member not found')
      }
      const currentUser: AppUserInfo = {
            id: user.id,
            email: user.email!,
            familyName: user.familyName,
            givenName: user.givenName,
            defaultRole: user.defaultRole as Role,
            phoneNumber: user.phoneNumber ?? undefined,
            idDocumentNumber: user.idDocumentNumber ?? undefined,
            allowedRoles: parse(user.roles ?? '[]') ?? [],
            gender: user.gender as GenderType,
            profilePhotoUrl: user.avatarUrl ?? undefined,
            isEnabled: !user.disabled,
            status: member?.memberStatus as TeamMemberStatus,
            teamId: member?.teamId,
            teamLocationId: member?.teamLocationId,
            isPhoneNumberVerified: user.phoneNumberVerified,
            isUserEmailVerified: user.emailVerified
      }

      const scopes = ['openid', 'email', 'profile', 'offline_access']
      const customClaims: JWTClaims = {
            loginMethod: principal.authType,
            admin: currentUser.defaultRole === Roles.Admin,
            iat: Math.floor(Date.now() / 1000),
            scopes,
            email: currentUser.email,
            userId: currentUser.id,
            defaultRole: currentUser.defaultRole!,
            allowedRoles: currentUser.allowedRoles as string[],
            teamId: currentUser.teamId,
            teamLocationId: currentUser.teamLocationId
      }

      console.info(`zero auth secret: ${authSecret?.toString()}`)

      const encodedKey = new TextEncoder().encode(authSecret)
      console.info(`encoded key: ${encodedKey}`)
      const newJwt = await new SignJWT(customClaims)
            .setProtectedHeader({ alg: 'HS256' })
            .setIssuedAt()
            .setIssuer('https://spendeed.com')
            .setAudience('https://spendeed.com')
            .setExpirationTime('7d') //24 Hours
            .setSubject(currentUser.id)
            .sign(encodedKey)
      const tokenExpiration = addDays(new Date(), 1)
      return { jwtToken: newJwt, user: currentUser, expiration: tokenExpiration }
}

export const getAppUser = async (sub: string, email: string) => {
      const { data: currentUserData, error: currentUserError } = await tryCatch(
            getCurrentUserData(sub, email)
      )
      if (currentUserError) {
            throw new Error(currentUserError.message)
      }
      if (!currentUserData) {
            throw new Error('User not found')
      }

      const { member, ...user } = currentUserData
      if (!user || !member) {
            throw new Error('User or member not found')
      }
      const currentUser: AppUserInfo = {
            id: user.id,
            email: user.email!,
            familyName: user.familyName,
            givenName: user.givenName,
            defaultRole: user.defaultRole as Role,
            phoneNumber: user.phoneNumber ?? undefined,
            idDocumentNumber: user.idDocumentNumber ?? undefined,
            allowedRoles: parse(user.roles ?? '[]') ?? [],
            gender: user.gender as GenderType,
            profilePhotoUrl: user.avatarUrl ?? undefined,
            isEnabled: !user.disabled,
            status: member?.memberStatus as TeamMemberStatus,
            teamId: member?.teamId,
            teamLocationId: member?.teamLocationId,
            isPhoneNumberVerified: user.phoneNumberVerified,
            isUserEmailVerified: user.emailVerified
      }
      return currentUser
}

export const passlock = new Passlock({
      tenancyId: passlockTenancyId || '',
      apiKey: passlockApiKey || ''
})