import { db, type TransactionType } from '../clients'
import type {
	TeamInsertDbType,
	TeamLocationInsertDbType,
	UserInsertDbType,
	MemberInsertDbType
} from '@repo/drizzle'
import { user, team, teamLocation, member } from '@repo/drizzle'

import { and, count, eq } from 'drizzle-orm'
import 'linq-extensions'

export const userWithEmailExists = async (email: string) => {
	const userCount = await db
		.select({ count: count() })
		.from(user)
		.where(eq(user.email, email))
	return userCount.first().count > 0
}

export const createTeamAdminUser = async ({
	userObject,
	teamObject,
	teamLocationObject,
	memberObject
}: {
	userObject: UserInsertDbType
	teamObject: TeamInsertDbType
	teamLocationObject: TeamLocationInsertDbType
	memberObject: MemberInsertDbType
}) => {
	const resp = await db.transaction(async (tx: TransactionType) => {
		const userInsert = await tx.insert(user).values(userObject).returning({ id: user.id })
		const teamInsert = await tx.insert(team).values(teamObject).returning({ id: team.id })
		const memberInsert = await tx
			.insert(member)
			.values(memberObject)
			.returning({ id: member.id })
		const tmInsert = await tx
			.insert(teamLocation)
			.values(teamLocationObject)
			.returning({ id: teamLocation.id })

		return (
			!!userInsert.firstOrNull()?.id &&
			!!teamInsert.firstOrNull()?.id &&
			!!tmInsert.firstOrNull()?.id &&
			!!memberInsert.firstOrNull()?.id
		)
	})
	console.log('team admin user ', resp)
	return resp
}

export const markUserEmailAsVerified = async (authKey: string, email: string) => {
	console.log('auth key', authKey, 'email', email)
	const resp = await db
		.update(user)
		.set({ emailVerified: true })
		.where(and(eq(user.authKey, authKey), eq(user.email, email)))
		.returning()
	return resp.first()
}

export const getCurrentUserData = async (sub: string, email: string) => {
	console.log('auth key', sub, 'email', email)
	const currentUserData = await db.query.user.findFirst({
		where: and(eq(user.authKey, sub), eq(user.email, email)),
		with: {
			member: true,
		}
	})
	return currentUserData
}
