{"extends": ["@repo/typescript-config/base.json"], "compilerOptions": {"target": "ESNext", "module": "ES2022", "moduleResolution": "bundler", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "noEmit": true, "verbatimModuleSyntax": true, "isolatedModules": true, "outDir": "dist"}, "include": ["src/**/*"]}